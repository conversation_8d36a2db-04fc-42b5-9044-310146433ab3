> Project Title:
>
> **ExLog: Cybersecurity Log Management System**
>
> Team Members:

+--------------+-------------------+-------------------+--------------+
| > Jarel      | Jordan            | Mahilla           | > <PERSON>ryan      |
+==============+===================+===================+==============+
| 167403211    | 146222203         | 139967194         | > 136235215  |
+--------------+-------------------+-------------------+--------------+

> **Date:** June 4, 2025
>
> **Version:** 1.0
>
> **Status:** Final
>
> **Period Covered:** May 21, 2025 - June 4, 2025
>
> Table of Contents

1.  

2.  1.  
    2.  

3.  1.  
    2.  
    3.  
    4.  
    5.  
    6.  

4.  1.  
    2.  
    3.  
    4.  

5.  1.  
    2.  
    3.  

[Executive Summary 3](#executive-summary)[Sprint Objectives and Scope
3](#sprint-objectives-and-scope)[Planned Objectives
3](#planned-objectives)[Scope Boundaries
4](#scope-boundaries)[Achievements in Detail
4](#achievements-in-detail)[Development Environment Setup
4](#development-environment-setup)[Project Structure Establishment
5](#project-structure-establishment)[Authentication Implementation
6](#authentication-implementation)[Database Schema Creation
7](#database-schema-creation)[Initial API Endpoints Development
7](#initial-api-endpoints-development)[Additional Achievements
8](#additional-achievements)[Technical Implementation Details
9](#technical-implementation-details)[Authentication System
9](#authentication-system)[Database Schema Implementation
10](#database-schema-implementation)[API Documentation Implementation
11](#api-documentation-implementation)[Docker Containerization
12](#docker-containerization)[Challenges and Solutions
14](#challenges-and-solutions)[Challenge 1: Log Persistence Issues
14](#challenge-1-log-persistence-issues)[Challenge 2: Network
Configuration Issues
14](#challenge-2-network-configuration-issues)[Challenge 3: Database
Schema Complexity 15](#challenge-3-database-schema-complexity)[Appendix
A: GANTT Chart 16](#appendix-a-gantt-chart)

## Executive Summary

> Sprint 1-2 marks the successful initiation of the ExLog Cybersecurity
> Log Management System project. Over the two-week period, the team has
> established the foundational architecture and infrastructure necessary
> for building a robust cybersecurity log management solution. Key
> achievements include setting up the development environment,
> establishing the project structure, implementing authentication
> mechanisms, creating database schemas, and developing initial API
> endpoints. The sprint has laid a solid groundwork for the subsequent
> development phases, positioning the project well for the upcoming
> agent development and log collection sprints.

## Sprint Objectives and Scope

### Planned Objectives

> According to the Implementation Plan, Sprint 1-2 was designated as
> \"Project Setup and Foundation\" with the following specific
> objectives:

#### Set up development environment

-   Configure local development environments for all team members

-   Establish version control workflows and branching strategies

-   Set up Docker containerization for consistent environments

-   Configure development, staging, and production environment variables

#### Configure CI/CD pipeline

-   Implement automated testing on code commits

-   Set up build automation for frontend and backend components

-   Configure deployment pipelines for different environments

-   Establish code quality checks and linting

#### Establish project structure

-   Define frontend architecture and component organization

-   Structure backend services and API layers

-   Establish database access patterns and models

-   Create documentation templates and standards

#### Implement basic authentication

-   Develop user registration functionality

-   Create secure login mechanisms with proper password handling

-   Implement JWT-based authentication

-   Set up session management and token refresh mechanisms

-   Configure role-based access control foundations

#### Create database schema

-   Design MongoDB collections for user data and configurations

-   Establish TimescaleDB schema for time-series metrics

-   Configure Elasticsearch mappings for log indexing

-   Set up Redis structures for caching and real-time data

#### Develop initial API endpoints

-   Create user management endpoints (CRUD operations)

-   Implement authentication endpoints (login, logout, refresh)

-   Develop basic log ingestion API

-   Set up health check and monitoring endpoints

-   Create API documentation framework

### Scope Boundaries

> The sprint was specifically focused on establishing the technical
> foundation and infrastructure, with the following explicit scope
> boundaries:

-   **In Scope**: Development environment setup, authentication system,
    database schema design, API foundation, containerization

-   **Out of Scope**: Agent development, advanced log processing,
    alerting mechanisms, dashboard visualizations, reporting features

## Achievements in Detail

### Development Environment Setup

> The team has successfully established a comprehensive development
> environment that ensures consistency across all development, testing,
> and production environments:

-   **Docker Containerization**: Created Docker and Docker Compose
    configurations for all services, including:

    -   Frontend container (React.js with Material-UI)

    -   Backend API container (Node.js/Express)

    -   WebSocket server container for real-time updates

    -   MongoDB container for document storage

    -   TimescaleDB container for time-series data

    -   Elasticsearch container for log indexing and search

    -   Redis container for caching and session management

    -   Nginx container for reverse proxy and load balancing

#### Development Tools Configuration:

-   ESLint and Prettier for code quality and formatting

-   Husky for pre-commit hooks

-   Jest and React Testing Library for frontend testing

-   Mocha and Chai for backend testing

-   Swagger for API documentation

#### Version Control Setup:

-   Git repository with branch protection rules

-   Pull request templates and review processes

-   Commit message conventions

-   Git hooks for automated testing before commits

### Project Structure Establishment

> The project structure has been meticulously organized following best
> practices for a microservice architecture:

#### Frontend Structure:

-   Component-based architecture with reusable UI elements

-   Redux for state management with proper action and reducer
    organization

-   React Router for navigation with protected routes

-   Service layer for API communication

-   Theme configuration for consistent styling

-   Responsive design implementation for various device sizes

#### Backend Structure:

-   Layered architecture (controllers, services, models, middleware)

-   Route organization by resource type

-   Middleware for authentication, logging, and error handling

-   Service layer for business logic

-   Data access layer for database interactions

-   Utility modules for common functions

#### Database Organization:

-   Clear separation of concerns across different database technologies

-   MongoDB for user data, configurations, and metadata

-   TimescaleDB for time-series metrics and analytics

-   Elasticsearch for full-text search and log indexing

-   Redis for caching, session management, and real-time data

### Authentication Implementation

> A robust authentication system has been implemented with the following
> features:

#### User Registration:

-   Secure user creation with email verification

-   Password strength requirements enforcement

-   Duplicate account prevention

-   Role assignment during registration

#### Login System:

-   Secure password hashing using bcrypt

-   JWT token generation with appropriate expiration

-   Refresh token mechanism for extended sessions

-   \"Remember Me\" functionality for 30-day persistence

-   Failed login attempt limiting for security

#### Session Management:

-   Secure cookie storage for tokens

-   Cross-site request forgery (CSRF) protection

-   Session timeout handling

-   Concurrent session management

#### Authorization Framework:

-   Role-based access control foundation

-   Permission definitions for different user types

-   Middleware for route protection

-   Object-level permission checking infrastructure

### Database Schema Creation

> Comprehensive database schemas have been designed and implemented
> across multiple database systems:

#### MongoDB Collections:

-   Users collection with proper indexing and validation

-   Roles collection with permission mappings

-   Logs collection with TTL indexes for retention policies

-   Agents collection for tracking deployed agents

-   Alerts collection for security incident tracking

-   Reports collection for generated reports

-   Configurations collection for system settings

#### TimescaleDB Tables:

-   Log metrics table with hypertable configuration

-   Performance metrics table for system monitoring

-   Agent health metrics table for agent monitoring

-   API usage metrics table for tracking API performance

#### Elasticsearch Mappings:

-   Logs index with appropriate field mappings

-   Full-text search configuration

-   Analyzer settings for optimal search performance

-   Index lifecycle management for log rotation

#### Redis Structures:

-   Session storage configuration

-   Cache invalidation strategies

-   Real-time metrics storage

-   Pub/sub channels for notifications

### Initial API Endpoints Development

> The team has successfully developed and documented the initial API
> endpoints:

#### Authentication Endpoints:

-   POST /api/v1/auth/register for user registration

-   POST /api/v1/auth/login for user authentication

-   POST /api/v1/auth/refresh for token refresh

-   POST /api/v1/auth/logout for secure logout

#### User Management Endpoints:

-   GET /api/v1/users for listing users

-   GET /api/v1/users/{id} for retrieving user details

-   PUT /api/v1/users/{id} for updating user information

-   DELETE /api/v1/users/{id} for removing users

-   GET /api/v1/users/me for current user information

#### Log Management Endpoints:

-   POST /api/v1/logs for log ingestion from agents

-   GET /api/v1/logs for retrieving logs with filtering

-   GET /api/v1/logs/{id} for specific log details

-   GET /api/v1/logs/statistics for log analytics

#### System Endpoints:

-   GET /api/v1/health for system health checking

-   GET /api/v1/metrics for system performance metrics

#### API Documentation:

-   Interactive Swagger UI at /api/docs

-   JSON specification at /api/docs.json

-   Detailed request/response models for all endpoints

### Additional Achievements

> Beyond the planned objectives, the team has also accomplished:

-   **Enhanced \"Remember Me\" Functionality**: Implemented a
    comprehensive \"Remember Me\" feature that allows users to stay
    logged in for extended periods (30 days) across browser sessions and
    restarts.

-   **Log Persistence Fix**: Identified and resolved an issue with the
    MongoDB TTL index that was causing logs to be automatically deleted
    after ingestion.

-   **Network Configuration**: Resolved several networking issues
    related to container communication, external access, and content
    security policy violations.

-   **Dashboard Foundation**: Created the initial dashboard structure
    with real-time data retrieval from the database instead of hardcoded
    values.

## Technical Implementation Details

### Authentication System

> The authentication system implements a secure JWT-based approach:
>
> The \"Remember Me\" functionality extends session persistence:

### Database Schema Implementation

### API Documentation Implementation

> The Swagger/OpenAPI documentation provides comprehensive API
> information:

### Docker Containerization

> The Docker Compose configuration ensures proper service orchestration:
>
> dockerfile: Dockerfile ports:
>
> \- \"3000:3000\"
>
> volumes:

-   ./frontend:/app

-   /app/node_modules environment:

-   NODE_ENV=development depends_on:

-   backend

> backend: build:
>
> context: ./backend dockerfile: Dockerfile
>
> ports:
>
> \- \"5000:5000\"
>
> volumes:

-   ./backend:/app

-   /app/node_modules environment:

-   NODE_ENV=development

-   MONGO_URI=mongodb://mongodb:27017/exlog

-   REDIS_URI=redis://redis:6379

-   ELASTICSEARCH_URI=[http://elasticsearch:9200](http://elasticsearch:9200/)

-   TIMESCALE_URI=*********************************************/exlog

-   JWT_SECRET=dev_jwt_secret

-   REFRESH_TOKEN_SECRET=dev_refresh_secret depends_on:

-   mongodb

-   redis

-   elasticsearch

-   timescaledb

> \# Database services configuration\...
>
> nginx:
>
> image: nginx:alpine ports:
>
> \- \"8080:80\"
>
> volumes:

-   ./nginx/default.conf:/etc/nginx/conf.d/default.conf depends_on:

-   frontend

-   backend

## Challenges and Solutions

### Challenge 1: Log Persistence Issues

> **Problem**: Real logs from agents were being automatically deleted
> from the database after ingestion, while only manually inserted sample
> logs persisted.
>
> **Root Cause**: The MongoDB Log model had a TTL (Time To Live) index
> configured with
>
> expireAfterSeconds: 0, which caused all logs to be deleted immediately
> after creation.
>
> **Solution**: Modified the TTL index configuration to use a proper
> retention period (30 days by default) and made it configurable through
> environment variables:

### Challenge 2: Network Configuration Issues

> **Problem**: Multiple networking issues were encountered, including
> CSP violations, login failures from external IP addresses, and blank
> API documentation screens when accessed from external devices.
>
> **Root Cause**: The frontend was hardcoded to use localhost:5000 for
> API calls, which worked only when accessed from the same machine.
>
> **Solution**: Implemented dynamic API URL detection and configured
> proper CORS settings:

### Challenge 3: Database Schema Complexity

> **Problem**: Designing a database schema that could efficiently handle
> the diverse requirements of log storage, user management, and
> configuration while supporting multiple database technologies.
>
> **Root Cause**: Different aspects of the system had different data
> storage requirements, from document-based user data to time-series
> metrics and full-text searchable logs.
>
> **Solution**: Implemented a polyglot persistence approach with clear
> separation of concerns:

1.  **MongoDB**: Used for document-oriented data like user profiles,
    configurations, and metadata

2.  **TimescaleDB**: Implemented for time-series data like performance
    metrics and aggregated log statistics

3.  **Elasticsearch**: Configured for full-text search and complex log
    querying

4.  **Redis**: Utilized for caching, session management, and real-time
    data

> This approach allowed each database technology to be used for its
> strengths while maintaining data consistency through careful service
> design

# Appendix A: GANTT Chart {#appendix-a-gantt-chart .unnumbered}

![](media/image1.jpeg){width="6.661666666666667in"
height="5.680207786526684in"}

> Figure 1: GANTT Chart

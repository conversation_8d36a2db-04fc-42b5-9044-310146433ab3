#!/bin/bash


set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DOCKER_HUB_NAMESPACE="${DOCKER_HUB_NAMESPACE:-jmason11}"
VERSION="${VERSION:-$(cat VERSION 2>/dev/null || echo "1.0.0")}"
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
PLATFORMS="${PLATFORMS:-linux/amd64,linux/arm64}"

FRONTEND_IMAGE="$DOCKER_HUB_NAMESPACE/exlog-frontend"
BACKEND_IMAGE="$DOCKER_HUB_NAMESPACE/exlog-backend"
WEBSOCKET_IMAGE="$DOCKER_HUB_NAMESPACE/exlog-websocket"
AI_SERVICE_IMAGE="$DOCKER_HUB_NAMESPACE/exlog-ai-service"
NGINX_IMAGE="$DOCKER_HUB_NAMESPACE/exlog-nginx"

echo -e "${BLUE}ExLog Dashboard Docker Hub Build and Push${NC}"
echo "=============================================="
echo -e "Namespace: ${DOCKER_HUB_NAMESPACE}"
echo -e "Version: ${VERSION}"
echo -e "Git Commit: ${GIT_COMMIT}"
echo -e "Build Date: ${BUILD_DATE}"
echo -e "Platforms: ${PLATFORMS}"
echo ""

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] [${level}] $message"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

check_prerequisites() {
    log "INFO" "Checking prerequisites..."
    
    if ! command -v docker >/dev/null 2>&1; then
        error_exit "Docker is not installed"
    fi
    
    if ! docker info >/dev/null 2>&1; then
        error_exit "Docker daemon is not running"
    fi
    
    if ! docker buildx version >/dev/null 2>&1; then
        error_exit "Docker buildx is required for multi-platform builds"
    fi
    
    if ! docker info | grep -q "Username:"; then
        log "WARN" "Not logged in to Docker Hub. Please run: docker login"
        error_exit "Docker Hub authentication required"
    fi
    
    log "INFO" "Prerequisites check passed"
}

setup_buildx() {
    log "INFO" "Setting up Docker buildx..."
    
    if ! docker buildx ls | grep -q "exlog-builder"; then
        docker buildx create --name exlog-builder --driver docker-container --bootstrap
    fi
    
    docker buildx use exlog-builder
    
    log "INFO" "Docker buildx setup completed"
}

build_frontend() {
    log "INFO" "Building and pushing frontend image..."
    
    docker buildx build \
        --platform "$PLATFORMS" \
        --file frontend/Dockerfile \
        --tag "$FRONTEND_IMAGE:$VERSION" \
        --tag "$FRONTEND_IMAGE:latest" \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$VERSION" \
        --push \
        ./frontend
    
    log "INFO" "Frontend image built and pushed successfully"
}

build_backend() {
    log "INFO" "Building and pushing backend image..."
    
    docker buildx build \
        --platform "$PLATFORMS" \
        --file backend/Dockerfile \
        --tag "$BACKEND_IMAGE:$VERSION" \
        --tag "$BACKEND_IMAGE:latest" \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$VERSION" \
        --push \
        ./backend
    
    log "INFO" "Backend image built and pushed successfully"
}

build_websocket() {
    log "INFO" "Building and pushing WebSocket service image..."
    
    docker buildx build \
        --platform "$PLATFORMS" \
        --file backend/Dockerfile.websocket \
        --tag "$WEBSOCKET_IMAGE:$VERSION" \
        --tag "$WEBSOCKET_IMAGE:latest" \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$VERSION" \
        --push \
        ./backend
    
    log "INFO" "WebSocket service image built and pushed successfully"
}

build_ai_service() {
    log "INFO" "Building and pushing AI service image..."
    
    docker buildx build \
        --platform "$PLATFORMS" \
        --file ai-service/Dockerfile \
        --tag "$AI_SERVICE_IMAGE:$VERSION" \
        --tag "$AI_SERVICE_IMAGE:latest" \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$VERSION" \
        --push \
        ./ai-service
    
    log "INFO" "AI service image built and pushed successfully"
}

build_nginx() {
    log "INFO" "Building and pushing Nginx image..."
    
    docker buildx build \
        --platform "$PLATFORMS" \
        --file nginx/Dockerfile \
        --tag "$NGINX_IMAGE:$VERSION" \
        --tag "$NGINX_IMAGE:latest" \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$VERSION" \
        --push \
        ./nginx
    
    log "INFO" "Nginx image built and pushed successfully"
}

generate_manifest() {
    log "INFO" "Generating image manifest..."
    
    cat > docker-images.json << EOF
{
  "version": "$VERSION",
  "build_date": "$BUILD_DATE",
  "git_commit": "$GIT_COMMIT",
  "namespace": "$DOCKER_HUB_NAMESPACE",
  "images": {
    "frontend": {
      "name": "$FRONTEND_IMAGE",
      "tags": ["$VERSION", "latest"],
      "platforms": ["linux/amd64", "linux/arm64"]
    },
    "backend": {
      "name": "$BACKEND_IMAGE",
      "tags": ["$VERSION", "latest"],
      "platforms": ["linux/amd64", "linux/arm64"]
    },
    "websocket": {
      "name": "$WEBSOCKET_IMAGE",
      "tags": ["$VERSION", "latest"],
      "platforms": ["linux/amd64", "linux/arm64"]
    },
    "ai_service": {
      "name": "$AI_SERVICE_IMAGE",
      "tags": ["$VERSION", "latest"],
      "platforms": ["linux/amd64", "linux/arm64"]
    },
    "nginx": {
      "name": "$NGINX_IMAGE",
      "tags": ["$VERSION", "latest"],
      "platforms": ["linux/amd64", "linux/arm64"]
    }
  }
}
EOF
    
    log "INFO" "Image manifest generated: docker-images.json"
}

show_completion() {
    echo ""
    echo -e "${GREEN}🎉 Docker Hub Build and Push Completed Successfully!${NC}"
    echo "====================================================="
    echo ""
    echo -e "${BLUE}Published Images:${NC}"
    echo -e "  • Frontend: ${FRONTEND_IMAGE}:${VERSION}"
    echo -e "  • Backend: ${BACKEND_IMAGE}:${VERSION}"
    echo -e "  • WebSocket: ${WEBSOCKET_IMAGE}:${VERSION}"
    echo -e "  • AI Service: ${AI_SERVICE_IMAGE}:${VERSION}"
    echo -e "  • Nginx: ${NGINX_IMAGE}:${VERSION}"
    echo ""
    echo -e "${BLUE}Docker Hub URLs:${NC}"
    echo -e "  • https://hub.docker.com/r/${DOCKER_HUB_NAMESPACE}/exlog-frontend"
    echo -e "  • https://hub.docker.com/r/${DOCKER_HUB_NAMESPACE}/exlog-backend"
    echo -e "  • https://hub.docker.com/r/${DOCKER_HUB_NAMESPACE}/exlog-websocket"
    echo -e "  • https://hub.docker.com/r/${DOCKER_HUB_NAMESPACE}/exlog-ai-service"
    echo -e "  • https://hub.docker.com/r/${DOCKER_HUB_NAMESPACE}/exlog-nginx"
    echo ""
    echo -e "${BLUE}Usage:${NC}"
    echo -e "  docker pull ${FRONTEND_IMAGE}:${VERSION}"
    echo -e "  docker pull ${BACKEND_IMAGE}:${VERSION}"
    echo -e "  docker pull ${WEBSOCKET_IMAGE}:${VERSION}"
    echo -e "  docker pull ${AI_SERVICE_IMAGE}:${VERSION}"
    echo -e "  docker pull ${NGINX_IMAGE}:${VERSION}"
    echo ""
}

main() {
    check_prerequisites
    setup_buildx
    build_frontend
    build_backend
    build_websocket
    build_ai_service
    build_nginx
    generate_manifest
    show_completion
}

case "${1:-}" in
    --help|-h)
        echo "ExLog Dashboard Docker Hub Build and Push Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h              Show this help message"
        echo "  --frontend-only         Build and push only frontend image"
        echo "  --backend-only          Build and push only backend image"
        echo "  --websocket-only        Build and push only WebSocket service image"
        echo "  --ai-service-only       Build and push only AI service image"
        echo "  --nginx-only            Build and push only Nginx image"
        echo "  --dry-run              Show what would be built without actually building"
        echo ""
        echo "Environment Variables:"
        echo "  DOCKER_HUB_NAMESPACE    Docker Hub namespace (default: exlog)"
        echo "  VERSION                 Image version tag (default: from VERSION file or 1.0.0)"
        echo "  PLATFORMS               Build platforms (default: linux/amd64,linux/arm64)"
        echo ""
        exit 0
        ;;
    --frontend-only)
        check_prerequisites
        setup_buildx
        build_frontend
        exit 0
        ;;
    --backend-only)
        check_prerequisites
        setup_buildx
        build_backend
        exit 0
        ;;
    --websocket-only)
        check_prerequisites
        setup_buildx
        build_websocket
        exit 0
        ;;
    --ai-service-only)
        check_prerequisites
        setup_buildx
        build_ai_service
        exit 0
        ;;
    --nginx-only)
        check_prerequisites
        setup_buildx
        build_nginx
        exit 0
        ;;
    --dry-run)
        echo "DRY RUN - Would build and push the following images:"
        echo "  • $FRONTEND_IMAGE:$VERSION"
        echo "  • $BACKEND_IMAGE:$VERSION"
        echo "  • $WEBSOCKET_IMAGE:$VERSION"
        echo "  • $AI_SERVICE_IMAGE:$VERSION"
        echo "  • $NGINX_IMAGE:$VERSION"
        echo ""
        echo "Platforms: $PLATFORMS"
        echo "Build Date: $BUILD_DATE"
        echo "Git Commit: $GIT_COMMIT"
        exit 0
        ;;
esac

main "$@"

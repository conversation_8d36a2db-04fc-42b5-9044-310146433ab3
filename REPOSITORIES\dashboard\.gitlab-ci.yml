# GitLab CI/CD Configuration for Dashboard Project - Simplified
# This configuration uses centralized templates from the cicd-templates project


# Include centralized templates
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/nodejs.yml'

# Project-specific variables
variables:
  NODE_VERSION: "18"
  PROJECT_NAME: "exlog-dashboard"
  DOCKER_HUB_NAMESPACE: "exlog"
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

# Simple project validation
validate_dashboard_structure:
  stage: validate
  extends: .base_job
  image: node:18
  script:
    - echo "Validating dashboard project structure..."
    - ls -la
    - echo "✓ Found package.json" && test -f package.json
    - echo "✓ Found frontend directory" && test -d frontend
    - echo "✓ Found backend directory" && test -d backend
    - echo "Dashboard structure validation passed"

# Docker build and push stages
build_docker_images:
  stage: build
  extends: .base_job
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $DOCKER_HUB_USERNAME -p $DOCKER_HUB_PASSWORD
    - apk add --no-cache git
  script:
    - export VERSION=$(cat VERSION 2>/dev/null || echo "1.0.0")
    - export BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    - export GIT_COMMIT=$(git rev-parse --short HEAD)
    - echo "Building version $VERSION"
    - chmod +x .dockerhub/build-and-push.sh
    - ./.dockerhub/build-and-push.sh
  only:
    - main
    - tags
  when: manual

# Release creation stage
create_release:
  stage: deploy
  extends: .base_job
  image: node:18
  before_script:
    - apt-get update && apt-get install -y git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "ExLog CI"
  script:
    - chmod +x scripts/release.sh
    - ./scripts/release.sh patch
  only:
    - main
  when: manual

# Production deployment stage
deploy_production:
  stage: deploy
  extends: .base_job
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  script:
    - echo "Deploying to production environment"
    - docker-compose -f docker-compose.prod.yml pull
    - docker-compose -f docker-compose.prod.yml up -d
  environment:
    name: production
    url: http://localhost:8080
  only:
    - tags
  when: manual

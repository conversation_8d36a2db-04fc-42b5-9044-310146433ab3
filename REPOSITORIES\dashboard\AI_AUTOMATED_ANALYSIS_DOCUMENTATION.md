# AI Automated Analysis System Documentation

## Overview

The AI Automated Analysis System provides comprehensive, configurable, and automated analysis of log data using machine learning algorithms. The system includes scheduling, data retention, monitoring, and health checking capabilities.

## Architecture

### Core Components

1. **AI Scheduler Service** (`aiSchedulerService.js`)
   - Manages automated analysis scheduling
   - Handles system restart recovery
   - Prevents duplicate analysis runs
   - Supports graceful shutdown

2. **Data Retention Service** (`dataRetentionService.js`)
   - Manages data lifecycle and cleanup
   - Implements compression strategies
   - Handles retention policies

3. **AI Monitoring Service** (`aiMonitoringService.js`)
   - Provides health checks and monitoring
   - Generates alerts for system issues
   - Tracks performance metrics

4. **AI Configuration Model** (`AIConfig.js`)
   - Stores analysis configuration settings
   - Validates configuration parameters
   - Manages scheduling parameters

5. **AI Analysis Results Model** (`AIAnalysisResult.js`)
   - Stores analysis results and metadata
   - Tracks performance metrics
   - Manages result retention

## Configuration

### Analysis Settings

```javascript
{
  "analysisSettings": {
    "interval": 300,                    // Analysis interval in seconds (60-86400)
    "timeRange": "1h",                  // Time range for analysis (15m, 30m, 1h, 2h, 6h, 12h, 24h)
    "enabledLogTypes": ["warn", "error", "fatal"],  // Log levels to analyze
    "maxLogsPerAnalysis": 10000,        // Maximum logs per analysis run
    "autoScheduling": {
      "enabled": true,                  // Enable automated scheduling
      "cronExpression": null,           // Custom cron expression (auto-generated if null)
      "timezone": "UTC",                // Timezone for scheduling
      "maxConcurrentRuns": 1,           // Maximum concurrent analysis runs
      "skipIfRunning": true             // Skip if analysis already running
    },
    "analysisTypes": {
      "periodic": true,                 // Enable periodic analysis
      "triggered": true,                // Enable triggered analysis
      "manual": true                    // Enable manual analysis
    }
  }
}
```

### Data Retention Settings

```javascript
{
  "dataRetention": {
    "analysisResultsRetentionDays": 180,    // Retention period for analysis results
    "insightsRetentionDays": 90,            // Retention period for insights
    "feedbackRetentionDays": 365,           // Retention period for feedback
    "autoCleanup": {
      "enabled": true,                      // Enable automatic cleanup
      "schedule": "0 2 * * *",              // Cleanup schedule (daily at 2 AM)
      "batchSize": 1000                     // Cleanup batch size
    },
    "compressionSettings": {
      "enabled": true,                      // Enable data compression
      "compressAfterDays": 30               // Compress data after N days
    }
  }
}
```

### Feature Configuration

```javascript
{
  "anomalyDetection": {
    "enabled": true,                        // Enable anomaly detection
    "threshold": 0.1,                       // Anomaly threshold (0.01-0.5)
    "sensitivity": "medium",                // Sensitivity level (low, medium, high)
    "minAnomaliesForAlert": 5               // Minimum anomalies to trigger alert
  },
  "threatPrediction": {
    "enabled": true,                        // Enable threat prediction
    "confidenceThreshold": 70,              // Confidence threshold (50-95)
    "enabledPatterns": [                    // Enabled threat patterns
      "bruteForce",
      "dataExfiltration",
      "privilegeEscalation",
      "malwareActivity",
      "networkIntrusion"
    ],
    "alertOnCritical": true                 // Alert on critical threats
  },
  "patternMatching": {
    "enabled": true,                        // Enable pattern matching
    "enableMitreAttack": true,              // Enable MITRE ATT&CK patterns
    "enableCustomRules": true               // Enable custom rules
  }
}
```

## API Endpoints

### Configuration Management

#### Get AI Configuration
```http
GET /api/v1/ai/config
Authorization: Bearer <token>
```

Response:
```json
{
  "success": true,
  "data": {
    "id": "config_id",
    "name": "Default AI Configuration",
    "analysisSettings": { ... },
    "scheduling": {
      "enabled": true,
      "cronExpression": "*/5 * * * *",
      "nextRun": "2024-01-01T12:05:00.000Z",
      "status": "idle"
    }
  }
}
```

#### Update AI Configuration
```http
PUT /api/v1/ai/config
Authorization: Bearer <token>
Content-Type: application/json

{
  "analysisSettings": {
    "interval": 600,
    "timeRange": "2h"
  }
}
```

### Analysis Results

#### Get Analysis Results
```http
GET /api/v1/ai/results?limit=10&startTime=2024-01-01T00:00:00Z&endTime=2024-01-01T23:59:59Z
Authorization: Bearer <token>
```

#### Get Specific Analysis Result
```http
GET /api/v1/ai/results/{analysisId}
Authorization: Bearer <token>
```

### Monitoring and Health

#### Get Health Status
```http
GET /api/v1/ai/health
Authorization: Bearer <token>
```

Response:
```json
{
  "success": true,
  "data": {
    "overall": "healthy",
    "lastCheck": "2024-01-01T12:00:00.000Z",
    "scheduler": {
      "status": "healthy",
      "details": {
        "initialized": true,
        "scheduledJobs": 1,
        "runningAnalyses": 0
      }
    },
    "metrics": {
      "totalAnalyses": 100,
      "successfulAnalyses": 95,
      "failedAnalyses": 5,
      "successRate": 0.95
    },
    "alerts": []
  }
}
```

#### Get Scheduler Status
```http
GET /api/v1/ai/scheduler/status
Authorization: Bearer <token>
```

#### Trigger Manual Health Check
```http
POST /api/v1/ai/monitoring/trigger-health-check
Authorization: Bearer <token>
```

## Frontend Components

### AI Configuration Panel
Location: `frontend/src/components/AI/AIConfigurationPanel.jsx`

Features:
- Configure analysis intervals and time ranges
- Enable/disable automated scheduling
- Set data retention policies
- View scheduler status
- Real-time configuration validation

### AI Analysis History
Location: `frontend/src/components/AI/AIAnalysisHistory.jsx`

Features:
- View historical analysis results
- Filter by date range, risk level, and type
- View detailed analysis results
- Performance metrics visualization

## System Startup and Recovery

### Startup Process
1. **Recovery Phase**: Check for stuck analyses from previous session
2. **Configuration Loading**: Load active AI configurations
3. **Scheduler Initialization**: Set up cron jobs for automated analysis
4. **Health Check Setup**: Initialize monitoring and health checks

### Recovery Mechanisms
- **Stuck Analysis Detection**: Identifies analyses running for >30 minutes
- **Graceful Failure Handling**: Marks interrupted analyses as failed
- **Duplicate Prevention**: Prevents overlapping analysis runs
- **Resource Cleanup**: Removes orphaned data and locks

## Monitoring and Alerting

### Health Checks
- **Scheduler Health**: Verifies scheduler initialization and job status
- **Data Retention Health**: Monitors retention service status
- **Recent Analyses Health**: Checks for analysis failures and stuck runs
- **System Resources Health**: Monitors memory usage and system performance

### Alert Conditions
- High failure rate (>20%)
- Consecutive analysis failures (≥3)
- Long-running analyses (>5 minutes)
- System resource exhaustion
- Service initialization failures

### Performance Metrics
- Total analyses count
- Success/failure rates
- Average analysis duration
- Memory and CPU usage
- Cache hit rates

## Best Practices

### Configuration
1. **Interval Selection**: Choose intervals based on log volume and system capacity
2. **Time Range**: Balance between data completeness and processing time
3. **Concurrent Runs**: Limit concurrent runs to prevent resource exhaustion
4. **Retention Policies**: Set appropriate retention periods based on compliance requirements

### Monitoring
1. **Regular Health Checks**: Monitor system health at least every 5 minutes
2. **Alert Thresholds**: Set realistic thresholds based on historical performance
3. **Performance Tracking**: Monitor trends over time to identify degradation
4. **Resource Monitoring**: Keep track of memory and CPU usage patterns

### Troubleshooting
1. **Check Logs**: Review application logs for error messages
2. **Health Status**: Use health check endpoints to identify issues
3. **Configuration Validation**: Verify configuration settings are valid
4. **Resource Usage**: Monitor system resources for bottlenecks
5. **Database Performance**: Check database query performance and indexes

## Error Handling

### Common Issues
- **Configuration Validation Errors**: Invalid parameter values
- **Scheduling Conflicts**: Overlapping analysis runs
- **Resource Exhaustion**: Memory or CPU limits exceeded
- **Database Connection Issues**: MongoDB connectivity problems
- **AI Service Unavailability**: AI analysis service downtime

### Recovery Strategies
- **Automatic Retry**: Failed analyses are automatically retried
- **Graceful Degradation**: System continues operating with reduced functionality
- **Alert Generation**: Notifications sent for critical issues
- **Manual Intervention**: Admin tools for manual recovery

## Security Considerations

### Authentication
- All API endpoints require valid JWT tokens
- Role-based access control for configuration changes
- Audit logging for configuration modifications

### Data Protection
- Analysis results contain sensitive log data
- Proper data retention and deletion policies
- Encryption at rest and in transit
- Access logging and monitoring

## Performance Optimization

### Database Optimization
- Proper indexing on frequently queried fields
- Aggregation pipelines for performance statistics
- Connection pooling and query optimization
- Regular maintenance and cleanup

### Memory Management
- Configurable batch sizes for processing
- Memory usage monitoring and alerts
- Garbage collection optimization
- Resource limit enforcement

### Scalability
- Horizontal scaling support for analysis workers
- Load balancing for API endpoints
- Database sharding for large datasets
- Caching strategies for frequently accessed data

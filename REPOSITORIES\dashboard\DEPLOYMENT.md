# ExLog Dashboard Production Deployment Guide

This guide covers best practices for deploying ExLog Dashboard in production environments.

## Production Architecture

### Recommended Infrastructure

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Servers   │    │   Database      │
│   (Nginx/HAProxy)│────│   (ExLog App)   │────│   (MongoDB)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌─────────┐            ┌─────────┐            ┌─────────┐
    │ SSL/TLS │            │ Docker  │            │ Backup  │
    │ Termination│          │ Swarm/K8s│          │ Storage │
    └─────────┘            └─────────┘            └─────────┘
```

### Minimum Production Requirements

- **CPU**: 4 cores
- **Memory**: 8GB RAM
- **Storage**: 100GB SSD (with growth planning)
- **Network**: 1Gbps connection
- **OS**: Ubuntu 20.04 LTS or later, CentOS 8+, or RHEL 8+

## Pre-Deployment Checklist

### Security Hardening

- [ ] Change all default passwords
- [ ] Generate secure JWT secrets
- [ ] Configure SSL/TLS certificates
- [ ] Set up firewall rules
- [ ] Enable audit logging
- [ ] Configure backup encryption
- [ ] Review CORS settings
- [ ] Set up monitoring and alerting

### Infrastructure Preparation

- [ ] Provision servers with adequate resources
- [ ] Set up load balancer (if using multiple instances)
- [ ] Configure DNS records
- [ ] Set up SSL certificates
- [ ] Prepare backup storage
- [ ] Configure monitoring systems
- [ ] Set up log aggregation

## Production Configuration

### Environment Variables

Create a production `.env` file with secure settings:

```bash
# Application Environment
NODE_ENV=production

# Security Configuration
JWT_SECRET=your-256-bit-secret-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Database Configuration
MONGODB_URI=************************************************************************************************
MONGODB_PASSWORD=your-secure-mongodb-password

# Network Configuration
CORS_ORIGIN=https://your-domain.com,https://app.your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
AUTH_RATE_LIMIT_MAX=20

# SSL Configuration (if terminating SSL at application level)
SSL_CERT_PATH=/etc/ssl/certs/exlog.crt
SSL_KEY_PATH=/etc/ssl/private/exlog.key

# Logging Configuration
LOG_LEVEL=warn
LOG_FILE=/var/log/exlog/app.log
LOG_MAX_SIZE=100m
LOG_MAX_FILES=30d

# Data Retention
LOG_RETENTION_SECONDS=7776000    # 90 days
ALERT_RETENTION_SECONDS=31536000 # 365 days
ENABLE_AUTO_DELETE=true
ARCHIVE_BEFORE_DELETE=true
ARCHIVE_RETENTION_DAYS=2555      # 7 years

# External Storage (recommended for production)
EXTERNAL_STORAGE_TYPE=s3
S3_BUCKET=your-exlog-logs-bucket
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key

# Email Configuration
RESEND_API_KEY=your-resend-api-key
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Monitoring and Alerting
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
ALERT_WEBHOOK_URL=https://your-webhook-url.com/alerts
```

### Docker Compose Production Configuration

Use the production Docker Compose file with environment-specific settings:

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  frontend:
    image: exlog/exlog-frontend:${EXLOG_VERSION:-latest}
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  backend:
    image: exlog/exlog-backend:${EXLOG_VERSION:-latest}
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  mongodb:
    image: mongo:7.0
    restart: unless-stopped
    command: mongod --replSet rs0 --bind_ip_all
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

## SSL/TLS Configuration

### Using Let's Encrypt with Nginx

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Database Configuration

### MongoDB Production Setup

```javascript
// MongoDB replica set initialization
rs.initiate({
  _id: "rs0",
  members: [
    { _id: 0, host: "mongodb-1:27017" },
    { _id: 1, host: "mongodb-2:27017" },
    { _id: 2, host: "mongodb-3:27017" }
  ]
});

// Create application user
use exlog;
db.createUser({
  user: "exlog_user",
  pwd: "secure_password_here",
  roles: [
    { role: "readWrite", db: "exlog" },
    { role: "dbAdmin", db: "exlog" }
  ]
});
```

### Database Indexes

```javascript
// Performance indexes
db.logs.createIndex({ "timestamp": -1 });
db.logs.createIndex({ "level": 1, "timestamp": -1 });
db.logs.createIndex({ "source": 1, "timestamp": -1 });
db.logs.createIndex({ "agent_id": 1, "timestamp": -1 });

// Text search index
db.logs.createIndex({ "message": "text", "details": "text" });

// Compound indexes for common queries
db.logs.createIndex({ "level": 1, "source": 1, "timestamp": -1 });
db.alerts.createIndex({ "status": 1, "created_at": -1 });
```

## Monitoring and Alerting

### Health Check Endpoints

ExLog Dashboard provides several health check endpoints:

- `/health` - Basic application health
- `/health/detailed` - Detailed component status
- `/metrics` - Prometheus metrics
- `/api/v1/health` - API health check

### Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'exlog-dashboard'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### Grafana Dashboard

Import the ExLog Dashboard Grafana template:

```json
{
  "dashboard": {
    "title": "ExLog Dashboard Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      }
    ]
  }
}
```

## Backup Strategy

### Automated Backup Script

```bash
#!/bin/bash
# Production backup script

BACKUP_DIR="/var/backups/exlog"
RETENTION_DAYS=30
S3_BUCKET="your-backup-bucket"

# Create MongoDB backup
mongodump --uri="*****************************************" --out="$BACKUP_DIR/$(date +%Y%m%d)"

# Backup application data
tar -czf "$BACKUP_DIR/app-data-$(date +%Y%m%d).tar.gz" /var/lib/docker/volumes/

# Upload to S3
aws s3 sync "$BACKUP_DIR" "s3://$S3_BUCKET/backups/"

# Clean old backups
find "$BACKUP_DIR" -type f -mtime +$RETENTION_DAYS -delete
```

### Backup Verification

```bash
#!/bin/bash
# Verify backup integrity

BACKUP_FILE="$1"
TEST_DB="exlog_backup_test"

# Restore to test database
mongorestore --uri="mongodb://localhost:27017/$TEST_DB" "$BACKUP_FILE"

# Verify data integrity
mongo "$TEST_DB" --eval "
  var count = db.logs.count();
  if (count > 0) {
    print('Backup verification successful: ' + count + ' documents');
    exit(0);
  } else {
    print('Backup verification failed: no documents found');
    exit(1);
  }
"

# Clean up test database
mongo "$TEST_DB" --eval "db.dropDatabase()"
```

## Security Hardening

### Firewall Configuration

```bash
# UFW firewall rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow from 10.0.0.0/8 to any port 27017  # MongoDB (internal only)
sudo ufw enable
```

### Docker Security

```yaml
# docker-compose.security.yml
version: '3.8'

services:
  backend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    user: "1000:1000"
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
```

### Application Security Headers

```nginx
# Security headers in Nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

## Performance Optimization

### Database Optimization

```javascript
// MongoDB performance settings
db.adminCommand({
  setParameter: 1,
  wiredTigerCacheSizeGB: 4,  // Adjust based on available RAM
  wiredTigerMaxCacheOverflowFileSizeGB: 1
});

// Enable profiling for slow queries
db.setProfilingLevel(1, { slowms: 100 });
```

### Application Tuning

```bash
# Node.js performance environment variables
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=2048"
export UV_THREADPOOL_SIZE=16
```

### Nginx Optimization

```nginx
# Nginx performance settings
worker_processes auto;
worker_connections 1024;

gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## Scaling Considerations

### Horizontal Scaling

```yaml
# Docker Swarm scaling
version: '3.8'

services:
  backend:
    deploy:
      replicas: 5
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

### Load Balancing

```nginx
# Nginx load balancing
upstream exlog_backend {
    least_conn;
    server backend-1:5000 max_fails=3 fail_timeout=30s;
    server backend-2:5000 max_fails=3 fail_timeout=30s;
    server backend-3:5000 max_fails=3 fail_timeout=30s;
}

server {
    location /api/ {
        proxy_pass http://exlog_backend;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }
}
```

## Disaster Recovery

### Recovery Procedures

1. **Database Recovery**
   ```bash
   # Restore from backup
   mongorestore --uri="mongodb://localhost:27017/exlog" /path/to/backup
   ```

2. **Application Recovery**
   ```bash
   # Pull latest images and restart
   docker-compose -f docker-compose.prod.yml pull
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Data Verification**
   ```bash
   # Verify application functionality
   curl -f http://localhost:8080/health || exit 1
   ```

### Recovery Testing

Schedule regular disaster recovery tests:

```bash
#!/bin/bash
# DR test script
echo "Starting disaster recovery test..."

# Simulate failure
docker-compose down

# Restore from backup
./restore-backup.sh latest

# Verify recovery
./verify-system.sh

echo "Disaster recovery test completed"
```

## Maintenance Procedures

### Regular Maintenance Tasks

1. **Weekly Tasks**
   - Review system logs
   - Check disk space usage
   - Verify backup integrity
   - Update security patches

2. **Monthly Tasks**
   - Update Docker images
   - Review performance metrics
   - Clean up old logs
   - Security audit

3. **Quarterly Tasks**
   - Disaster recovery testing
   - Capacity planning review
   - Security penetration testing
   - Documentation updates

### Update Procedures

```bash
#!/bin/bash
# Production update script

# Backup current state
./backup-system.sh

# Pull new images
docker-compose -f docker-compose.prod.yml pull

# Rolling update
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
sleep 30
docker-compose -f docker-compose.prod.yml up -d --no-deps frontend

# Verify update
./verify-system.sh || {
    echo "Update failed, rolling back..."
    ./rollback-system.sh
    exit 1
}

echo "Update completed successfully"
```

## Troubleshooting

### Common Production Issues

1. **High Memory Usage**
   ```bash
   # Check container memory usage
   docker stats
   
   # Adjust memory limits
   docker-compose -f docker-compose.prod.yml up -d --scale backend=2
   ```

2. **Database Connection Issues**
   ```bash
   # Check MongoDB status
   docker-compose exec mongodb mongo --eval "rs.status()"
   
   # Check connection pool
   docker-compose logs backend | grep "connection"
   ```

3. **Performance Issues**
   ```bash
   # Check slow queries
   docker-compose exec mongodb mongo exlog --eval "db.system.profile.find().sort({ts:-1}).limit(5)"
   
   # Monitor resource usage
   htop
   iotop
   ```

### Log Analysis

```bash
# Centralized logging with ELK stack
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  elasticsearch:7.14.0

docker run -d \
  --name kibana \
  -p 5601:5601 \
  --link elasticsearch:elasticsearch \
  kibana:7.14.0
```

## Support and Documentation

- **Internal Documentation**: Maintain runbooks for common procedures
- **Monitoring Dashboards**: Set up comprehensive monitoring
- **Incident Response**: Define escalation procedures
- **Change Management**: Document all configuration changes

For additional support:
- **Technical Issues**: https://gitlab.com/spr888/dashboard/-/issues
- **Security Concerns**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX

# ExLog Dashboard Installation Guide

This guide provides comprehensive instructions for installing and setting up the ExLog Dashboard on various platforms.

## Quick Start (Recommended)

The fastest way to get ExLog Dashboard running is using our automated installation scripts:

### Linux/macOS
```bash
curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/scripts/install.sh | bash
```

### Windows (PowerShell)
```powershell
Invoke-WebRequest -Uri "https://gitlab.com/spr888/dashboard/-/raw/main/scripts/install.ps1" -OutFile "install.ps1"; .\install.ps1
```

### Windows (Command Prompt)
```cmd
curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/scripts/install.bat -o install.bat && install.bat
```

## Manual Installation

If you prefer to install manually or need more control over the process, follow these detailed instructions.

### Prerequisites

#### System Requirements
- **Operating System**: Linux, macOS, or Windows 10/11
- **Memory**: Minimum 2GB RAM (4GB recommended for Windows)
- **Storage**: Minimum 5GB free disk space
- **Network**: Internet connection for downloading dependencies

#### Required Software
- **Docker**: Version 20.10 or later
- **Docker Compose**: Version 2.0 or later
- **Git**: For cloning the repository (optional if using Docker Hub images)

### Step 1: Install Docker

#### Linux (Ubuntu/Debian)
```bash
# Update package index
sudo apt-get update

# Install dependencies
sudo apt-get install -y ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker
```

#### Linux (CentOS/RHEL)
```bash
# Install dependencies
sudo yum install -y yum-utils

# Add Docker repository
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# Install Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
```

#### macOS
```bash
# Install using Homebrew
brew install --cask docker

# Or download Docker Desktop from:
# https://www.docker.com/products/docker-desktop
```

#### Windows
1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop
2. Run the installer and follow the setup wizard
3. Restart your computer if prompted
4. Start Docker Desktop

### Step 2: Download ExLog Dashboard

#### Option A: Using Docker Hub Images (Recommended)
```bash
# Create installation directory
mkdir -p ~/exlog-dashboard
cd ~/exlog-dashboard

# Download Docker Compose configuration
curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml -o docker-compose.yml
curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/.env.template -o .env
```

#### Option B: Clone Repository (For Development)
```bash
# Clone the repository
git clone https://gitlab.com/spr888/dashboard.git
cd dashboard

# Copy environment template
cp .env.example .env
```

### Step 3: Configure Environment

Edit the `.env` file to customize your installation:

```bash
# Basic configuration
NODE_ENV=production
JWT_SECRET=your-secure-jwt-secret-here
MONGODB_PASSWORD=your-secure-mongodb-password

# Network configuration
CORS_ORIGIN=http://localhost:8080,http://localhost:3000

# Security settings
ENABLE_AUTO_DELETE=true
RATE_LIMIT_MAX=1000

# Log retention (in seconds)
LOG_RETENTION_SECONDS=7776000  # 90 days
ALERT_RETENTION_SECONDS=31536000  # 365 days
```

**Important Security Notes:**
- Change the default `JWT_SECRET` to a secure random string
- Change the default `MONGODB_PASSWORD` to a strong password
- Update `CORS_ORIGIN` to match your domain in production

### Step 4: Start ExLog Dashboard

#### Using Docker Hub Images
```bash
# Pull latest images and start services
docker compose pull
docker compose up -d
```

#### Using Local Build
```bash
# Build images and start services
docker compose build
docker compose up -d
```

### Step 5: Verify Installation

1. **Check service status:**
   ```bash
   docker compose ps
   ```

2. **View logs:**
   ```bash
   docker compose logs -f
   ```

3. **Access the dashboard:**
   - Primary URL: http://localhost:8080
   - Alternative URL: http://localhost:3000

4. **Default login credentials:**
   - Username: `<EMAIL>`
   - Password: `Admin123!`
   
   **⚠️ Important:** Change the default password immediately after first login!

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `NODE_ENV` | `development` | Application environment |
| `JWT_SECRET` | `your-super-secret...` | JWT signing secret |
| `MONGODB_PASSWORD` | `password` | MongoDB root password |
| `CORS_ORIGIN` | `*` | Allowed CORS origins |
| `LOG_LEVEL` | `info` | Application log level |
| `RATE_LIMIT_MAX` | `1000` | API rate limit per window |
| `LOG_RETENTION_SECONDS` | `7776000` | Log retention period (90 days) |
| `ALERT_RETENTION_SECONDS` | `31536000` | Alert retention period (365 days) |

### Port Configuration

| Service | Default Port | Description |
|---------|--------------|-------------|
| Nginx (Main) | 8080 | Primary web interface |
| Frontend | 3000 | React application (direct access) |
| Backend API | 5000 | REST API server |
| WebSocket | 5001 | Real-time communication |
| AI Service | 5002 | AI analysis service |
| MongoDB | 27017 | Database server |

### Storage Configuration

ExLog Dashboard uses Docker volumes for persistent storage:

- `mongodb_data`: Database files
- `exlog_logs`: Application logs
- `exlog_log_storage`: Processed log storage
- `exlog_log_archive`: Archived logs

To customize storage locations, modify the volume mappings in `docker-compose.yml`.

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8080

# Stop conflicting services or change ports in docker-compose.yml
```

#### 2. Docker Permission Denied
```bash
# Add user to docker group (Linux)
sudo usermod -aG docker $USER

# Log out and back in, or run:
newgrp docker
```

#### 3. Services Not Starting
```bash
# Check Docker daemon status
sudo systemctl status docker

# Check service logs
docker compose logs [service_name]

# Restart services
docker compose restart
```

#### 4. Memory Issues
```bash
# Check available memory
free -h

# Increase Docker memory limit in Docker Desktop settings
# Or add swap space on Linux systems
```

### Log Files

Application logs are stored in the following locations:

- **Container logs**: `docker compose logs -f`
- **Application logs**: `./data/logs/` (if using local volumes)
- **Nginx logs**: Available through `docker compose logs nginx`

### Health Checks

ExLog Dashboard includes built-in health checks:

```bash
# Check all services
docker compose ps

# Test API health
curl http://localhost:8080/api/v1/health

# Test frontend
curl http://localhost:8080/
```

## Updating ExLog Dashboard

### Using Docker Hub Images
```bash
# Pull latest images
docker compose pull

# Restart with new images
docker compose up -d
```

### Using Local Build
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker compose build
docker compose up -d
```

## Uninstalling

To completely remove ExLog Dashboard:

```bash
# Stop and remove containers
docker compose down

# Remove volumes (⚠️ This will delete all data!)
docker compose down -v

# Remove images
docker rmi $(docker images "exlog/*" -q)

# Remove installation directory
rm -rf ~/exlog-dashboard
```

## Getting Help

- **Documentation**: https://gitlab.com/spr888/dashboard/-/blob/main/README.md
- **Troubleshooting**: https://gitlab.com/spr888/dashboard/-/blob/main/docs/TROUBLESHOOTING.md
- **Issues**: https://gitlab.com/spr888/dashboard/-/issues
- **Deployment Guide**: https://gitlab.com/spr888/dashboard/-/blob/main/DEPLOYMENT.md

## Next Steps

After successful installation:

1. **Change default credentials**
2. **Configure log sources and agents**
3. **Set up alerting rules**
4. **Configure backup schedules**
5. **Review security settings**

For production deployment, see the [Deployment Guide](DEPLOYMENT.md).

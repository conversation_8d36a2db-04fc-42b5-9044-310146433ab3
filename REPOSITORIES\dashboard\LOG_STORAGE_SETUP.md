# ExLog Log Storage and Retention Setup

This document provides comprehensive instructions for setting up persistent log storage and retention policies in ExLog.

## Overview

ExLog now supports:
- **Persistent log storage** outside Docker containers
- **Configurable retention policies** with automatic cleanup
- **Multiple storage backends** (Local, S3, Azure, GCP)
- **Log archiving and compression**
- **Disaster recovery** with backup systems

## Quick Setup

### 1. Run Setup Script

**Linux/macOS:**
```bash
cd dashboard
chmod +x scripts/setup-log-storage.sh
./scripts/setup-log-storage.sh
```

**Windows:**
```cmd
cd dashboard
scripts\setup-log-storage.bat
```

### 2. Start the Application

**Development:**
```bash
docker-compose up -d
```

**Production:**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Detailed Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Log Storage Paths (host system)
LOG_HOST_PATH=./data/logs
LOG_STORAGE_HOST_PATH=./data/logs/storage
LOG_ARCHIVE_HOST_PATH=./data/logs/archive

# MongoDB Storage
MONGODB_DATA_PATH=./data/mongodb
MONGODB_CONFIG_PATH=./data/mongodb-config

# Log Retention Configuration
LOG_RETENTION_SECONDS=7776000          # 90 days
ALERT_RETENTION_SECONDS=31536000       # 365 days
ENABLE_AUTO_DELETE=false               # Set to true in production
ARCHIVE_BEFORE_DELETE=true             # Archive before deletion
ARCHIVE_RETENTION_DAYS=2555            # 7 years

# Cleanup Job Configuration
CLEANUP_JOB_INTERVAL=86400000          # 24 hours in milliseconds
CLEANUP_BATCH_SIZE=1000                # Logs to process per batch

# Log Compression
LOG_COMPRESSION_ENABLED=true
LOG_COMPRESSION_ALGORITHM=gzip         # gzip or brotli
LOG_COMPRESSION_LEVEL=6

# External Storage Type
EXTERNAL_STORAGE_TYPE=local            # local, s3, azure, gcp
```

### Cloud Storage Configuration

#### Amazon S3
```bash
EXTERNAL_STORAGE_TYPE=s3
S3_BUCKET=your-log-bucket
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key
S3_PREFIX=logs/
```

#### Azure Blob Storage
```bash
EXTERNAL_STORAGE_TYPE=azure
AZURE_STORAGE_CONNECTION_STRING=your-connection-string
AZURE_CONTAINER_NAME=logs
AZURE_PREFIX=logs/
```

#### Google Cloud Storage
```bash
EXTERNAL_STORAGE_TYPE=gcp
GCP_PROJECT_ID=your-project-id
GCP_BUCKET_NAME=your-log-bucket
GCP_PREFIX=logs/
GCP_KEY_FILE_PATH=/path/to/service-account-key.json
```

## Directory Structure

After setup, your directory structure will be:

```
dashboard/
├── data/
│   ├── logs/                    # Application logs
│   ├── logs/storage/           # Processed log storage
│   ├── logs/archive/           # Archived logs
│   ├── mongodb/                # MongoDB data
│   └── mongodb-config/         # MongoDB configuration
├── docker-compose.yml          # Development configuration
├── docker-compose.prod.yml     # Production configuration
└── scripts/
    ├── setup-log-storage.sh    # Linux/macOS setup
    └── setup-log-storage.bat   # Windows setup
```

## Retention Policies

### Creating Retention Policies

1. Access **Settings > System** (Admin only)
2. Navigate to **Log Retention Settings**
3. Click **Add Policy**
4. Configure:
   - **Name**: Unique policy name
   - **Retention Days**: How long to keep logs (1-3650 days)
   - **Log Sources**: Filter by source (System, Application, Security, etc.)
   - **Log Levels**: Filter by level (critical, error, warning, info, debug)
   - **Priority**: Policy priority (1-100, higher = more important)
   - **Default**: Set as default policy for unmatched logs

### Policy Examples

**Critical Logs (Long Retention):**
- Name: "Critical Security Logs"
- Retention: 2555 days (7 years)
- Sources: Security
- Levels: critical, error
- Priority: 90

**Debug Logs (Short Retention):**
- Name: "Debug Information"
- Retention: 7 days
- Sources: Application
- Levels: debug
- Priority: 10

**Default Policy:**
- Name: "Standard Retention"
- Retention: 90 days
- Sources: All
- Levels: All
- Default: Yes

## Storage Backends

### Local Storage
- **Pros**: Simple, fast, no external dependencies
- **Cons**: Limited to single server, no automatic backup
- **Use Case**: Development, small deployments

### Amazon S3
- **Pros**: Highly durable, scalable, cost-effective for archival
- **Cons**: Network dependency, potential egress costs
- **Use Case**: Production deployments, compliance requirements

### Azure Blob Storage
- **Pros**: Integration with Azure ecosystem, good performance
- **Cons**: Azure-specific, network dependency
- **Use Case**: Azure-based deployments

### Google Cloud Storage
- **Pros**: Integration with GCP, good analytics tools
- **Cons**: GCP-specific, network dependency
- **Use Case**: GCP-based deployments

## Monitoring and Management

### API Endpoints

**Get Retention Statistics:**
```bash
GET /api/v1/settings/system/retention-stats
```

**Trigger Manual Cleanup:**
```bash
POST /api/v1/settings/system/trigger-cleanup
```

**Retrieve Archived Log:**
```bash
GET /api/v1/settings/system/archived-log/{logId}
```

### Monitoring Cleanup Jobs

Check application logs for cleanup job status:
```bash
docker-compose logs backend | grep "retention"
```

### Storage Statistics

Monitor storage usage through the admin interface:
- **Settings > System > Log Retention Settings**
- View retention statistics and storage usage

## Backup and Recovery

### Local Backup Script

Create a backup script for local storage:

```bash
#!/bin/bash
# backup-logs.sh

BACKUP_DIR="/backup/exlog-$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# Backup log storage
tar -czf "$BACKUP_DIR/log-storage.tar.gz" ./data/logs/storage/

# Backup archives
tar -czf "$BACKUP_DIR/log-archive.tar.gz" ./data/logs/archive/

# Backup MongoDB
docker exec dashboard-mongodb-1 mongodump --out /tmp/backup
docker cp dashboard-mongodb-1:/tmp/backup "$BACKUP_DIR/mongodb"

echo "Backup completed: $BACKUP_DIR"
```

### Cloud Backup

For cloud storage, backups are handled automatically by the cloud provider's durability guarantees.

## Troubleshooting

### Common Issues

**1. Permission Denied**
```bash
sudo chown -R $USER:$USER ./data/
chmod -R 755 ./data/
```

**2. Disk Space Issues**
- Monitor disk usage: `df -h`
- Adjust retention policies for shorter periods
- Enable compression: `LOG_COMPRESSION_ENABLED=true`

**3. Cloud Storage Authentication**
- Verify credentials and permissions
- Check network connectivity
- Review cloud provider documentation

**4. Cleanup Job Not Running**
- Check `ENABLE_AUTO_DELETE=true` in production
- Verify cron job is scheduled
- Check application logs for errors

### Log Files

Monitor these log files for issues:
- Application logs: `./data/logs/app-*.log`
- Error logs: `./data/logs/error-*.log`
- Docker logs: `docker-compose logs backend`

## Performance Optimization

### Recommendations

1. **Use SSD storage** for better I/O performance
2. **Enable compression** to reduce storage usage
3. **Adjust batch sizes** based on system resources
4. **Use cloud storage** for long-term archival
5. **Monitor cleanup job performance** and adjust intervals

### Scaling Considerations

- **Large deployments**: Use cloud storage backends
- **High volume**: Increase cleanup batch sizes and frequency
- **Compliance**: Use longer retention periods with archival
- **Cost optimization**: Use tiered storage (hot/cold)

## Security Considerations

1. **Encrypt archived logs** when using cloud storage
2. **Secure cloud credentials** using environment variables
3. **Limit access** to log storage directories
4. **Regular security audits** of retention policies
5. **Backup encryption** for sensitive log data

## Support

For additional support:
1. Check the application logs
2. Review this documentation
3. Contact the ExLog development team
4. Submit issues on the project repository

# AI Insights to Notifications Integration

## Overview

This document describes the integration between AI insights and the in-app notification system, enabling real-time notifications for AI-generated security alerts.

## How It Works

### 1. AI Analysis & Alert Generation
- AI insights service runs automatically every 5 minutes
- Analyzes logs from the last 24 hours (configurable)
- Detects security patterns and anomalies using MITRE ATT&CK framework
- Generates alerts for critical and high-severity findings

### 2. Backend Notification Creation
- AI alerts are sent to `/api/v1/ai/alerts` endpoint
- Correlation<PERSON><PERSON><PERSON> creates Alert records in database
- `createAlertNotifications()` automatically creates notifications for users with `view_alerts` permission
- NotificationService handles notification creation and user targeting

### 3. Real-time Delivery
- WebSocket broadcasts notifications to connected users
- NotificationBell component receives and displays notifications
- Notification count badge updates automatically
- Users see detailed alert information in notification dropdown

## User Permissions

For users to receive AI-generated alert notifications, they must have the `view_alerts` permission:

```javascript
// Required permission for AI alert notifications
user.permissions.includes('view_alerts')
```

## Testing the Integration

### 1. Verify AI Analysis is Running
```bash
# Check AI service logs
docker-compose logs ai-insights

# Look for periodic analysis messages
# "Running periodic analysis..."
# "Generated X AI-driven alerts"
```

### 2. Check Backend Notification Creation
```bash
# Check backend logs
docker-compose logs backend

# Look for notification creation messages
# "Received AI-generated alert: [title]"
# "Notification created for user: [userId]"
```

### 3. Verify Frontend Notifications
- Login to dashboard at http://localhost:3000
- Check notification bell in top-right corner
- Should show unread count for AI-generated alerts
- Click bell to see notification details

### 4. Generate Test Alerts
```bash
# Create test security logs to trigger AI analysis
curl -X POST http://localhost:5000/api/v1/logs \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "logs": [{
      "logId": "test-security-001",
      "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
      "level": "ERROR",
      "message": "Failed login attempt for admin user",
      "source": "auth-service",
      "metadata": {
        "user": "admin",
        "ip": "*************",
        "userAgent": "Mozilla/5.0"
      }
    }]
  }'
```

## Configuration

### AI Analysis Interval
```yaml
# docker-compose.yml
ai-insights:
  environment:
    - AI_ANALYSIS_INTERVAL=300  # 5 minutes (default)
```

### Default Analysis Timeframe
```javascript
// backend/src/models/AIConfig.js
timeRange: '24h'  // Analyze last 24 hours by default
```

### Notification Categories
AI-generated notifications use the following categories:
- `alert` - For security alerts and critical findings
- Severity levels: `critical`, `high`, `medium`, `low`

## WebSocket Integration

The notification system uses WebSocket for real-time delivery:

```javascript
// Backend: Send notification to user
sendNotificationToUser(userId, notification);

// Frontend: Receive via WebSocket
// NotificationBell component automatically updates
```

## Troubleshooting

### No Notifications Appearing
1. Check user has `view_alerts` permission
2. Verify AI service is running and generating alerts
3. Check backend logs for notification creation
4. Ensure WebSocket connection is active

### AI Not Generating Alerts
1. Verify AI service container is running
2. Check for sufficient log data (needs security-related logs)
3. Review AI analysis logs for pattern detection
4. Ensure backend `/api/v1/ai/alerts` endpoint is accessible

### Permission Issues
```bash
# Add view_alerts permission to user
db.users.updateOne(
  { email: "<EMAIL>" },
  { $addToSet: { permissions: "view_alerts" } }
)
```

## Integration Points

### Backend Services
- `CorrelationEngine` - Creates alerts and triggers notifications
- `NotificationService` - Handles notification creation and user targeting
- `WebSocket` - Broadcasts notifications to connected users

### Frontend Components
- `NotificationBell` - Displays notification count and dropdown
- `NotificationContext` - Manages notification state
- WebSocket client - Receives real-time notifications

### API Endpoints
- `POST /api/v1/ai/alerts` - Receives AI-generated alerts
- `GET /api/v1/notifications` - Fetches user notifications
- WebSocket connection for real-time updates

## Security Considerations

- AI service uses internal authentication key (`AI_SERVICE_KEY`)
- User permissions control notification visibility
- WebSocket connections are authenticated
- Notifications contain only necessary alert information

## Performance

- Notifications are created asynchronously to avoid blocking alert processing
- WebSocket broadcasts are efficient for real-time delivery
- Database queries are optimized for user permission checks
- Notification history is maintained for audit purposes

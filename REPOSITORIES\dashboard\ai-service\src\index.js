const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const mongoose = require('mongoose');
const winston = require('winston');
const cron = require('node-cron');

const AIAnalyzer = require('./services/aiAnalyzer');
const logger = require('./utils/logger');

const app = express();
const PORT = process.env.PORT || 5002;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog';

// Initialize AI Analyzer
let aiAnalyzer;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'exlog-ai-service',
    version: '1.0.0',
    aiAnalyzer: aiAnalyzer ? 'initialized' : 'initializing'
  });
});

// AI Analysis endpoints
app.post('/api/analyze', async (req, res) => {
  try {
    const { timeRange, logTypes, options } = req.body;
    
    if (!aiAnalyzer) {
      return res.status(503).json({ error: 'AI service is still initializing' });
    }

    const results = await aiAnalyzer.analyzeLogs(timeRange, logTypes, options);
    res.json(results);
  } catch (error) {
    logger.error('Analysis error:', error);
    res.status(500).json({ error: 'Analysis failed', message: error.message });
  }
});

app.get('/api/insights', async (req, res) => {
  try {
    if (!aiAnalyzer) {
      return res.status(503).json({ error: 'AI service is still initializing' });
    }

    const insights = await aiAnalyzer.getCurrentInsights();
    res.json(insights);
  } catch (error) {
    logger.error('Insights error:', error);
    res.status(500).json({ error: 'Failed to get insights', message: error.message });
  }
});

app.get('/api/threats', async (req, res) => {
  try {
    if (!aiAnalyzer) {
      return res.status(503).json({ error: 'AI service is still initializing' });
    }

    const threats = await aiAnalyzer.getThreatPredictions();
    res.json(threats);
  } catch (error) {
    logger.error('Threats error:', error);
    res.status(500).json({ error: 'Failed to get threats', message: error.message });
  }
});

app.get('/api/anomalies', async (req, res) => {
  try {
    const { limit = 50, offset = 0 } = req.query;
    
    if (!aiAnalyzer) {
      return res.status(503).json({ error: 'AI service is still initializing' });
    }

    const anomalies = await aiAnalyzer.getAnomalies(parseInt(limit), parseInt(offset));
    res.json(anomalies);
  } catch (error) {
    logger.error('Anomalies error:', error);
    res.status(500).json({ error: 'Failed to get anomalies', message: error.message });
  }
});

app.post('/api/feedback', async (req, res) => {
  try {
    const { anomalyId, feedback, isCorrect } = req.body;
    
    if (!aiAnalyzer) {
      return res.status(503).json({ error: 'AI service is still initializing' });
    }

    await aiAnalyzer.submitFeedback(anomalyId, feedback, isCorrect);
    res.json({ success: true, message: 'Feedback submitted successfully' });
  } catch (error) {
    logger.error('Feedback error:', error);
    res.status(500).json({ error: 'Failed to submit feedback', message: error.message });
  }
});

app.get('/api/performance', async (req, res) => {
  try {
    if (!aiAnalyzer) {
      return res.status(503).json({ error: 'AI service is still initializing' });
    }

    const performance = await aiAnalyzer.getPerformanceMetrics();
    res.json(performance);
  } catch (error) {
    logger.error('Performance error:', error);
    res.status(500).json({ error: 'Failed to get performance metrics', message: error.message });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error', message: error.message });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Initialize database connection and AI service
async function initialize() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    logger.info('Connected to MongoDB');

    // Initialize AI Analyzer
    aiAnalyzer = new AIAnalyzer();
    await aiAnalyzer.initialize();
    logger.info('AI Analyzer initialized successfully');

    // Schedule periodic analysis (every 5 minutes)
    const analysisInterval = process.env.ANALYSIS_INTERVAL || 300;
    cron.schedule(`*/${Math.floor(analysisInterval/60)} * * * *`, async () => {
      try {
        logger.info('Running scheduled AI analysis...');
        await aiAnalyzer.runPeriodicAnalysis();
      } catch (error) {
        logger.error('Scheduled analysis failed:', error);
      }
    });

    // Start server
    app.listen(PORT, () => {
      logger.info(`AI Service running on port ${PORT}`);
    });

  } catch (error) {
    logger.error('Failed to initialize AI service:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully...');
  await mongoose.connection.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully...');
  await mongoose.connection.close();
  process.exit(0);
});

// Start the service
initialize();

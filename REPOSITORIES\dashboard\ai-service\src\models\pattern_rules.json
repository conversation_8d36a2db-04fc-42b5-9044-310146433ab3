{"mitre_attack_patterns": {"T1078": {"name": "Valid Accounts", "patterns": ["multiple failed login attempts", "login from unusual location", "privilege escalation", "account lockout", "password spray"], "severity": "high", "category": "initial_access"}, "T1055": {"name": "Process Injection", "patterns": ["suspicious process creation", "dll injection", "process hollowing", "thread execution hijacking"], "severity": "critical", "category": "defense_evasion"}, "T1059": {"name": "Command and Scripting Interpreter", "patterns": ["powershell execution", "cmd.exe suspicious", "script execution", "command line injection"], "severity": "high", "category": "execution"}, "T1083": {"name": "File and Directory Discovery", "patterns": ["directory enumeration", "file system scanning", "reconnaissance activity"], "severity": "medium", "category": "discovery"}, "T1105": {"name": "Ingress Tool Transfer", "patterns": ["file download", "tool transfer", "payload delivery", "remote file access"], "severity": "high", "category": "command_and_control"}}, "custom_security_rules": {"brute_force_detection": {"condition": "failed_login_count > 5 AND time_window < 300", "severity": "high", "description": "Potential brute force attack detected", "category": "authentication"}, "data_exfiltration": {"condition": "large_file_transfer AND unusual_time", "severity": "critical", "description": "Potential data exfiltration detected", "category": "data_theft"}, "privilege_escalation": {"condition": "admin_access AND non_admin_user", "severity": "critical", "description": "Unauthorized privilege escalation detected", "category": "privilege_escalation"}, "malware_indicators": {"condition": "suspicious_process OR malicious_file", "severity": "critical", "description": "Malware activity indicators detected", "category": "malware"}, "network_anomaly": {"condition": "unusual_network_traffic OR port_scanning", "severity": "medium", "description": "Network anomaly detected", "category": "network"}}}
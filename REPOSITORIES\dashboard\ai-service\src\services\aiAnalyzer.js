const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const mongoose = require('mongoose');
const logger = require('../utils/logger');
const AnomalyDetector = require('./anomalyDetector');
const ThreatPredictor = require('./threatPredictor');
const PatternMatcher = require('./patternMatcher');
const { v4: uuidv4 } = require('uuid');

class AIAnalyzer {
  constructor() {
    this.anomalyDetector = null;
    this.threatPredictor = null;
    this.patternMatcher = null;
    this.isInitialized = false;
    this.analysisCache = new Map();
    this.performanceMetrics = {
      totalAnalyses: 0,
      averageResponseTime: 0,
      anomaliesDetected: 0,
      threatsIdentified: 0,
      lastAnalysis: null,
      uptime: Date.now()
    };
  }

  async initialize() {
    try {
      logger.info('Initializing AI Analyzer...');

      // Initialize sub-services
      this.anomalyDetector = new AnomalyDetector();
      await this.anomalyDetector.initialize();

      this.threatPredictor = new ThreatPredictor();
      await this.threatPredictor.initialize();

      this.patternMatcher = new PatternMatcher();
      await this.patternMatcher.initialize();

      this.isInitialized = true;
      logger.info('AI Analyzer initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Analyzer:', error);
      throw error;
    }
  }

  async analyzeLogs(timeRange = '1h', logTypes = [], options = {}) {
    const startTime = Date.now();

    try {
      if (!this.isInitialized) {
        throw new Error('AI Analyzer not initialized');
      }

      logger.info(`Starting log analysis for timeRange: ${timeRange}, logTypes: ${logTypes.join(',')}`);

      // 1. Fetch logs from MongoDB
      const logs = await this.fetchLogs(timeRange, logTypes);

      if (logs.length === 0) {
        return {
          summary: {
            totalLogs: 0,
            anomalies: 0,
            threats: 0,
            riskLevel: 'low'
          },
          anomalies: [],
          threats: [],
          patterns: [],
          timestamp: new Date().toISOString()
        };
      }

      // 2. Run parallel analysis
      const [anomalies, threats, patterns] = await Promise.all([
        this.anomalyDetector.detectAnomalies(logs),
        this.threatPredictor.predictThreats(logs),
        this.patternMatcher.matchPatterns(logs)
      ]);

      // 3. Calculate overall risk level
      const riskLevel = this.calculateRiskLevel(anomalies, threats, patterns);

      // 4. Generate insights
      const insights = {
        summary: {
          totalLogs: logs.length,
          anomalies: anomalies.length,
          threats: threats.length,
          patterns: patterns.length,
          riskLevel,
          analysisTime: Date.now() - startTime
        },
        anomalies: anomalies.slice(0, 20), // Limit to top 20
        threats: threats.slice(0, 10),     // Limit to top 10
        patterns: patterns.slice(0, 15),   // Limit to top 15
        timestamp: new Date().toISOString(),
        recommendations: this.generateRecommendations(anomalies, threats, patterns)
      };

      // 5. Update performance metrics
      this.updatePerformanceMetrics(insights, Date.now() - startTime);

      // 6. Cache results
      const cacheKey = `${timeRange}-${logTypes.join(',')}-${JSON.stringify(options)}`;
      this.analysisCache.set(cacheKey, {
        data: insights,
        timestamp: Date.now(),
        ttl: 300000 // 5 minutes
      });

      logger.info(`Analysis completed in ${Date.now() - startTime}ms`);
      return insights;

    } catch (error) {
      logger.error('Analysis failed:', error);
      throw error;
    }
  }

  /**
   * Enhanced analysis method that works with AI configuration and stores results
   */
  async analyzeWithConfig(config, analysisResult = null) {
    const startTime = Date.now();
    const analysisId = analysisResult?.analysisId || uuidv4();

    try {
      if (!this.isInitialized) {
        throw new Error('AI Analyzer not initialized');
      }

      logger.info(`Starting configurable analysis ${analysisId} with config: ${config.name}`);

      // Extract analysis parameters from config
      const {
        timeRange,
        enabledLogTypes,
        maxLogsPerAnalysis
      } = config.analysisSettings;

      // 1. Fetch logs based on configuration
      const logs = await this.fetchLogsWithConfig(config);

      if (logs.length === 0) {
        const emptyResults = {
          summary: {
            totalLogs: 0,
            anomalies: 0,
            threats: 0,
            patterns: 0,
            riskLevel: 'low',
            confidence: 0
          },
          anomalies: [],
          threats: [],
          patterns: [],
          performance: this.getPerformanceMetrics(startTime),
          timestamp: new Date().toISOString()
        };

        if (analysisResult) {
          await analysisResult.markCompleted(emptyResults);
        }

        return emptyResults;
      }

      // 2. Run analysis based on enabled features
      const analysisPromises = [];

      if (config.anomalyDetection.enabled) {
        analysisPromises.push(this.anomalyDetector.detectAnomalies(logs));
      } else {
        analysisPromises.push(Promise.resolve([]));
      }

      if (config.threatPrediction.enabled) {
        analysisPromises.push(this.threatPredictor.predictThreats(logs));
      } else {
        analysisPromises.push(Promise.resolve([]));
      }

      if (config.patternMatching.enabled) {
        analysisPromises.push(this.patternMatcher.matchPatterns(logs));
      } else {
        analysisPromises.push(Promise.resolve([]));
      }

      const [anomalies, threats, patterns] = await Promise.all(analysisPromises);

      // 3. Filter results based on configuration thresholds
      const filteredAnomalies = this.filterAnomalies(anomalies, config.anomalyDetection);
      const filteredThreats = this.filterThreats(threats, config.threatPrediction);
      const filteredPatterns = this.filterPatterns(patterns, config.patternMatching);

      // 4. Calculate overall risk level and confidence
      const riskLevel = this.calculateRiskLevel(filteredAnomalies, filteredThreats, filteredPatterns);
      const confidence = this.calculateConfidence(filteredAnomalies, filteredThreats, filteredPatterns);

      // 5. Generate comprehensive results
      const results = {
        summary: {
          totalLogs: logs.length,
          anomalies: filteredAnomalies.length,
          threats: filteredThreats.length,
          patterns: filteredPatterns.length,
          riskLevel,
          confidence
        },
        anomalies: filteredAnomalies,
        threats: filteredThreats,
        patterns: filteredPatterns,
        performance: this.getPerformanceMetrics(startTime),
        timestamp: new Date().toISOString(),
        recommendations: this.generateRecommendations(filteredAnomalies, filteredThreats, filteredPatterns)
      };

      // 6. Update analysis result if provided
      if (analysisResult) {
        analysisResult.execution.processedLogs = logs.length;
        await analysisResult.markCompleted(results);
      }

      // 7. Update performance metrics
      this.updatePerformanceMetrics(results, Date.now() - startTime);

      logger.info(`Configurable analysis ${analysisId} completed in ${Date.now() - startTime}ms`);
      return results;

    } catch (error) {
      logger.error(`Configurable analysis ${analysisId} failed:`, error);

      if (analysisResult) {
        await analysisResult.markFailed(error.message);
      }

      throw error;
    }
  }

  async fetchLogs(timeRange, logTypes) {
    try {
      // Parse time range
      const timeRangeMs = this.parseTimeRange(timeRange);
      const startTime = new Date(Date.now() - timeRangeMs);

      // Build query
      const query = {
        timestamp: { $gte: startTime }
      };

      if (logTypes && logTypes.length > 0) {
        query.logLevel = { $in: logTypes };
      }

      // Get logs collection from the main database
      const db = mongoose.connection.db;
      const logsCollection = db.collection('logs');

      const logs = await logsCollection
        .find(query)
        .sort({ timestamp: -1 })
        .limit(10000) // Limit to prevent memory issues
        .toArray();

      logger.info(`Fetched ${logs.length} logs for analysis`);
      return logs;

    } catch (error) {
      logger.error('Error fetching logs:', error);
      throw error;
    }
  }

  async fetchLogsWithConfig(config) {
    try {
      const {
        timeRange,
        enabledLogTypes,
        maxLogsPerAnalysis
      } = config.analysisSettings;

      // Parse time range
      const timeRangeMs = this.parseTimeRange(timeRange);
      const startTime = new Date(Date.now() - timeRangeMs);

      // Build query
      const query = {
        timestamp: { $gte: startTime }
      };

      if (enabledLogTypes && enabledLogTypes.length > 0) {
        query.logLevel = { $in: enabledLogTypes };
      }

      // Get logs collection from the main database
      const db = mongoose.connection.db;
      const logsCollection = db.collection('logs');

      const logs = await logsCollection
        .find(query)
        .sort({ timestamp: -1 })
        .limit(maxLogsPerAnalysis || 10000)
        .toArray();

      logger.info(`Fetched ${logs.length} logs for configurable analysis`);
      return logs;

    } catch (error) {
      logger.error('Error fetching logs with config:', error);
      throw error;
    }
  }

  filterAnomalies(anomalies, anomalyConfig) {
    if (!anomalyConfig.enabled) return [];

    return anomalies.filter(anomaly => {
      // Filter by threshold
      if (anomaly.anomalyScore < anomalyConfig.threshold) return false;

      // Filter by minimum count for alerts
      return true; // Individual anomalies don't have counts
    }).slice(0, 50); // Limit results
  }

  filterThreats(threats, threatConfig) {
    if (!threatConfig.enabled) return [];

    return threats.filter(threat => {
      // Filter by confidence threshold
      if (threat.confidence < threatConfig.confidenceThreshold) return false;

      // Filter by enabled patterns
      if (threatConfig.enabledPatterns && threatConfig.enabledPatterns.length > 0) {
        return threatConfig.enabledPatterns.includes(threat.type);
      }

      return true;
    }).slice(0, 25); // Limit results
  }

  filterPatterns(patterns, patternConfig) {
    if (!patternConfig.enabled) return [];

    return patterns.filter(pattern => {
      // Apply any pattern-specific filtering here
      return true;
    }).slice(0, 30); // Limit results
  }

  calculateConfidence(anomalies, threats, patterns) {
    if (anomalies.length === 0 && threats.length === 0 && patterns.length === 0) {
      return 0;
    }

    // Calculate weighted confidence based on findings
    let totalConfidence = 0;
    let totalWeight = 0;

    // Anomalies contribute to confidence
    anomalies.forEach(anomaly => {
      if (anomaly.confidence) {
        totalConfidence += anomaly.confidence * 0.3; // 30% weight
        totalWeight += 0.3;
      }
    });

    // Threats contribute more to confidence
    threats.forEach(threat => {
      if (threat.confidence) {
        totalConfidence += threat.confidence * 0.5; // 50% weight
        totalWeight += 0.5;
      }
    });

    // Patterns contribute to confidence
    patterns.forEach(pattern => {
      if (pattern.confidence) {
        totalConfidence += pattern.confidence * 0.2; // 20% weight
        totalWeight += 0.2;
      }
    });

    return totalWeight > 0 ? Math.round(totalConfidence / totalWeight) : 0;
  }

  getPerformanceMetrics(startTime) {
    const duration = Date.now() - startTime;

    return {
      memoryUsage: {
        peak: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
        average: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) // MB
      },
      cpuUsage: {
        peak: 0, // Would need more sophisticated monitoring
        average: 0
      },
      processingRate: 0, // Will be calculated by caller
      cacheHitRate: this.calculateCacheHitRate(),
      duration
    };
  }

  calculateCacheHitRate() {
    // Simple cache hit rate calculation
    const totalRequests = this.performanceMetrics.totalAnalyses;
    const cacheSize = this.analysisCache.size;

    if (totalRequests === 0) return 0;
    return Math.round((cacheSize / totalRequests) * 100) / 100;
  }

  parseTimeRange(timeRange) {
    const timeMap = {
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '12h': 12 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000
    };

    return timeMap[timeRange] || timeMap['1h'];
  }

  calculateRiskLevel(anomalies, threats, patterns) {
    let score = 0;

    // Weight anomalies
    anomalies.forEach(anomaly => {
      if (anomaly.riskLevel === 'critical') score += 10;
      else if (anomaly.riskLevel === 'high') score += 5;
      else if (anomaly.riskLevel === 'medium') score += 2;
      else score += 1;
    });

    // Weight threats
    threats.forEach(threat => {
      if (threat.severity === 'critical') score += 15;
      else if (threat.severity === 'high') score += 8;
      else if (threat.severity === 'medium') score += 3;
      else score += 1;
    });

    // Weight patterns
    patterns.forEach(pattern => {
      if (pattern.severity === 'critical') score += 12;
      else if (pattern.severity === 'high') score += 6;
      else if (pattern.severity === 'medium') score += 2;
      else score += 1;
    });

    // Determine risk level
    if (score >= 50) return 'critical';
    if (score >= 25) return 'high';
    if (score >= 10) return 'medium';
    return 'low';
  }

  generateRecommendations(anomalies, threats, patterns) {
    const recommendations = [];

    // Anomaly-based recommendations
    if (anomalies.length > 10) {
      recommendations.push({
        type: 'anomaly',
        priority: 'high',
        message: 'High number of anomalies detected. Consider reviewing system configurations.',
        action: 'review_system_config'
      });
    }

    // Threat-based recommendations
    const criticalThreats = threats.filter(t => t.severity === 'critical');
    if (criticalThreats.length > 0) {
      recommendations.push({
        type: 'threat',
        priority: 'critical',
        message: `${criticalThreats.length} critical threats detected. Immediate action required.`,
        action: 'immediate_response'
      });
    }

    // Pattern-based recommendations
    const attackPatterns = patterns.filter(p => p.category === 'attack');
    if (attackPatterns.length > 0) {
      recommendations.push({
        type: 'pattern',
        priority: 'high',
        message: 'Attack patterns detected. Review security measures.',
        action: 'security_review'
      });
    }

    return recommendations;
  }

  updatePerformanceMetrics(insights, responseTime) {
    this.performanceMetrics.totalAnalyses++;
    this.performanceMetrics.averageResponseTime =
      (this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalAnalyses - 1) + responseTime) /
      this.performanceMetrics.totalAnalyses;
    this.performanceMetrics.anomaliesDetected += insights.anomalies.length;
    this.performanceMetrics.threatsIdentified += insights.threats.length;
    this.performanceMetrics.lastAnalysis = new Date().toISOString();
  }

  async getCurrentInsights() {
    // Return cached insights or run new analysis
    const recentCache = Array.from(this.analysisCache.values())
      .filter(cache => Date.now() - cache.timestamp < cache.ttl)
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    if (recentCache) {
      return recentCache.data;
    }

    // Try to get from database
    try {
      const db = mongoose.connection.db;
      const insightsCollection = db.collection('ai_insights');
      const recentInsight = await insightsCollection.findOne(
        {},
        { sort: { timestamp: -1 } }
      );

      if (recentInsight) {
        // Remove MongoDB _id field and return
        const { _id, ...insights } = recentInsight;
        return insights;
      }
    } catch (error) {
      logger.warn('Failed to get insights from database, running fresh analysis:', error);
    }

    // Run new analysis with default parameters
    return await this.analyzeLogs('1h', []);
  }

  async getThreatPredictions() {
    if (!this.threatPredictor) {
      throw new Error('Threat predictor not initialized');
    }

    const logs = await this.fetchLogs('2h', []);
    return await this.threatPredictor.predictThreats(logs);
  }

  async getAnomalies(limit = 50, offset = 0) {
    if (!this.anomalyDetector) {
      throw new Error('Anomaly detector not initialized');
    }

    // First try to get from recent cache
    const recentCache = Array.from(this.analysisCache.values())
      .filter(cache => Date.now() - cache.timestamp < cache.ttl)
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    let anomalies = [];
    if (recentCache && recentCache.data.anomalies) {
      anomalies = recentCache.data.anomalies;
    } else {
      // If no cache, try to get from database
      try {
        const db = mongoose.connection.db;
        const insightsCollection = db.collection('ai_insights');
        const recentInsight = await insightsCollection.findOne(
          {},
          { sort: { timestamp: -1 } }
        );

        if (recentInsight && recentInsight.anomalies) {
          anomalies = recentInsight.anomalies;
        } else {
          // Last resort: run fresh analysis
          const logs = await this.fetchLogs('24h', []);
          anomalies = await this.anomalyDetector.detectAnomalies(logs);
        }
      } catch (error) {
        logger.warn('Failed to get anomalies from database, running fresh analysis:', error);
        const logs = await this.fetchLogs('24h', []);
        anomalies = await this.anomalyDetector.detectAnomalies(logs);
      }
    }

    return {
      anomalies: anomalies.slice(offset, offset + limit),
      total: anomalies.length,
      limit,
      offset
    };
  }

  async submitFeedback(anomalyId, feedback, isCorrect) {
    // Store feedback for model improvement
    const db = mongoose.connection.db;
    const feedbackCollection = db.collection('ai_feedback');

    await feedbackCollection.insertOne({
      anomalyId,
      feedback,
      isCorrect,
      timestamp: new Date(),
      processed: false
    });

    logger.info(`Feedback submitted for anomaly ${anomalyId}: ${isCorrect ? 'correct' : 'incorrect'}`);
  }

  async getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      uptime: Date.now() - this.performanceMetrics.uptime,
      cacheSize: this.analysisCache.size,
      memoryUsage: process.memoryUsage(),
      isInitialized: this.isInitialized
    };
  }

  async runPeriodicAnalysis() {
    try {
      logger.info('Running periodic analysis...');

      // Get active configuration
      const db = mongoose.connection.db;
      const configCollection = db.collection('ai_config');
      const activeConfig = await configCollection.findOne({ isActive: true });

      if (!activeConfig) {
        logger.warn('No active AI configuration found for periodic analysis');
        const insights = await this.analyzeLogs('24h', []);
        
        // Store insights in database for historical tracking
        const insightsCollection = db.collection('ai_insights');
        await insightsCollection.insertOne({
          ...insights,
          type: 'periodic',
          createdAt: new Date()
        });

        // Generate alerts based on insights
        await this.generateAIAlerts(insights);
        
        logger.info('Periodic analysis completed successfully with default settings');
        return insights;
      }

      // Run analysis with configuration
      const results = await this.analyzeWithConfig(activeConfig);

      // Generate alerts for critical findings
      await this.generateAIAlerts(results);

      logger.info('Periodic analysis completed successfully');
      return results;

    } catch (error) {
      logger.error('Periodic analysis failed:', error);
      throw error;
    }
  }

  async generateAIAlerts(insights) {
    try {
      logger.info('generateAIAlerts called with insights:', JSON.stringify(insights, null, 2));
      const alerts = [];

      // Generate alerts for high-risk anomalies
      if (insights.anomalies && insights.anomalies.length > 0) {
        for (const anomaly of insights.anomalies) {
          if (anomaly.confidence > 70 || anomaly.riskLevel === 'high' || anomaly.riskLevel === 'critical') {
            alerts.push({
              type: 'anomaly',
              severity: this.mapRiskToSeverity(anomaly.riskLevel),
              title: `High-Confidence Anomaly Detected`,
              message: `${anomaly.description} (Confidence: ${anomaly.confidence}%)`,
              source: 'ai_anomaly_detection',
              metadata: {
                anomalyId: anomaly.id,
                confidence: anomaly.confidence,
                riskLevel: anomaly.riskLevel,
                logSource: anomaly.logSource,
                timestamp: anomaly.timestamp
              }
            });
          }
        }
      }

      // Generate alerts for critical security patterns
      if (insights.patterns && insights.patterns.length > 0) {
        logger.info(`Processing ${insights.patterns.length} security patterns for alert generation`);
        for (const pattern of insights.patterns) {
          logger.info(`Checking pattern: ${pattern.name}, severity: ${pattern.severity}`);
          if (pattern.severity === 'critical' || pattern.severity === 'high') {
            alerts.push({
              type: 'security_pattern',
              severity: pattern.severity,
              title: `Security Pattern Detected: ${pattern.name}`,
              message: pattern.description,
              source: 'ai_security_analysis',
              metadata: {
                patternId: pattern.id,
                patternType: pattern.type,
                mitreId: pattern.mitreId,
                confidence: pattern.confidence,
                indicators: pattern.indicators
              }
            });
          }
        }
      }

      // Generate alerts for elevated threat levels
      if (insights.summary && insights.summary.riskLevel) {
        const riskLevel = insights.summary.riskLevel.toLowerCase();
        if (riskLevel === 'high' || riskLevel === 'critical') {
          alerts.push({
            type: 'threat_level',
            severity: riskLevel,
            title: `Elevated Threat Level: ${insights.summary.riskLevel}`,
            message: `AI analysis indicates ${riskLevel} threat level based on ${insights.summary.logsAnalyzed} logs analyzed.`,
            source: 'ai_threat_assessment',
            metadata: {
              riskScore: insights.summary.riskScore,
              logsAnalyzed: insights.summary.logsAnalyzed,
              analysisTime: insights.summary.analysisTime,
              threatCount: insights.threats ? insights.threats.length : 0
            }
          });
        }
      }

      // Send alerts to backend notification service
      if (alerts.length > 0) {
        logger.info(`Sending ${alerts.length} alerts to backend`);
        await this.sendAlertsToBackend(alerts);
        logger.info(`Generated ${alerts.length} AI-driven alerts`);
      } else {
        logger.warn('No alerts generated despite having insights data');
      }

    } catch (error) {
      logger.error('Error generating AI alerts:', error);
    }
  }

  mapRiskToSeverity(riskLevel) {
    const mapping = {
      'low': 'low',
      'medium': 'medium',
      'high': 'high',
      'critical': 'critical'
    };
    return mapping[riskLevel?.toLowerCase()] || 'medium';
  }

  async sendAlertsToBackend(alerts) {
    try {
      const axios = require('axios');
      const backendUrl = process.env.BACKEND_URL || 'http://backend:5000';

      for (const alert of alerts) {
        try {
          await axios.post(`${backendUrl}/api/v1/ai/alerts`, alert, {
            headers: {
              'Content-Type': 'application/json',
              'X-AI-Service-Key': process.env.AI_SERVICE_KEY || 'ai-service-internal-key'
            },
            timeout: 5000
          });
          logger.debug(`Sent AI alert: ${alert.title}`);
        } catch (error) {
          logger.error(`Failed to send alert to backend: ${alert.title}`, error.message);
        }
      }
    } catch (error) {
      logger.error('Error sending alerts to backend:', error);
    }
  }
}

module.exports = AIAnalyzer;

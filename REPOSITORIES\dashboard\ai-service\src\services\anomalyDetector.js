const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const logger = require('../utils/logger');

class AnomalyDetector {
  constructor() {
    this.modelPath = path.join(__dirname, '../models/isolation_forest.py');
    this.isInitialized = false;
    this.featureExtractor = null;
  }

  async initialize() {
    try {
      logger.info('Initializing Anomaly Detector...');

      // Ensure Python model file exists
      await this.ensureModelExists();

      // Initialize feature extraction rules
      this.initializeFeatureExtractor();

      this.isInitialized = true;
      logger.info('Anomaly Detector initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Anomaly Detector:', error);
      throw error;
    }
  }

  async ensureModelExists() {
    const modelExists = await fs.pathExists(this.modelPath);
    if (!modelExists) {
      await this.createPythonModel();
    }
  }

  async createPythonModel() {
    const pythonCode = `import json
import sys
import re
from datetime import datetime
import math
import statistics
import numpy as np
from sklearn.ensemble import IsolationForest
from sklearn.feature_extraction.text import TfidfVectorizer

class SecurityAnomalyDetector:
    def __init__(self):
        self.isolation_forest = IsolationForest(
            contamination=0.1,
            random_state=42,
            n_estimators=100,
            max_samples='auto'
        )
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=500,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.is_fitted = False
        
    def extract_features(self, log):
        """Extract numerical and categorical features from a log entry"""
        features = []
        
        # Basic features
        message = log.get('message', '')
        features.append(len(message))  # Message length
        features.append(log.get('severity', 0))  # Severity level
        
        # Log level encoding
        log_level = log.get('logLevel', 'info').lower()
        level_map = {'debug': 0, 'info': 1, 'warn': 2, 'error': 3, 'fatal': 4}
        features.append(level_map.get(log_level, 1))
        
        # Source type encoding
        source_type = log.get('sourceType', 'unknown').lower()
        source_map = {'system': 0, 'application': 1, 'security': 2, 'network': 3, 'unknown': 4}
        features.append(source_map.get(source_type, 4))
        
        # Time-based features
        timestamp = log.get('timestamp')
        if timestamp:
            try:
                if isinstance(timestamp, str):
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                else:
                    dt = timestamp
                features.append(dt.hour)  # Hour of day
                features.append(dt.weekday())  # Day of week
            except:
                features.append(12)  # Default hour
                features.append(0)   # Default weekday
        else:
            features.append(12)
            features.append(0)
        
        # Security-specific features
        features.append(self.count_suspicious_keywords(message))
        features.append(self.count_ip_addresses(message))
        features.append(self.count_file_paths(message))
        features.append(int('failed' in message.lower()))
        features.append(int('error' in message.lower()))
        features.append(int('unauthorized' in message.lower()))
        features.append(int('denied' in message.lower()))
        
        return features
    
    def count_suspicious_keywords(self, message):
        """Count suspicious security-related keywords"""
        keywords = ['attack', 'breach', 'malware', 'virus', 'hack', 'exploit', 
                   'injection', 'overflow', 'backdoor', 'trojan', 'phishing']
        return sum(1 for keyword in keywords if keyword in message.lower())
    
    def count_ip_addresses(self, message):
        """Count IP addresses in the message"""
        ip_pattern = r'\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b'
        return len(re.findall(ip_pattern, message))
    
    def count_file_paths(self, message):
        """Count file paths in the message"""
        path_patterns = [r'/[\\w/.-]+', r'[A-Z]:\\\\[\\w\\\\.-]+', r'\\\\[\\w.-]+\\\\[\\w\\\\.-]+']
        count = 0
        for pattern in path_patterns:
            count += len(re.findall(pattern, message))
        return count
    
    def preprocess_logs(self, logs):
        """Extract features from all logs"""
        features = []
        for log in logs:
            feature_vector = self.extract_features(log)
            features.append(feature_vector)
        return np.array(features)
    
    def detect_anomalies(self, logs):
        """Detect anomalies in the provided logs"""
        if len(logs) < 10:
            return []  # Need minimum logs for meaningful analysis
        
        try:
            # Extract features
            features = self.preprocess_logs(logs)
            
            # Fit the model if not already fitted
            if not self.is_fitted:
                self.isolation_forest.fit(features)
                self.is_fitted = True
            
            # Predict anomalies
            anomaly_scores = self.isolation_forest.decision_function(features)
            anomalies = self.isolation_forest.predict(features)
            
            results = []
            for i, (log, score, is_anomaly) in enumerate(zip(logs, anomaly_scores, anomalies)):
                if is_anomaly == -1:  # Anomaly detected
                    risk_level = self.calculate_risk_level(score)
                    description = self.generate_anomaly_description(log, score)
                    
                    results.append({
                        'id': f"anomaly_{i}_{int(datetime.now().timestamp())}",
                        'logId': log.get('_id', str(i)),
                        'anomalyScore': float(score),
                        'riskLevel': risk_level,
                        'description': description,
                        'timestamp': log.get('timestamp'),
                        'message': log.get('message', ''),
                        'logLevel': log.get('logLevel', 'info'),
                        'sourceType': log.get('sourceType', 'unknown'),
                        'recommendations': self.generate_recommendations(log, score, risk_level),
                        'confidence': self.calculate_confidence(score)
                    })
            
            # Sort by anomaly score (most anomalous first)
            results.sort(key=lambda x: x['anomalyScore'])
            
            return results
            
        except Exception as e:
            print(f"Error in anomaly detection: {str(e)}", file=sys.stderr)
            return []
    
    def calculate_risk_level(self, score):
        """Calculate risk level based on anomaly score"""
        if score < -0.5:
            return 'critical'
        elif score < -0.3:
            return 'high'
        elif score < -0.1:
            return 'medium'
        else:
            return 'low'
    
    def calculate_confidence(self, score):
        """Calculate confidence level for the anomaly detection"""
        # Normalize score to 0-1 range
        normalized = max(0, min(1, (abs(score) + 0.5) / 1.0))
        return round(normalized * 100, 2)
    
    def generate_anomaly_description(self, log, score):
        """Generate human-readable description of the anomaly"""
        message = log.get('message', '')
        log_level = log.get('logLevel', 'info')
        
        descriptions = []
        
        if score < -0.5:
            descriptions.append("Highly unusual log pattern detected")
        elif score < -0.3:
            descriptions.append("Suspicious activity pattern identified")
        else:
            descriptions.append("Anomalous behavior detected")
        
        if log_level in ['error', 'fatal']:
            descriptions.append("associated with error conditions")
        
        if self.count_suspicious_keywords(message) > 0:
            descriptions.append("containing security-related keywords")
        
        if self.count_ip_addresses(message) > 0:
            descriptions.append("involving IP address activity")
        
        return "; ".join(descriptions)
    
    def generate_recommendations(self, log, score, risk_level):
        """Generate recommendations based on the anomaly"""
        recommendations = []
        
        if risk_level in ['critical', 'high']:
            recommendations.append("Immediate investigation required")
            recommendations.append("Review related system logs")
        
        if 'failed' in log.get('message', '').lower():
            recommendations.append("Check authentication systems")
        
        if 'error' in log.get('message', '').lower():
            recommendations.append("Investigate error conditions")
        
        if self.count_ip_addresses(log.get('message', '')) > 0:
            recommendations.append("Verify IP address legitimacy")
        
        recommendations.append("Monitor for similar patterns")
        
        return recommendations

if __name__ == "__main__":
    try:
        # Read input from command line argument
        input_data = json.loads(sys.argv[1])
        
        # Initialize detector
        detector = SecurityAnomalyDetector()
        
        # Detect anomalies
        results = detector.detect_anomalies(input_data.get('logs', []))
        
        # Output results
        print(json.dumps(results, default=str))
        
    except Exception as e:
        print(json.dumps({"error": str(e)}), file=sys.stderr)
        sys.exit(1)
`;

    await fs.ensureDir(path.dirname(this.modelPath));
    await fs.writeFile(this.modelPath, pythonCode);
    logger.info('Python anomaly detection model created');
  }

  initializeFeatureExtractor() {
    this.featureExtractor = {
      logLevelWeights: {
        'debug': 1,
        'info': 2,
        'warn': 4,
        'error': 8,
        'fatal': 16
      },
      sourceTypeWeights: {
        'system': 1,
        'application': 2,
        'security': 4,
        'network': 3,
        'unknown': 2
      },
      suspiciousKeywords: [
        'attack', 'breach', 'malware', 'virus', 'hack', 'exploit',
        'injection', 'overflow', 'backdoor', 'trojan', 'phishing',
        'unauthorized', 'denied', 'failed', 'blocked', 'suspicious'
      ]
    };
  }

  async detectAnomalies(logs) {
    try {
      if (!this.isInitialized) {
        throw new Error('Anomaly detector not initialized');
      }

      if (!logs || logs.length === 0) {
        return [];
      }

      logger.info(`Detecting anomalies in ${logs.length} logs`);

      // Prepare data for Python script
      const inputData = {
        logs: logs.map(log => ({
          _id: log._id ? log._id.toString() : undefined,
          message: log.message || '',
          logLevel: log.logLevel || 'info',
          sourceType: log.sourceType || 'unknown',
          severity: log.severity || 0,
          timestamp: log.timestamp || new Date().toISOString()
        }))
      };

      // Run Python anomaly detection
      const results = await this.runPythonAnalysis(inputData);

      logger.info(`Detected ${results.length} anomalies`);
      return results;

    } catch (error) {
      logger.error('Anomaly detection failed:', error);
      throw error;
    }
  }

  async runPythonAnalysis(inputData) {
    return new Promise((resolve, reject) => {
      const python = spawn('python3', [this.modelPath, JSON.stringify(inputData)]);

      let stdout = '';
      let stderr = '';

      python.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      python.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      python.on('close', (code) => {
        if (code !== 0) {
          logger.error(`Python script failed with code ${code}: ${stderr}`);
          reject(new Error(`Anomaly detection failed: ${stderr}`));
          return;
        }

        try {
          const results = JSON.parse(stdout);
          if (results.error) {
            reject(new Error(results.error));
          } else {
            resolve(results);
          }
        } catch (parseError) {
          logger.error('Failed to parse Python output:', parseError);
          reject(new Error('Failed to parse anomaly detection results'));
        }
      });

      python.on('error', (error) => {
        logger.error('Failed to spawn Python process:', error);
        reject(new Error('Failed to run anomaly detection'));
      });
    });
  }
}

module.exports = AnomalyDetector;

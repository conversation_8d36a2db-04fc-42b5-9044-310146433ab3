const fs = require('fs-extra');
const path = require('path');
const logger = require('../utils/logger');

class PatternMatcher {
  constructor() {
    this.isInitialized = false;
    this.securityRules = null;
    this.mitrePatterns = null;
    this.customRules = null;
  }

  async initialize() {
    try {
      logger.info('Initializing Pattern Matcher...');
      
      await this.loadSecurityRules();
      this.initializeMitrePatterns();
      this.initializeCustomRules();
      
      this.isInitialized = true;
      logger.info('Pattern Matcher initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Pattern Matcher:', error);
      throw error;
    }
  }

  async loadSecurityRules() {
    const rulesPath = path.join(__dirname, '../models/pattern_rules.json');
    
    try {
      if (await fs.pathExists(rulesPath)) {
        const rulesData = await fs.readJson(rulesPath);
        this.securityRules = rulesData;
      } else {
        await this.createDefaultRules(rulesPath);
      }
    } catch (error) {
      logger.warn('Failed to load security rules, using defaults:', error);
      await this.createDefaultRules(rulesPath);
    }
  }

  async createDefaultRules(rulesPath) {
    const defaultRules = {
      mitre_attack_patterns: {
        "T1078": {
          name: "Valid Accounts",
          patterns: [
            "multiple failed login attempts",
            "login from unusual location",
            "privilege escalation",
            "account lockout",
            "password spray"
          ],
          severity: "high",
          category: "initial_access"
        },
        "T1055": {
          name: "Process Injection",
          patterns: [
            "suspicious process creation",
            "dll injection",
            "process hollowing",
            "thread execution hijacking"
          ],
          severity: "critical",
          category: "defense_evasion"
        },
        "T1059": {
          name: "Command and Scripting Interpreter",
          patterns: [
            "powershell execution",
            "cmd.exe suspicious",
            "script execution",
            "command line injection"
          ],
          severity: "high",
          category: "execution"
        },
        "T1083": {
          name: "File and Directory Discovery",
          patterns: [
            "directory enumeration",
            "file system scanning",
            "reconnaissance activity"
          ],
          severity: "medium",
          category: "discovery"
        },
        "T1105": {
          name: "Ingress Tool Transfer",
          patterns: [
            "file download",
            "tool transfer",
            "payload delivery",
            "remote file access"
          ],
          severity: "high",
          category: "command_and_control"
        }
      },
      custom_security_rules: {
        brute_force_detection: {
          condition: "failed_login_count > 5 AND time_window < 300",
          severity: "high",
          description: "Potential brute force attack detected",
          category: "authentication"
        },
        data_exfiltration: {
          condition: "large_file_transfer AND unusual_time",
          severity: "critical",
          description: "Potential data exfiltration detected",
          category: "data_theft"
        },
        privilege_escalation: {
          condition: "admin_access AND non_admin_user",
          severity: "critical",
          description: "Unauthorized privilege escalation detected",
          category: "privilege_escalation"
        },
        malware_indicators: {
          condition: "suspicious_process OR malicious_file",
          severity: "critical",
          description: "Malware activity indicators detected",
          category: "malware"
        },
        network_anomaly: {
          condition: "unusual_network_traffic OR port_scanning",
          severity: "medium",
          description: "Network anomaly detected",
          category: "network"
        }
      }
    };

    await fs.ensureDir(path.dirname(rulesPath));
    await fs.writeJson(rulesPath, defaultRules, { spaces: 2 });
    this.securityRules = defaultRules;
    logger.info('Created default security rules');
  }

  initializeMitrePatterns() {
    this.mitrePatterns = this.securityRules?.mitre_attack_patterns || {};
  }

  initializeCustomRules() {
    this.customRules = this.securityRules?.custom_security_rules || {};
  }

  async matchPatterns(logs) {
    try {
      if (!this.isInitialized) {
        throw new Error('Pattern matcher not initialized');
      }

      if (!logs || logs.length === 0) {
        return [];
      }

      logger.info(`Matching patterns in ${logs.length} logs`);

      const patterns = [];

      // Match MITRE ATT&CK patterns
      const mitreMatches = await this.matchMitrePatterns(logs);
      patterns.push(...mitreMatches);

      // Match custom security rules
      const customMatches = await this.matchCustomRules(logs);
      patterns.push(...customMatches);

      // Sort by severity and confidence
      patterns.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const aSeverity = severityOrder[a.severity] || 1;
        const bSeverity = severityOrder[b.severity] || 1;
        
        if (aSeverity !== bSeverity) {
          return bSeverity - aSeverity;
        }
        
        return b.confidence - a.confidence;
      });

      logger.info(`Matched ${patterns.length} security patterns`);
      return patterns;

    } catch (error) {
      logger.error('Pattern matching failed:', error);
      throw error;
    }
  }

  async matchMitrePatterns(logs) {
    const matches = [];

    for (const [techniqueId, technique] of Object.entries(this.mitrePatterns)) {
      const matchingLogs = logs.filter(log => 
        this.matchesMitrePattern(log, technique.patterns)
      );

      if (matchingLogs.length > 0) {
        const match = {
          id: `mitre_${techniqueId}_${Date.now()}`,
          type: 'mitre_attack',
          techniqueId,
          name: technique.name,
          severity: technique.severity,
          category: technique.category,
          confidence: this.calculatePatternConfidence(matchingLogs, technique.patterns),
          matchCount: matchingLogs.length,
          description: `MITRE ATT&CK technique ${techniqueId} (${technique.name}) detected`,
          indicators: matchingLogs.map(log => ({
            logId: log._id ? log._id.toString() : undefined,
            message: log.message,
            timestamp: log.timestamp,
            matchedPattern: this.findMatchedPattern(log, technique.patterns)
          })),
          recommendations: this.generateMitreRecommendations(techniqueId, technique),
          references: [`https://attack.mitre.org/techniques/${techniqueId}/`],
          timestamp: new Date().toISOString()
        };

        matches.push(match);
      }
    }

    return matches;
  }

  async matchCustomRules(logs) {
    const matches = [];

    for (const [ruleId, rule] of Object.entries(this.customRules)) {
      const evaluation = this.evaluateCustomRule(logs, rule);

      if (evaluation.matched) {
        const match = {
          id: `custom_${ruleId}_${Date.now()}`,
          type: 'custom_rule',
          ruleId,
          name: rule.description,
          severity: rule.severity,
          category: rule.category,
          confidence: evaluation.confidence,
          matchCount: evaluation.matchingLogs.length,
          description: rule.description,
          condition: rule.condition,
          indicators: evaluation.matchingLogs.map(log => ({
            logId: log._id ? log._id.toString() : undefined,
            message: log.message,
            timestamp: log.timestamp,
            ruleMatch: evaluation.ruleDetails
          })),
          recommendations: this.generateCustomRuleRecommendations(ruleId, rule),
          timestamp: new Date().toISOString()
        };

        matches.push(match);
      }
    }

    return matches;
  }

  matchesMitrePattern(log, patterns) {
    const message = (log.message || '').toLowerCase();
    return patterns.some(pattern => 
      message.includes(pattern.toLowerCase())
    );
  }

  findMatchedPattern(log, patterns) {
    const message = (log.message || '').toLowerCase();
    return patterns.find(pattern => 
      message.includes(pattern.toLowerCase())
    );
  }

  calculatePatternConfidence(matchingLogs, patterns) {
    const totalLogs = matchingLogs.length;
    const uniquePatterns = new Set();
    
    matchingLogs.forEach(log => {
      const matchedPattern = this.findMatchedPattern(log, patterns);
      if (matchedPattern) {
        uniquePatterns.add(matchedPattern);
      }
    });

    // Confidence based on number of matches and pattern diversity
    const baseConfidence = Math.min(90, 30 + (totalLogs * 10));
    const diversityBonus = (uniquePatterns.size / patterns.length) * 20;
    
    return Math.round(baseConfidence + diversityBonus);
  }

  evaluateCustomRule(logs, rule) {
    const condition = rule.condition.toLowerCase();
    const matchingLogs = [];
    let matched = false;
    let confidence = 0;

    // Simple rule evaluation (can be enhanced with a proper rule engine)
    if (condition.includes('failed_login_count > 5')) {
      const failedLogins = logs.filter(log => 
        (log.message || '').toLowerCase().includes('failed') &&
        (log.message || '').toLowerCase().includes('login')
      );
      
      if (failedLogins.length > 5) {
        matched = true;
        matchingLogs.push(...failedLogins);
        confidence = Math.min(95, 50 + (failedLogins.length * 5));
      }
    }

    if (condition.includes('large_file_transfer')) {
      const fileTransfers = logs.filter(log => 
        (log.message || '').toLowerCase().includes('transfer') ||
        (log.message || '').toLowerCase().includes('download') ||
        (log.message || '').toLowerCase().includes('upload')
      );
      
      if (fileTransfers.length > 0) {
        matched = true;
        matchingLogs.push(...fileTransfers);
        confidence = Math.max(confidence, 60);
      }
    }

    if (condition.includes('admin_access')) {
      const adminAccess = logs.filter(log => 
        (log.message || '').toLowerCase().includes('admin') ||
        (log.message || '').toLowerCase().includes('administrator') ||
        (log.message || '').toLowerCase().includes('privilege')
      );
      
      if (adminAccess.length > 0) {
        matched = true;
        matchingLogs.push(...adminAccess);
        confidence = Math.max(confidence, 70);
      }
    }

    if (condition.includes('suspicious_process')) {
      const suspiciousProcesses = logs.filter(log => 
        (log.message || '').toLowerCase().includes('process') &&
        ((log.message || '').toLowerCase().includes('suspicious') ||
         (log.message || '').toLowerCase().includes('malicious') ||
         (log.message || '').toLowerCase().includes('unknown'))
      );
      
      if (suspiciousProcesses.length > 0) {
        matched = true;
        matchingLogs.push(...suspiciousProcesses);
        confidence = Math.max(confidence, 80);
      }
    }

    if (condition.includes('unusual_network_traffic')) {
      const networkLogs = logs.filter(log => 
        (log.message || '').toLowerCase().includes('network') ||
        (log.message || '').toLowerCase().includes('connection') ||
        (log.message || '').toLowerCase().includes('traffic')
      );
      
      if (networkLogs.length > 0) {
        matched = true;
        matchingLogs.push(...networkLogs);
        confidence = Math.max(confidence, 50);
      }
    }

    return {
      matched,
      confidence,
      matchingLogs: [...new Set(matchingLogs)], // Remove duplicates
      ruleDetails: condition
    };
  }

  generateMitreRecommendations(techniqueId, technique) {
    const recommendations = [];

    switch (technique.category) {
      case 'initial_access':
        recommendations.push('Strengthen access controls');
        recommendations.push('Implement multi-factor authentication');
        recommendations.push('Monitor login activities');
        break;
      case 'execution':
        recommendations.push('Restrict script execution');
        recommendations.push('Monitor process creation');
        recommendations.push('Implement application whitelisting');
        break;
      case 'defense_evasion':
        recommendations.push('Monitor process injection techniques');
        recommendations.push('Implement endpoint detection');
        recommendations.push('Review security tool configurations');
        break;
      case 'discovery':
        recommendations.push('Monitor file system access');
        recommendations.push('Implement network segmentation');
        recommendations.push('Review user permissions');
        break;
      case 'command_and_control':
        recommendations.push('Monitor network communications');
        recommendations.push('Implement network filtering');
        recommendations.push('Review firewall rules');
        break;
      default:
        recommendations.push('Investigate immediately');
        recommendations.push('Review security policies');
        recommendations.push('Contact security team');
    }

    return recommendations;
  }

  generateCustomRuleRecommendations(ruleId, rule) {
    const recommendations = [];

    switch (rule.category) {
      case 'authentication':
        recommendations.push('Review authentication logs');
        recommendations.push('Implement account lockout policies');
        recommendations.push('Enable multi-factor authentication');
        break;
      case 'data_theft':
        recommendations.push('Review data access permissions');
        recommendations.push('Monitor file transfers');
        recommendations.push('Implement data loss prevention');
        break;
      case 'privilege_escalation':
        recommendations.push('Review privilege assignments');
        recommendations.push('Implement least privilege principle');
        recommendations.push('Monitor administrative activities');
        break;
      case 'malware':
        recommendations.push('Run antivirus scan');
        recommendations.push('Isolate affected systems');
        recommendations.push('Update security definitions');
        break;
      case 'network':
        recommendations.push('Review network configurations');
        recommendations.push('Monitor network traffic');
        recommendations.push('Check firewall rules');
        break;
      default:
        recommendations.push('Investigate pattern details');
        recommendations.push('Review related logs');
        recommendations.push('Contact security team');
    }

    return recommendations;
  }
}

module.exports = PatternMatcher;

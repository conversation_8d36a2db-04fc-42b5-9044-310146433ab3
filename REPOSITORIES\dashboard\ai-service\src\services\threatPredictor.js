const logger = require('../utils/logger');
const moment = require('moment');

class ThreatPredictor {
  constructor() {
    this.isInitialized = false;
    this.threatPatterns = null;
    this.riskFactors = null;
  }

  async initialize() {
    try {
      logger.info('Initializing Threat Predictor...');
      
      this.initializeThreatPatterns();
      this.initializeRiskFactors();
      
      this.isInitialized = true;
      logger.info('Threat Predictor initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Threat Predictor:', error);
      throw error;
    }
  }

  initializeThreatPatterns() {
    this.threatPatterns = {
      bruteForce: {
        name: 'Brute Force Attack',
        indicators: ['failed login', 'authentication failed', 'invalid credentials'],
        threshold: 5,
        timeWindow: 300000, // 5 minutes
        severity: 'high'
      },
      dataExfiltration: {
        name: 'Data Exfiltration',
        indicators: ['large file transfer', 'unusual data access', 'bulk download'],
        threshold: 3,
        timeWindow: 600000, // 10 minutes
        severity: 'critical'
      },
      privilegeEscalation: {
        name: 'Privilege Escalation',
        indicators: ['privilege escalation', 'admin access', 'elevated permissions'],
        threshold: 2,
        timeWindow: 900000, // 15 minutes
        severity: 'critical'
      },
      malwareActivity: {
        name: 'Malware Activity',
        indicators: ['malware', 'virus', 'trojan', 'suspicious process'],
        threshold: 1,
        timeWindow: 300000, // 5 minutes
        severity: 'critical'
      },
      networkIntrusion: {
        name: 'Network Intrusion',
        indicators: ['port scan', 'network probe', 'unauthorized access'],
        threshold: 3,
        timeWindow: 600000, // 10 minutes
        severity: 'high'
      },
      systemCompromise: {
        name: 'System Compromise',
        indicators: ['system compromise', 'backdoor', 'rootkit'],
        threshold: 1,
        timeWindow: 300000, // 5 minutes
        severity: 'critical'
      }
    };
  }

  initializeRiskFactors() {
    this.riskFactors = {
      timeOfDay: {
        // Higher risk during off-hours
        getScore: (hour) => {
          if (hour >= 22 || hour <= 6) return 3; // Night
          if (hour >= 18 || hour <= 8) return 2; // Evening/Early morning
          return 1; // Business hours
        }
      },
      logVolume: {
        // Higher risk with unusual log volumes
        getScore: (currentVolume, averageVolume) => {
          const ratio = currentVolume / (averageVolume || 1);
          if (ratio > 5) return 4; // Very high volume
          if (ratio > 2) return 3; // High volume
          if (ratio < 0.1) return 2; // Very low volume (suspicious)
          return 1; // Normal volume
        }
      },
      errorRate: {
        // Higher risk with high error rates
        getScore: (errorRate) => {
          if (errorRate > 0.5) return 4; // >50% errors
          if (errorRate > 0.2) return 3; // >20% errors
          if (errorRate > 0.1) return 2; // >10% errors
          return 1; // Normal error rate
        }
      },
      sourceDistribution: {
        // Higher risk with unusual source patterns
        getScore: (sources) => {
          const uniqueSources = new Set(sources).size;
          const totalLogs = sources.length;
          const diversity = uniqueSources / totalLogs;
          
          if (diversity > 0.8) return 3; // Very diverse sources
          if (diversity < 0.1) return 2; // Very concentrated sources
          return 1; // Normal distribution
        }
      }
    };
  }

  async predictThreats(logs) {
    try {
      if (!this.isInitialized) {
        throw new Error('Threat predictor not initialized');
      }

      if (!logs || logs.length === 0) {
        return [];
      }

      logger.info(`Predicting threats from ${logs.length} logs`);

      const threats = [];
      const now = Date.now();

      // Analyze each threat pattern
      for (const [patternId, pattern] of Object.entries(this.threatPatterns)) {
        const patternThreats = await this.analyzePattern(logs, patternId, pattern, now);
        threats.push(...patternThreats);
      }

      // Analyze risk factors
      const riskAssessment = this.assessRiskFactors(logs);
      
      // Combine pattern-based threats with risk assessment
      const enhancedThreats = threats.map(threat => ({
        ...threat,
        riskFactors: riskAssessment,
        overallRiskScore: this.calculateOverallRisk(threat.riskScore, riskAssessment.totalScore)
      }));

      // Sort by risk score and return top threats
      enhancedThreats.sort((a, b) => b.overallRiskScore - a.overallRiskScore);

      logger.info(`Identified ${enhancedThreats.length} potential threats`);
      return enhancedThreats.slice(0, 20); // Return top 20 threats

    } catch (error) {
      logger.error('Threat prediction failed:', error);
      throw error;
    }
  }

  async analyzePattern(logs, patternId, pattern, currentTime) {
    const threats = [];
    const timeWindow = pattern.timeWindow;
    const threshold = pattern.threshold;

    // Group logs by time windows
    const timeWindows = this.groupLogsByTimeWindows(logs, timeWindow, currentTime);

    for (const [windowStart, windowLogs] of timeWindows) {
      const matchingLogs = windowLogs.filter(log => 
        this.matchesPattern(log, pattern.indicators)
      );

      if (matchingLogs.length >= threshold) {
        const threat = {
          id: `threat_${patternId}_${windowStart}`,
          type: patternId,
          name: pattern.name,
          severity: pattern.severity,
          confidence: this.calculateConfidence(matchingLogs.length, threshold),
          riskScore: this.calculateRiskScore(pattern.severity, matchingLogs.length, threshold),
          timeWindow: {
            start: new Date(windowStart),
            end: new Date(windowStart + timeWindow)
          },
          indicators: matchingLogs.map(log => ({
            logId: log._id ? log._id.toString() : undefined,
            message: log.message,
            timestamp: log.timestamp,
            logLevel: log.logLevel
          })),
          description: this.generateThreatDescription(pattern, matchingLogs),
          recommendations: this.generateThreatRecommendations(pattern, matchingLogs),
          affectedSystems: this.extractAffectedSystems(matchingLogs),
          attackVector: this.identifyAttackVector(pattern, matchingLogs)
        };

        threats.push(threat);
      }
    }

    return threats;
  }

  groupLogsByTimeWindows(logs, windowSize, currentTime) {
    const windows = new Map();
    
    logs.forEach(log => {
      const logTime = new Date(log.timestamp).getTime();
      const windowStart = Math.floor(logTime / windowSize) * windowSize;
      
      if (!windows.has(windowStart)) {
        windows.set(windowStart, []);
      }
      windows.get(windowStart).push(log);
    });

    return windows;
  }

  matchesPattern(log, indicators) {
    const message = (log.message || '').toLowerCase();
    return indicators.some(indicator => 
      message.includes(indicator.toLowerCase())
    );
  }

  calculateConfidence(matchCount, threshold) {
    const ratio = matchCount / threshold;
    const confidence = Math.min(100, 50 + (ratio - 1) * 25);
    return Math.round(confidence);
  }

  calculateRiskScore(severity, matchCount, threshold) {
    const severityScores = {
      'low': 1,
      'medium': 3,
      'high': 6,
      'critical': 10
    };

    const baseScore = severityScores[severity] || 1;
    const multiplier = Math.min(3, matchCount / threshold);
    
    return Math.round(baseScore * multiplier * 10) / 10;
  }

  generateThreatDescription(pattern, matchingLogs) {
    const count = matchingLogs.length;
    const timeSpan = this.getTimeSpan(matchingLogs);
    
    return `${pattern.name} detected with ${count} indicators over ${timeSpan}. ` +
           `This pattern suggests potential ${pattern.name.toLowerCase()} activity that requires attention.`;
  }

  generateThreatRecommendations(pattern, matchingLogs) {
    const recommendations = [];
    
    switch (pattern.name) {
      case 'Brute Force Attack':
        recommendations.push('Implement account lockout policies');
        recommendations.push('Enable multi-factor authentication');
        recommendations.push('Monitor failed login attempts');
        break;
      case 'Data Exfiltration':
        recommendations.push('Review data access permissions');
        recommendations.push('Monitor large file transfers');
        recommendations.push('Implement data loss prevention (DLP)');
        break;
      case 'Privilege Escalation':
        recommendations.push('Review privilege assignments');
        recommendations.push('Implement least privilege principle');
        recommendations.push('Monitor administrative activities');
        break;
      case 'Malware Activity':
        recommendations.push('Run full system scan');
        recommendations.push('Update antivirus definitions');
        recommendations.push('Isolate affected systems');
        break;
      case 'Network Intrusion':
        recommendations.push('Review firewall rules');
        recommendations.push('Monitor network traffic');
        recommendations.push('Check for unauthorized devices');
        break;
      default:
        recommendations.push('Investigate immediately');
        recommendations.push('Review system logs');
        recommendations.push('Contact security team');
    }

    return recommendations;
  }

  extractAffectedSystems(logs) {
    const systems = new Set();
    
    logs.forEach(log => {
      if (log.sourceType) systems.add(log.sourceType);
      if (log.hostname) systems.add(log.hostname);
      if (log.source) systems.add(log.source);
    });

    return Array.from(systems);
  }

  identifyAttackVector(pattern, logs) {
    const vectors = {
      'Brute Force Attack': 'Authentication',
      'Data Exfiltration': 'Data Access',
      'Privilege Escalation': 'Authorization',
      'Malware Activity': 'Malicious Code',
      'Network Intrusion': 'Network',
      'System Compromise': 'System'
    };

    return vectors[pattern.name] || 'Unknown';
  }

  getTimeSpan(logs) {
    if (logs.length === 0) return '0 minutes';
    
    const timestamps = logs.map(log => new Date(log.timestamp).getTime());
    const minTime = Math.min(...timestamps);
    const maxTime = Math.max(...timestamps);
    const spanMs = maxTime - minTime;
    
    if (spanMs < 60000) return `${Math.round(spanMs / 1000)} seconds`;
    if (spanMs < 3600000) return `${Math.round(spanMs / 60000)} minutes`;
    return `${Math.round(spanMs / 3600000)} hours`;
  }

  assessRiskFactors(logs) {
    const assessment = {
      timeOfDay: 1,
      logVolume: 1,
      errorRate: 1,
      sourceDistribution: 1,
      totalScore: 1
    };

    if (logs.length === 0) return assessment;

    // Time of day risk
    const currentHour = new Date().getHours();
    assessment.timeOfDay = this.riskFactors.timeOfDay.getScore(currentHour);

    // Log volume risk (simplified - would need historical data for accurate assessment)
    const assumedAverageVolume = 1000; // This would come from historical analysis
    assessment.logVolume = this.riskFactors.logVolume.getScore(logs.length, assumedAverageVolume);

    // Error rate risk
    const errorLogs = logs.filter(log => 
      ['error', 'fatal', 'warn'].includes(log.logLevel?.toLowerCase())
    );
    const errorRate = errorLogs.length / logs.length;
    assessment.errorRate = this.riskFactors.errorRate.getScore(errorRate);

    // Source distribution risk
    const sources = logs.map(log => log.sourceType || 'unknown');
    assessment.sourceDistribution = this.riskFactors.sourceDistribution.getScore(sources);

    // Calculate total score
    assessment.totalScore = (
      assessment.timeOfDay + 
      assessment.logVolume + 
      assessment.errorRate + 
      assessment.sourceDistribution
    ) / 4;

    return assessment;
  }

  calculateOverallRisk(threatRiskScore, riskFactorScore) {
    // Combine threat-specific risk with environmental risk factors
    return Math.round((threatRiskScore * 0.7 + riskFactorScore * 0.3) * 10) / 10;
  }
}

module.exports = ThreatPredictor;

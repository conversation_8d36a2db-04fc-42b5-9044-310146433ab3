const mongoose = require('mongoose');
const Log = require('../src/models/Log');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog';

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Sample log data generator
function generateDemoLogs() {
  const logLevels = ['critical', 'error', 'warning', 'info', 'debug'];
  const sources = ['System', 'Application', 'Security', 'Network', 'Custom']; // Valid enum values
  const sourceTypes = ['event', 'application', 'security', 'network', 'audit', 'performance']; // Valid enum values
  const categories = ['System', 'Application', 'Security', 'Network', 'Custom'];
  const hostnames = ['web-server-01', 'db-server-02', 'app-server-03', 'proxy-server-04', 'worker-node-05'];
  const osTypes = ['Windows Server 2019', 'Ubuntu 20.04', 'CentOS 8', 'Windows 10', 'Debian 11'];

  const messages = {
    critical: [
      'System disk space critically low - 95% full',
      'Database connection pool exhausted',
      'Memory usage exceeded 90% threshold',
      'Critical security breach detected',
      'Service completely unavailable'
    ],
    error: [
      'Failed to authenticate user login attempt',
      'Database query timeout after 30 seconds',
      'File not found: /var/log/application.log',
      'Network connection refused on port 443',
      'Invalid configuration parameter detected'
    ],
    warning: [
      'High CPU usage detected - 85% utilization',
      'SSL certificate expires in 7 days',
      'Unusual network traffic pattern observed',
      'Backup process took longer than expected',
      'Cache hit ratio below optimal threshold'
    ],
    info: [
      'User successfully logged in from IP *************',
      'Scheduled backup completed successfully',
      'Service restarted automatically',
      'Configuration file reloaded',
      'Health check passed - all systems operational'
    ],
    debug: [
      'Processing request ID: req_12345',
      'Cache miss for key: user_session_abc123',
      'Database query executed in 45ms',
      'API response time: 120ms',
      'Memory allocation: 256MB for process'
    ]
  };

  const logs = [];
  const now = new Date();

  // Generate 500 logs over the past 30 days
  for (let i = 0; i < 500; i++) {
    const daysAgo = Math.floor(Math.random() * 30);
    const hoursAgo = Math.floor(Math.random() * 24);
    const minutesAgo = Math.floor(Math.random() * 60);

    const timestamp = new Date(now);
    timestamp.setDate(timestamp.getDate() - daysAgo);
    timestamp.setHours(timestamp.getHours() - hoursAgo);
    timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);

    const logLevel = logLevels[Math.floor(Math.random() * logLevels.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const hostname = hostnames[Math.floor(Math.random() * hostnames.length)];
    const source = sources[Math.floor(Math.random() * sources.length)];
    const sourceType = sourceTypes[Math.floor(Math.random() * sourceTypes.length)];
    const osType = osTypes[Math.floor(Math.random() * osTypes.length)];
    const message = messages[logLevel][Math.floor(Math.random() * messages[logLevel].length)];

    const log = {
      logId: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Required unique field
      timestamp,
      host: hostname,
      logLevel,
      source,
      sourceType, // Required field
      category,
      message,
      severity: logLevel === 'critical' ? 5 : logLevel === 'error' ? 4 : logLevel === 'warning' ? 3 : logLevel === 'info' ? 2 : 1,
      metadata: {
        agentId: `agent_${hostname.replace('-', '_')}`,
        sourceMetadata: {
          os: osType,
          version: '1.0.0',
          hostname: hostname
        },
        processingTime: Math.floor(Math.random() * 100) + 10
      },
      tags: [category.toLowerCase(), source, logLevel],
      rawData: `[${timestamp.toISOString()}] [${logLevel.toUpperCase()}] [${source}] ${message}`
    };

    logs.push(log);
  }

  return logs;
}

async function insertDemoLogs() {
  try {
    console.log('Generating demo logs...');
    const demoLogs = generateDemoLogs();

    console.log('Clearing existing demo logs...');
    // Remove any existing demo logs (optional - comment out if you want to keep existing data)
    await Log.deleteMany({ 'metadata.agentId': /^agent_/ });

    console.log('Inserting demo logs...');
    await Log.insertMany(demoLogs);

    console.log(`Successfully inserted ${demoLogs.length} demo logs`);

    // Show summary
    const summary = await Log.aggregate([
      { $match: { 'metadata.agentId': /^agent_/ } },
      { $group: { _id: '$logLevel', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]);

    console.log('\nDemo logs summary by level:');
    summary.forEach(item => {
      console.log(`  ${item._id}: ${item.count} logs`);
    });

    const categorySummary = await Log.aggregate([
      { $match: { 'metadata.agentId': /^agent_/ } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]);

    console.log('\nDemo logs summary by category:');
    categorySummary.forEach(item => {
      console.log(`  ${item._id}: ${item.count} logs`);
    });

  } catch (error) {
    console.error('Error inserting demo logs:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed');
  }
}

// Run the script
async function main() {
  await connectDB();
  await insertDemoLogs();
}

main().catch(console.error);

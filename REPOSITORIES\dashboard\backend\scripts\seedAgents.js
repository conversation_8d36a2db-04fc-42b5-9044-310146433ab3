const mongoose = require('mongoose');
const Agent = require('../src/models/Agent');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const sampleAgents = [
  {
    agentId: 'agent-web-01',
    hostname: 'web-server-01',
    platform: 'linux',
    version: '1.2.3',
    status: 'online',
    isActive: true,
    ipAddress: '************',
    lastHeartbeat: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
    metadata: {
      os: 'Ubuntu 20.04',
      architecture: 'x86_64',
      performance: {
        cpuUsage: 45,
        memoryUsage: 62,
        diskUsage: 78
      }
    },
    registeredAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
  },
  {
    agentId: 'agent-web-02',
    hostname: 'web-server-02',
    platform: 'linux',
    version: '1.2.3',
    status: 'online',
    isActive: true,
    ipAddress: '************',
    lastHeartbeat: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
    metadata: {
      os: 'Ubuntu 20.04',
      architecture: 'x86_64',
      performance: {
        cpuUsage: 32,
        memoryUsage: 58,
        diskUsage: 65
      }
    },
    registeredAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
  },
  {
    agentId: 'agent-db-01',
    hostname: 'database-server',
    platform: 'linux',
    version: '1.2.2',
    status: 'warning',
    isActive: true,
    ipAddress: '************',
    lastHeartbeat: new Date(Date.now() - 3 * 60 * 1000), // 3 minutes ago
    metadata: {
      os: 'CentOS 8',
      architecture: 'x86_64',
      performance: {
        cpuUsage: 78,
        memoryUsage: 85,
        diskUsage: 92
      }
    },
    registeredAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
  },
  {
    agentId: 'agent-app-01',
    hostname: 'app-server-01',
    platform: 'windows',
    version: '1.2.3',
    status: 'online',
    isActive: true,
    ipAddress: '************',
    lastHeartbeat: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
    metadata: {
      os: 'Windows Server 2019',
      architecture: 'x86_64',
      performance: {
        cpuUsage: 25,
        memoryUsage: 45,
        diskUsage: 55
      }
    },
    registeredAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
  },
  {
    agentId: 'agent-app-02',
    hostname: 'app-server-02',
    platform: 'windows',
    version: '1.2.1',
    status: 'offline',
    isActive: true,
    ipAddress: '************',
    lastHeartbeat: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago (offline)
    metadata: {
      os: 'Windows Server 2019',
      architecture: 'x86_64',
      performance: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 60
      }
    },
    registeredAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
  },
  {
    agentId: 'agent-monitor-01',
    hostname: 'monitoring-server',
    platform: 'linux',
    version: '1.2.3',
    status: 'online',
    isActive: true,
    ipAddress: '************',
    lastHeartbeat: new Date(Date.now() - 30 * 1000), // 30 seconds ago
    metadata: {
      os: 'Alpine Linux',
      architecture: 'x86_64',
      performance: {
        cpuUsage: 15,
        memoryUsage: 35,
        diskUsage: 40
      }
    },
    registeredAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
  }
];

async function seedAgents() {
  try {
    console.log('Connecting to MongoDB...');
    
    // Clear existing agents
    await Agent.deleteMany({});
    console.log('Cleared existing agents');
    
    // Insert sample agents
    const insertedAgents = await Agent.insertMany(sampleAgents);
    console.log(`Inserted ${insertedAgents.length} sample agents`);
    
    // Display summary
    const agentCounts = await Agent.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    
    console.log('\nAgent Summary:');
    agentCounts.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });
    
    const platformCounts = await Agent.aggregate([
      { $group: { _id: '$platform', count: { $sum: 1 } } }
    ]);
    
    console.log('\nPlatform Summary:');
    platformCounts.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });
    
    console.log('\nSample agents seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding agents:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedAgents();

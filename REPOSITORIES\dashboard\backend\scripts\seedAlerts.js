const mongoose = require('mongoose');
const Alert = require('../src/models/Alert');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const sampleAlerts = [
  {
    title: 'High CPU Usage Detected',
    message: 'CPU usage has exceeded 90% for more than 5 minutes on server web-01',
    severity: 'critical',
    status: 'open',
    source: 'system-monitor',
    metadata: {
      server: 'web-01',
      cpuUsage: 95,
      threshold: 90
    },
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  },
  {
    title: 'Database Connection Pool Exhausted',
    message: 'All database connections are in use, new requests are being queued',
    severity: 'high',
    status: 'acknowledged',
    source: 'database-monitor',
    metadata: {
      poolSize: 100,
      activeConnections: 100,
      queuedRequests: 25
    },
    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    acknowledgedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  },
  {
    title: 'Failed Login Attempts',
    message: 'Multiple failed login attempts detected from IP *************',
    severity: 'medium',
    status: 'open',
    source: 'security-monitor',
    metadata: {
      sourceIP: '*************',
      attemptCount: 15,
      timeWindow: '5 minutes'
    },
    createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  },
  {
    title: 'Disk Space Warning',
    message: 'Disk usage on /var/log partition has reached 85%',
    severity: 'medium',
    status: 'open',
    source: 'system-monitor',
    metadata: {
      partition: '/var/log',
      usage: 85,
      threshold: 80
    },
    createdAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
  },
  {
    title: 'API Response Time Degradation',
    message: 'Average API response time has increased to 2.5 seconds',
    severity: 'high',
    status: 'open',
    source: 'api-monitor',
    metadata: {
      averageResponseTime: 2500,
      threshold: 1000,
      endpoint: '/api/v1/logs'
    },
    createdAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
  },
  {
    title: 'Memory Usage Alert',
    message: 'Memory usage has reached 88% on server app-02',
    severity: 'medium',
    status: 'resolved',
    source: 'system-monitor',
    metadata: {
      server: 'app-02',
      memoryUsage: 88,
      threshold: 85
    },
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    resolvedAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
  },
  {
    title: 'SSL Certificate Expiring',
    message: 'SSL certificate for api.example.com will expire in 7 days',
    severity: 'low',
    status: 'open',
    source: 'certificate-monitor',
    metadata: {
      domain: 'api.example.com',
      expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      daysRemaining: 7
    },
    createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
  },
  {
    title: 'Log Ingestion Rate Drop',
    message: 'Log ingestion rate has dropped by 50% in the last hour',
    severity: 'high',
    status: 'open',
    source: 'log-monitor',
    metadata: {
      currentRate: 500,
      expectedRate: 1000,
      dropPercentage: 50
    },
    createdAt: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
  }
];

async function seedAlerts() {
  try {
    console.log('Connecting to MongoDB...');
    
    // Clear existing alerts
    await Alert.deleteMany({});
    console.log('Cleared existing alerts');
    
    // Insert sample alerts
    const insertedAlerts = await Alert.insertMany(sampleAlerts);
    console.log(`Inserted ${insertedAlerts.length} sample alerts`);
    
    // Display summary
    const alertCounts = await Alert.aggregate([
      { $group: { _id: '$severity', count: { $sum: 1 } } }
    ]);
    
    console.log('\nAlert Summary:');
    alertCounts.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });
    
    console.log('\nSample alerts seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding alerts:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedAlerts();

const mongoose = require('mongoose');
const Alert = require('../src/models/Alert');
const Agent = require('../src/models/Agent');
const User = require('../src/models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function seedDashboardData() {
  try {
    console.log('🚀 Starting dashboard data seeding...');
    
    // Seed Alerts
    console.log('\n📢 Seeding alerts...');
    await Alert.deleteMany({});
    
    const sampleAlerts = [
      {
        title: 'High CPU Usage Detected',
        message: 'CPU usage has exceeded 90% for more than 5 minutes on server web-01',
        severity: 'critical',
        status: 'open',
        source: 'system-monitor',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      },
      {
        title: 'Database Connection Pool Exhausted',
        message: 'All database connections are in use, new requests are being queued',
        severity: 'high',
        status: 'acknowledged',
        source: 'database-monitor',
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
        acknowledgedAt: new Date(Date.now() - 30 * 60 * 1000),
      },
      {
        title: 'Failed Login Attempts',
        message: 'Multiple failed login attempts detected from IP *************',
        severity: 'medium',
        status: 'open',
        source: 'security-monitor',
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
      },
      {
        title: 'API Response Time Degradation',
        message: 'Average API response time has increased to 2.5 seconds',
        severity: 'high',
        status: 'open',
        source: 'api-monitor',
        createdAt: new Date(Date.now() - 10 * 60 * 1000),
      },
      {
        title: 'SSL Certificate Expiring',
        message: 'SSL certificate for api.example.com will expire in 7 days',
        severity: 'low',
        status: 'open',
        source: 'certificate-monitor',
        createdAt: new Date(Date.now() - 5 * 60 * 1000),
      }
    ];
    
    await Alert.insertMany(sampleAlerts);
    console.log(`✅ Inserted ${sampleAlerts.length} alerts`);
    
    // Seed Agents
    console.log('\n🤖 Seeding agents...');
    await Agent.deleteMany({});
    
    const sampleAgents = [
      {
        agentId: 'agent-web-01',
        hostname: 'web-server-01',
        platform: 'linux',
        version: '1.2.3',
        status: 'online',
        isActive: true,
        ipAddress: '************',
        lastHeartbeat: new Date(Date.now() - 1 * 60 * 1000),
        registeredAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      },
      {
        agentId: 'agent-web-02',
        hostname: 'web-server-02',
        platform: 'linux',
        version: '1.2.3',
        status: 'online',
        isActive: true,
        ipAddress: '************',
        lastHeartbeat: new Date(Date.now() - 2 * 60 * 1000),
        registeredAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      },
      {
        agentId: 'agent-db-01',
        hostname: 'database-server',
        platform: 'linux',
        version: '1.2.2',
        status: 'warning',
        isActive: true,
        ipAddress: '************',
        lastHeartbeat: new Date(Date.now() - 3 * 60 * 1000),
        registeredAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      },
      {
        agentId: 'agent-app-01',
        hostname: 'app-server-01',
        platform: 'windows',
        version: '1.2.3',
        status: 'online',
        isActive: true,
        ipAddress: '************',
        lastHeartbeat: new Date(Date.now() - 1 * 60 * 1000),
        registeredAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      },
      {
        agentId: 'agent-app-02',
        hostname: 'app-server-02',
        platform: 'windows',
        version: '1.2.1',
        status: 'offline',
        isActive: true,
        ipAddress: '************',
        lastHeartbeat: new Date(Date.now() - 15 * 60 * 1000),
        registeredAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      }
    ];
    
    await Agent.insertMany(sampleAgents);
    console.log(`✅ Inserted ${sampleAgents.length} agents`);
    
    // Add some user activity data
    console.log('\n👥 Adding user activity data...');
    const users = await User.find({}).limit(3);
    
    if (users.length > 0) {
      for (const user of users) {
        // Add some recent activity
        const activities = [
          { action: 'login', timestamp: new Date(Date.now() - 30 * 60 * 1000) },
          { action: 'view_logs', timestamp: new Date(Date.now() - 25 * 60 * 1000) },
          { action: 'view_logs', timestamp: new Date(Date.now() - 15 * 60 * 1000) },
        ];
        
        user.activityLog = user.activityLog || [];
        user.activityLog.push(...activities);
        
        // Add active session
        user.sessions = user.sessions || [];
        user.sessions.push({
          sessionId: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          isActive: true,
          lastActivity: new Date(Date.now() - 5 * 60 * 1000),
          ipAddress: '*************'
        });
        
        await user.save();
      }
      console.log(`✅ Added activity data for ${users.length} users`);
    }
    
    // Display summary
    console.log('\n📊 Dashboard Data Summary:');
    
    const alertSummary = await Alert.aggregate([
      { $group: { _id: '$severity', count: { $sum: 1 } } }
    ]);
    console.log('  Alerts:');
    alertSummary.forEach(item => {
      console.log(`    ${item._id}: ${item.count}`);
    });
    
    const agentSummary = await Agent.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    console.log('  Agents:');
    agentSummary.forEach(item => {
      console.log(`    ${item._id}: ${item.count}`);
    });
    
    console.log('\n🎉 Dashboard data seeded successfully!');
    console.log('💡 You can now see real data in your dashboard widgets.');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding dashboard data:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedDashboardData();

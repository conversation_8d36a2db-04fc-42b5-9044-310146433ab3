require('dotenv').config();

const config = {
  // Server Configuration
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',

  // Database Configuration
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog',
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    rememberMeExpiresIn: process.env.JWT_REMEMBER_ME_EXPIRES_IN || '30d',
    rememberMeRefreshExpiresIn: process.env.JWT_REMEMBER_ME_REFRESH_EXPIRES_IN || '90d',
  },

  // Security Configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 1000, // Increased general limit
    authRateLimitMax: parseInt(process.env.AUTH_RATE_LIMIT_MAX) || 20, // Strict limit for auth endpoints
    authenticatedRateLimitMax: parseInt(process.env.AUTHENTICATED_RATE_LIMIT_MAX) || 200, // Per minute for authenticated users
  },

  // CORS Configuration
  cors: {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      // Get CORS_ORIGIN from environment
      const corsOrigin = process.env.CORS_ORIGIN || 'http://localhost:3000';

      // If CORS_ORIGIN is '*', allow all origins
      if (corsOrigin === '*') {
        return callback(null, true);
      }

      // If CORS_ORIGIN contains multiple origins (comma-separated)
      if (corsOrigin.includes(',')) {
        const allowedOrigins = corsOrigin.split(',').map(o => o.trim());
        if (allowedOrigins.includes(origin)) {
          return callback(null, true);
        }
      } else {
        // Single origin check
        if (corsOrigin === origin) {
          return callback(null, true);
        }
      }

      // Allow localhost and 127.0.0.1 with any port for development
      if (process.env.NODE_ENV === 'development') {
        const localhostRegex = /^https?:\/\/(localhost|127\.0\.0\.1|0\.0\.0\.0)(:\d+)?$/;
        const localNetworkRegex = /^https?:\/\/192\.168\.\d+\.\d+(:\d+)?$/;
        const privateNetworkRegex = /^https?:\/\/10\.\d+\.\d+\.\d+(:\d+)?$/;
        const privateNetwork172Regex = /^https?:\/\/172\.(1[6-9]|2[0-9]|3[0-1])\.\d+\.\d+(:\d+)?$/;

        if (localhostRegex.test(origin) || localNetworkRegex.test(origin) ||
          privateNetworkRegex.test(origin) || privateNetwork172Regex.test(origin)) {
          return callback(null, true);
        }
      }

      // Reject the request
      callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
  },

  // Email Configuration
  email: {
    // Resend configuration (preferred)
    resendApiKey: process.env.RESEND_API_KEY,
    // SMTP configuration (fallback)
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER,
    password: process.env.EMAIL_PASSWORD,
  },

  // Log Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
  },

  // Data Retention Configuration (fallback values, actual config comes from database)
  retention: {
    // Log retention in seconds (default: 90 days)
    logRetentionSeconds: parseInt(process.env.LOG_RETENTION_SECONDS) || (90 * 24 * 60 * 60),
    // Alert retention in seconds (default: 365 days)
    alertRetentionSeconds: parseInt(process.env.ALERT_RETENTION_SECONDS) || (365 * 24 * 60 * 60),
    // Disable automatic deletion in development
    enableAutoDelete: process.env.ENABLE_AUTO_DELETE === 'true' || process.env.NODE_ENV === 'production',
  },

  // Log Storage Configuration (fallback values, actual config comes from database)
  logStorage: {
    // Local storage settings
    localPath: process.env.LOG_STORAGE_PATH || './logs/storage',
    // Archive storage settings
    archivePath: process.env.LOG_ARCHIVE_PATH || './logs/archive',
    // External storage settings (fallback)
    externalStorage: {
      type: process.env.EXTERNAL_STORAGE_TYPE || 'local', // local, s3, azure, gcp
    },
  },

  // Agent Configuration
  agent: {
    defaultConfig: {
      general: {
        serviceName: 'ExLog Agent',
        logLevel: 'info',
        bufferSize: 1000,
        processingInterval: 5000,
      },
      collection: {
        eventLogs: {
          enabled: true,
          sources: ['System', 'Application', 'Security'],
          maxRecords: 1000,
        },
        securityLogs: {
          enabled: true,
          authentication: true,
          policyChanges: true,
          privilegeUse: true,
        },
        applicationLogs: {
          enabled: true,
          sources: [],
        },
        systemLogs: {
          enabled: true,
          hardware: true,
          drivers: true,
          services: true,
        },
        networkLogs: {
          enabled: false,
          connections: false,
          interfaceChanges: false,
        },
      },
      standardization: {
        outputFormat: 'json',
        includeRawData: false,
        timestampFormat: 'ISO8601',
        includeHostname: true,
        includeSourceMetadata: true,
        generateLogId: true,
      },
      output: {
        file: {
          enabled: false,
          path: './logs/agent.log',
          rotation: true,
        },
        console: {
          enabled: false,
        },
        api: {
          enabled: true,
          endpoint: 'http://localhost:5000/api/v1/logs',
          batchSize: 100,
          timeout: 30000,
        },
      },
      performance: {
        maxCpuPercentage: 10,
        maxMemoryUsage: '100MB',
        workerThreads: 2,
      },
      errorHandling: {
        errorLogging: true,
        retryAttempts: 3,
        retryDelay: 5000,
      },
    },
  },

  // Alert Configuration
  alerts: {
    defaultSeverityLevels: ['critical', 'high', 'medium', 'low', 'informational'],
    defaultNotificationChannels: ['email', 'webhook', 'in-app'],
    maxAlertsPerMinute: 100,
  },

  // Report Configuration
  reports: {
    maxReportSize: '50MB',
    supportedFormats: ['pdf', 'csv', 'html', 'json'],
    retentionDays: 90,
  },

  // WebSocket Configuration
  websocket: {
    port: process.env.WEBSOCKET_PORT || 5001,
    pingInterval: 30000,
    pingTimeout: 5000,
  },
};

module.exports = config;

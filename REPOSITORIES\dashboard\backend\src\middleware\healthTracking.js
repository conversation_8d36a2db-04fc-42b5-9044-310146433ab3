const systemHealthService = require('../services/systemHealthService');

/**
 * Middleware to track API response times for health monitoring
 */
const healthTrackingMiddleware = (req, res, next) => {
  const startTime = Date.now();

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Record the request metrics
    systemHealthService.recordRequest(responseTime);
    
    // Call the original end method
    originalEnd.apply(this, args);
  };

  next();
};

module.exports = healthTrackingMiddleware;

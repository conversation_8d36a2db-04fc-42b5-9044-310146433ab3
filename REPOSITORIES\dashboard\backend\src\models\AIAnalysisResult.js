const mongoose = require('mongoose');

const aiAnalysisResultSchema = new mongoose.Schema({
  // Analysis metadata
  analysisId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  configId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AIConfig',
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: ['periodic', 'manual', 'triggered'],
    required: true,
    default: 'periodic',
    index: true
  },
  status: {
    type: String,
    enum: ['running', 'completed', 'failed', 'cancelled'],
    required: true,
    default: 'running',
    index: true
  },
  
  // Analysis parameters
  analysisParameters: {
    timeRange: {
      type: String,
      required: true
    },
    startTime: {
      type: Date,
      required: true,
      index: true
    },
    endTime: {
      type: Date,
      required: true,
      index: true
    },
    logTypes: [{
      type: String,
      enum: ['debug', 'info', 'warn', 'error', 'fatal']
    }],
    maxLogs: {
      type: Number,
      default: 10000
    }
  },

  // Analysis execution details
  execution: {
    startedAt: {
      type: Date,
      required: true,
      default: Date.now,
      index: true
    },
    completedAt: {
      type: Date,
      index: true
    },
    duration: {
      type: Number, // milliseconds
      index: true
    },
    processedLogs: {
      type: Number,
      default: 0
    },
    errorMessage: {
      type: String
    },
    retryCount: {
      type: Number,
      default: 0
    }
  },

  // Analysis results summary
  summary: {
    totalLogs: {
      type: Number,
      required: true,
      default: 0
    },
    anomalies: {
      type: Number,
      required: true,
      default: 0
    },
    threats: {
      type: Number,
      required: true,
      default: 0
    },
    patterns: {
      type: Number,
      required: true,
      default: 0
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      required: true,
      default: 'low',
      index: true
    },
    confidence: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    }
  },

  // Detailed results
  anomalies: [{
    id: String,
    logId: String,
    anomalyScore: Number,
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    description: String,
    timestamp: Date,
    message: String,
    logLevel: String,
    sourceType: String,
    recommendations: [String],
    confidence: Number
  }],

  threats: [{
    id: String,
    type: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    confidence: Number,
    description: String,
    indicators: [String],
    affectedLogs: [String],
    timeline: [{
      timestamp: Date,
      event: String,
      logId: String
    }],
    mitreAttackTechniques: [String],
    recommendations: [String],
    riskScore: Number
  }],

  patterns: [{
    id: String,
    name: String,
    type: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    matchCount: Number,
    description: String,
    matchedLogs: [String],
    pattern: String,
    confidence: Number
  }],

  // Performance metrics
  performance: {
    memoryUsage: {
      peak: Number,
      average: Number
    },
    cpuUsage: {
      peak: Number,
      average: Number
    },
    processingRate: Number, // logs per second
    cacheHitRate: Number
  },

  // Data retention and compression
  retention: {
    expiresAt: {
      type: Date,
      index: { expireAfterSeconds: 0 }
    },
    isCompressed: {
      type: Boolean,
      default: false
    },
    compressionRatio: {
      type: Number
    },
    originalSize: {
      type: Number
    },
    compressedSize: {
      type: Number
    }
  },

  // Metadata
  metadata: {
    version: {
      type: String,
      default: '1.0.0'
    },
    tags: [String],
    notes: String,
    correlationId: String
  }
}, {
  timestamps: true,
  collection: 'ai_analysis_results'
});

// Compound indexes for better query performance
aiAnalysisResultSchema.index({ configId: 1, 'execution.startedAt': -1 });
aiAnalysisResultSchema.index({ type: 1, status: 1, 'execution.startedAt': -1 });
aiAnalysisResultSchema.index({ 'summary.riskLevel': 1, 'execution.startedAt': -1 });
aiAnalysisResultSchema.index({ 'analysisParameters.startTime': 1, 'analysisParameters.endTime': 1 });
aiAnalysisResultSchema.index({ 'retention.expiresAt': 1 });

// Virtual for analysis duration in human-readable format
aiAnalysisResultSchema.virtual('formattedDuration').get(function() {
  if (!this.execution.duration) return 'N/A';
  
  const seconds = Math.floor(this.execution.duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
});

// Virtual for processing rate
aiAnalysisResultSchema.virtual('logsPerSecond').get(function() {
  if (!this.execution.duration || this.execution.duration === 0) return 0;
  return Math.round((this.execution.processedLogs / (this.execution.duration / 1000)) * 100) / 100;
});

// Method to mark analysis as completed
aiAnalysisResultSchema.methods.markCompleted = function(results) {
  this.status = 'completed';
  this.execution.completedAt = new Date();
  this.execution.duration = this.execution.completedAt - this.execution.startedAt;
  
  if (results) {
    this.summary = results.summary || this.summary;
    this.anomalies = results.anomalies || this.anomalies;
    this.threats = results.threats || this.threats;
    this.patterns = results.patterns || this.patterns;
    this.performance = results.performance || this.performance;
  }
  
  return this.save();
};

// Method to mark analysis as failed
aiAnalysisResultSchema.methods.markFailed = function(errorMessage) {
  this.status = 'failed';
  this.execution.completedAt = new Date();
  this.execution.duration = this.execution.completedAt - this.execution.startedAt;
  this.execution.errorMessage = errorMessage;
  
  return this.save();
};

// Method to set retention expiry
aiAnalysisResultSchema.methods.setRetentionExpiry = function(retentionDays) {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + retentionDays);
  this.retention.expiresAt = expiryDate;
  
  return this.save();
};

// Static method to get recent results
aiAnalysisResultSchema.statics.getRecentResults = function(limit = 10, configId = null) {
  const query = configId ? { configId } : {};
  return this.find(query)
    .sort({ 'execution.startedAt': -1 })
    .limit(limit)
    .populate('configId', 'name version')
    .lean();
};

// Static method to get results by time range
aiAnalysisResultSchema.statics.getResultsByTimeRange = function(startTime, endTime, configId = null) {
  const query = {
    'execution.startedAt': {
      $gte: startTime,
      $lte: endTime
    }
  };
  
  if (configId) {
    query.configId = configId;
  }
  
  return this.find(query)
    .sort({ 'execution.startedAt': -1 })
    .populate('configId', 'name version')
    .lean();
};

// Static method to get performance statistics
aiAnalysisResultSchema.statics.getPerformanceStats = function(configId = null, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const matchStage = {
    'execution.startedAt': { $gte: startDate },
    status: 'completed'
  };
  
  if (configId) {
    matchStage.configId = new mongoose.Types.ObjectId(configId);
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalAnalyses: { $sum: 1 },
        avgDuration: { $avg: '$execution.duration' },
        avgProcessedLogs: { $avg: '$execution.processedLogs' },
        avgAnomalies: { $avg: '$summary.anomalies' },
        avgThreats: { $avg: '$summary.threats' },
        avgPatterns: { $avg: '$summary.patterns' },
        riskLevelDistribution: {
          $push: '$summary.riskLevel'
        }
      }
    }
  ]);
};

module.exports = mongoose.model('AIAnalysisResult', aiAnalysisResultSchema);

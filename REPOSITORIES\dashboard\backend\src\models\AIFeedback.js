const mongoose = require('mongoose');

const aiFeedbackSchema = new mongoose.Schema({
  anomalyId: {
    type: String,
    required: true,
    index: true
  },
  insightId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AIInsight',
    required: false
  },
  feedbackType: {
    type: String,
    enum: ['anomaly', 'threat', 'pattern', 'general'],
    required: true,
    default: 'anomaly'
  },
  isCorrect: {
    type: Boolean,
    required: true
  },
  feedback: {
    type: String,
    maxlength: 1000,
    trim: true
  },
  category: {
    type: String,
    enum: ['false_positive', 'true_positive', 'improvement_suggestion', 'bug_report', 'other'],
    required: false
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    required: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  userRole: {
    type: String,
    required: false
  },
  processed: {
    type: Boolean,
    default: false,
    index: true
  },
  processedAt: {
    type: Date,
    required: false
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  processingNotes: {
    type: String,
    maxlength: 500,
    trim: true
  },
  modelVersion: {
    type: String,
    required: false
  },
  confidence: {
    type: Number,
    min: 0,
    max: 100,
    required: false
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'ai_feedback'
});

// Indexes for better query performance
aiFeedbackSchema.index({ createdAt: -1 });
aiFeedbackSchema.index({ userId: 1, createdAt: -1 });
aiFeedbackSchema.index({ feedbackType: 1, createdAt: -1 });
aiFeedbackSchema.index({ isCorrect: 1, processed: 1 });
aiFeedbackSchema.index({ category: 1, createdAt: -1 });
aiFeedbackSchema.index({ processed: 1, createdAt: -1 });

// Virtual for formatted creation date
aiFeedbackSchema.virtual('formattedCreatedAt').get(function() {
  return this.createdAt.toISOString();
});

// Virtual for processing time
aiFeedbackSchema.virtual('processingTime').get(function() {
  if (this.processedAt && this.createdAt) {
    return this.processedAt.getTime() - this.createdAt.getTime();
  }
  return null;
});

// Method to mark as processed
aiFeedbackSchema.methods.markAsProcessed = function(processedBy, notes) {
  this.processed = true;
  this.processedAt = new Date();
  this.processedBy = processedBy;
  if (notes) {
    this.processingNotes = notes;
  }
  return this.save();
};

// Method to get feedback summary
aiFeedbackSchema.methods.getSummary = function() {
  return {
    id: this._id,
    anomalyId: this.anomalyId,
    feedbackType: this.feedbackType,
    isCorrect: this.isCorrect,
    category: this.category,
    severity: this.severity,
    processed: this.processed,
    createdAt: this.createdAt,
    userId: this.userId
  };
};

// Static method to get feedback statistics
aiFeedbackSchema.statics.getFeedbackStats = async function(timeRange = '30d') {
  const timeRangeMs = {
    '1d': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000,
    '90d': 90 * 24 * 60 * 60 * 1000
  };

  const startTime = new Date(Date.now() - (timeRangeMs[timeRange] || timeRangeMs['30d']));

  const stats = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: null,
        totalFeedback: { $sum: 1 },
        correctPredictions: {
          $sum: { $cond: [{ $eq: ['$isCorrect', true] }, 1, 0] }
        },
        incorrectPredictions: {
          $sum: { $cond: [{ $eq: ['$isCorrect', false] }, 1, 0] }
        },
        processedFeedback: {
          $sum: { $cond: [{ $eq: ['$processed', true] }, 1, 0] }
        },
        feedbackByType: {
          $push: '$feedbackType'
        },
        feedbackByCategory: {
          $push: '$category'
        }
      }
    },
    {
      $project: {
        _id: 0,
        totalFeedback: 1,
        correctPredictions: 1,
        incorrectPredictions: 1,
        processedFeedback: 1,
        accuracy: {
          $cond: [
            { $gt: ['$totalFeedback', 0] },
            { $multiply: [{ $divide: ['$correctPredictions', '$totalFeedback'] }, 100] },
            0
          ]
        },
        processingRate: {
          $cond: [
            { $gt: ['$totalFeedback', 0] },
            { $multiply: [{ $divide: ['$processedFeedback', '$totalFeedback'] }, 100] },
            0
          ]
        },
        typeDistribution: {
          anomaly: {
            $size: {
              $filter: {
                input: '$feedbackByType',
                cond: { $eq: ['$$this', 'anomaly'] }
              }
            }
          },
          threat: {
            $size: {
              $filter: {
                input: '$feedbackByType',
                cond: { $eq: ['$$this', 'threat'] }
              }
            }
          },
          pattern: {
            $size: {
              $filter: {
                input: '$feedbackByType',
                cond: { $eq: ['$$this', 'pattern'] }
              }
            }
          },
          general: {
            $size: {
              $filter: {
                input: '$feedbackByType',
                cond: { $eq: ['$$this', 'general'] }
              }
            }
          }
        },
        categoryDistribution: {
          false_positive: {
            $size: {
              $filter: {
                input: '$feedbackByCategory',
                cond: { $eq: ['$$this', 'false_positive'] }
              }
            }
          },
          true_positive: {
            $size: {
              $filter: {
                input: '$feedbackByCategory',
                cond: { $eq: ['$$this', 'true_positive'] }
              }
            }
          },
          improvement_suggestion: {
            $size: {
              $filter: {
                input: '$feedbackByCategory',
                cond: { $eq: ['$$this', 'improvement_suggestion'] }
              }
            }
          },
          bug_report: {
            $size: {
              $filter: {
                input: '$feedbackByCategory',
                cond: { $eq: ['$$this', 'bug_report'] }
              }
            }
          }
        }
      }
    }
  ]);

  return stats[0] || {
    totalFeedback: 0,
    correctPredictions: 0,
    incorrectPredictions: 0,
    processedFeedback: 0,
    accuracy: 0,
    processingRate: 0,
    typeDistribution: { anomaly: 0, threat: 0, pattern: 0, general: 0 },
    categoryDistribution: { false_positive: 0, true_positive: 0, improvement_suggestion: 0, bug_report: 0 }
  };
};

// Static method to get unprocessed feedback
aiFeedbackSchema.statics.getUnprocessedFeedback = function(limit = 50) {
  return this.find({ processed: false })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('userId', 'username email')
    .lean();
};

// Static method to get feedback by user
aiFeedbackSchema.statics.getFeedbackByUser = function(userId, limit = 20) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('anomalyId feedbackType isCorrect category processed createdAt')
    .lean();
};

// Static method to get recent feedback trends
aiFeedbackSchema.statics.getFeedbackTrends = async function(days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const trends = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          isCorrect: '$isCorrect'
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.date',
        correct: {
          $sum: { $cond: [{ $eq: ['$_id.isCorrect', true] }, '$count', 0] }
        },
        incorrect: {
          $sum: { $cond: [{ $eq: ['$_id.isCorrect', false] }, '$count', 0] }
        },
        total: { $sum: '$count' }
      }
    },
    {
      $project: {
        date: '$_id',
        correct: 1,
        incorrect: 1,
        total: 1,
        accuracy: {
          $cond: [
            { $gt: ['$total', 0] },
            { $multiply: [{ $divide: ['$correct', '$total'] }, 100] },
            0
          ]
        }
      }
    },
    {
      $sort: { date: 1 }
    }
  ]);

  return trends;
};

module.exports = mongoose.model('AIFeedback', aiFeedbackSchema);

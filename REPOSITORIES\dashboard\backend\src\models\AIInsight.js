const mongoose = require('mongoose');

const aiInsightSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['periodic', 'manual', 'triggered'],
    required: true,
    default: 'manual'
  },
  summary: {
    totalLogs: {
      type: Number,
      required: true,
      default: 0
    },
    anomalies: {
      type: Number,
      required: true,
      default: 0
    },
    threats: {
      type: Number,
      required: true,
      default: 0
    },
    patterns: {
      type: Number,
      required: true,
      default: 0
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      required: true,
      default: 'low'
    },
    analysisTime: {
      type: Number,
      required: true,
      default: 0
    }
  },
  anomalies: [{
    id: String,
    logId: String,
    anomalyScore: Number,
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    description: String,
    timestamp: Date,
    message: String,
    logLevel: String,
    sourceType: String,
    recommendations: [String],
    confidence: Number
  }],
  threats: [{
    id: String,
    type: String,
    name: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    confidence: Number,
    riskScore: Number,
    timeWindow: {
      start: Date,
      end: Date
    },
    indicators: [{
      logId: String,
      message: String,
      timestamp: Date,
      logLevel: String
    }],
    description: String,
    recommendations: [String],
    affectedSystems: [String],
    attackVector: String
  }],
  patterns: [{
    id: String,
    type: {
      type: String,
      enum: ['mitre_attack', 'custom_rule']
    },
    techniqueId: String,
    ruleId: String,
    name: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    category: String,
    confidence: Number,
    matchCount: Number,
    description: String,
    condition: String,
    indicators: [{
      logId: String,
      message: String,
      timestamp: Date,
      matchedPattern: String,
      ruleMatch: String
    }],
    recommendations: [String],
    references: [String]
  }],
  recommendations: [{
    type: {
      type: String,
      enum: ['anomaly', 'threat', 'pattern', 'general']
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    message: String,
    action: String
  }],
  timeRange: {
    type: String,
    required: true
  },
  logTypes: [String],
  options: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  triggeredBy: {
    type: String,
    enum: ['system', 'user', 'schedule'],
    default: 'system'
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  }
}, {
  timestamps: true,
  collection: 'ai_insights'
});

// Indexes for better query performance
aiInsightSchema.index({ createdAt: -1 });
aiInsightSchema.index({ type: 1, createdAt: -1 });
aiInsightSchema.index({ 'summary.riskLevel': 1, createdAt: -1 });
aiInsightSchema.index({ triggeredBy: 1, createdAt: -1 });
aiInsightSchema.index({ userId: 1, createdAt: -1 });

// Virtual for formatted creation date
aiInsightSchema.virtual('formattedCreatedAt').get(function() {
  return this.createdAt.toISOString();
});

// Method to get risk level color
aiInsightSchema.methods.getRiskLevelColor = function() {
  const colors = {
    low: '#4CAF50',
    medium: '#FF9800',
    high: '#FF5722',
    critical: '#F44336'
  };
  return colors[this.summary.riskLevel] || colors.low;
};

// Method to get summary statistics
aiInsightSchema.methods.getSummaryStats = function() {
  return {
    totalIssues: this.summary.anomalies + this.summary.threats + this.summary.patterns,
    criticalIssues: [
      ...this.anomalies.filter(a => a.riskLevel === 'critical'),
      ...this.threats.filter(t => t.severity === 'critical'),
      ...this.patterns.filter(p => p.severity === 'critical')
    ].length,
    highPriorityRecommendations: this.recommendations.filter(r => 
      ['high', 'critical'].includes(r.priority)
    ).length
  };
};

// Static method to get recent insights
aiInsightSchema.statics.getRecentInsights = function(limit = 10) {
  return this.find()
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('summary type createdAt triggeredBy')
    .lean();
};

// Static method to get insights by risk level
aiInsightSchema.statics.getInsightsByRiskLevel = function(riskLevel, limit = 20) {
  return this.find({ 'summary.riskLevel': riskLevel })
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();
};

// Static method to get insights statistics
aiInsightSchema.statics.getInsightsStats = async function(timeRange = '24h') {
  const timeRangeMs = {
    '1h': 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000
  };

  const startTime = new Date(Date.now() - (timeRangeMs[timeRange] || timeRangeMs['24h']));

  const stats = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: null,
        totalInsights: { $sum: 1 },
        totalAnomalies: { $sum: '$summary.anomalies' },
        totalThreats: { $sum: '$summary.threats' },
        totalPatterns: { $sum: '$summary.patterns' },
        avgAnalysisTime: { $avg: '$summary.analysisTime' },
        riskLevelCounts: {
          $push: '$summary.riskLevel'
        }
      }
    },
    {
      $project: {
        _id: 0,
        totalInsights: 1,
        totalAnomalies: 1,
        totalThreats: 1,
        totalPatterns: 1,
        avgAnalysisTime: { $round: ['$avgAnalysisTime', 2] },
        riskLevelDistribution: {
          low: {
            $size: {
              $filter: {
                input: '$riskLevelCounts',
                cond: { $eq: ['$$this', 'low'] }
              }
            }
          },
          medium: {
            $size: {
              $filter: {
                input: '$riskLevelCounts',
                cond: { $eq: ['$$this', 'medium'] }
              }
            }
          },
          high: {
            $size: {
              $filter: {
                input: '$riskLevelCounts',
                cond: { $eq: ['$$this', 'high'] }
              }
            }
          },
          critical: {
            $size: {
              $filter: {
                input: '$riskLevelCounts',
                cond: { $eq: ['$$this', 'critical'] }
              }
            }
          }
        }
      }
    }
  ]);

  return stats[0] || {
    totalInsights: 0,
    totalAnomalies: 0,
    totalThreats: 0,
    totalPatterns: 0,
    avgAnalysisTime: 0,
    riskLevelDistribution: { low: 0, medium: 0, high: 0, critical: 0 }
  };
};

module.exports = mongoose.model('AIInsight', aiInsightSchema);

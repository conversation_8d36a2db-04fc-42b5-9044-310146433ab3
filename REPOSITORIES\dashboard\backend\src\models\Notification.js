const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
  },
  type: {
    type: String,
    required: true,
    enum: ['info', 'success', 'warning', 'error'],
    default: 'info',
    index: true,
  },
  category: {
    type: String,
    required: true,
    enum: ['alert', 'system', 'agent', 'report', 'security', 'user', 'general'],
    default: 'general',
    index: true,
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium',
    index: true,
  },
  read: {
    type: Boolean,
    default: false,
    index: true,
  },
  readAt: {
    type: Date,
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  expiresAt: {
    type: Date,
    default: null,
    index: true,
  },
  // Reference to the source object that triggered this notification
  sourceType: {
    type: String,
    enum: ['alert', 'agent', 'log', 'report', 'user', 'system'],
    default: null,
  },
  sourceId: {
    type: mongoose.Schema.Types.ObjectId,
    default: null,
  },
  // Additional data for the notification
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },
  // Action buttons/links for the notification
  actions: [{
    label: {
      type: String,
      required: true,
      maxlength: 50,
    },
    url: {
      type: String,
      maxlength: 500,
    },
    action: {
      type: String,
      maxlength: 100,
    },
    style: {
      type: String,
      enum: ['primary', 'secondary', 'success', 'warning', 'error'],
      default: 'primary',
    },
  }],
  // Delivery tracking
  delivered: {
    type: Boolean,
    default: false,
  },
  deliveredAt: {
    type: Date,
    default: null,
  },
  // For batch operations
  batchId: {
    type: String,
    default: null,
    index: true,
  },
}, {
  timestamps: true,
});

// Indexes for performance
notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ userId: 1, read: 1, createdAt: -1 });
notificationSchema.index({ userId: 1, category: 1, createdAt: -1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Static methods
notificationSchema.statics.createNotification = async function(notificationData) {
  const notification = new this(notificationData);
  return await notification.save();
};

notificationSchema.statics.createBulkNotifications = async function(notifications) {
  return await this.insertMany(notifications);
};

notificationSchema.statics.getUserNotifications = async function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    category = null,
    type = null,
    read = null,
    severity = null,
  } = options;

  const query = { userId };
  
  if (category) query.category = category;
  if (type) query.type = type;
  if (read !== null) query.read = read;
  if (severity) query.severity = severity;

  const skip = (page - 1) * limit;

  const [notifications, total] = await Promise.all([
    this.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    this.countDocuments(query),
  ]);

  return {
    notifications,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
};

notificationSchema.statics.getUnreadCount = async function(userId) {
  return await this.countDocuments({ userId, read: false });
};

notificationSchema.statics.markAsRead = async function(userId, notificationIds) {
  const query = { userId };
  
  if (notificationIds && notificationIds.length > 0) {
    query._id = { $in: notificationIds };
  } else {
    query.read = false;
  }

  return await this.updateMany(query, {
    $set: {
      read: true,
      readAt: new Date(),
    },
  });
};

notificationSchema.statics.deleteNotifications = async function(userId, notificationIds) {
  return await this.deleteMany({
    userId,
    _id: { $in: notificationIds },
  });
};

notificationSchema.statics.cleanupExpiredNotifications = async function() {
  const now = new Date();
  return await this.deleteMany({
    expiresAt: { $lte: now },
  });
};

notificationSchema.statics.getNotificationStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        unread: { $sum: { $cond: [{ $eq: ['$read', false] }, 1, 0] } },
        byType: {
          $push: {
            type: '$type',
            read: '$read',
          },
        },
        byCategory: {
          $push: {
            category: '$category',
            read: '$read',
          },
        },
      },
    },
  ]);

  if (stats.length === 0) {
    return {
      total: 0,
      unread: 0,
      byType: {},
      byCategory: {},
    };
  }

  const result = stats[0];
  
  // Process type statistics
  const typeStats = {};
  result.byType.forEach(item => {
    if (!typeStats[item.type]) {
      typeStats[item.type] = { total: 0, unread: 0 };
    }
    typeStats[item.type].total++;
    if (!item.read) {
      typeStats[item.type].unread++;
    }
  });

  // Process category statistics
  const categoryStats = {};
  result.byCategory.forEach(item => {
    if (!categoryStats[item.category]) {
      categoryStats[item.category] = { total: 0, unread: 0 };
    }
    categoryStats[item.category].total++;
    if (!item.read) {
      categoryStats[item.category].unread++;
    }
  });

  return {
    total: result.total,
    unread: result.unread,
    byType: typeStats,
    byCategory: categoryStats,
  };
};

// Instance methods
notificationSchema.methods.markAsRead = async function() {
  this.read = true;
  this.readAt = new Date();
  return await this.save();
};

notificationSchema.methods.markAsDelivered = async function() {
  this.delivered = true;
  this.deliveredAt = new Date();
  return await this.save();
};

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;

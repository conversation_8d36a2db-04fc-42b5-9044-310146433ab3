const mongoose = require('mongoose');

const simpleReportSchema = new mongoose.Schema({
  reportId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500,
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  filters: {
    startDate: Date,
    endDate: Date,
    logCategories: [String],
    logLevels: [String],
    agentTypes: [String],
    searchKeywords: String,
  },
  resultSnapshot: {
    totalRecords: Number,
    data: [mongoose.Schema.Types.Mixed],
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
}, {
  timestamps: true,
});

// Compound indexes for efficient queries
simpleReportSchema.index({ owner: 1, createdAt: -1 });
simpleReportSchema.index({ reportId: 1, owner: 1 });

module.exports = mongoose.model('SimpleReport', simpleReportSchema);

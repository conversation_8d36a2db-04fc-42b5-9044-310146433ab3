const express = require('express');
const axios = require('axios');
const logger = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');
const AIAlertService = require('../services/aiAlertService');
const AIConfig = require('../models/AIConfig');
const AIAnalysisResult = require('../models/AIAnalysisResult');
const aiSchedulerService = require('../services/aiSchedulerService');
const aiMonitoringService = require('../services/aiMonitoringService');

const router = express.Router();
const aiAlertService = new AIAlertService();

// AI service configuration
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://ai-insights:5002';
const AI_SERVICE_TIMEOUT = parseInt(process.env.AI_SERVICE_TIMEOUT) || 60000; // 60 seconds

// Create axios instance for AI service communication
const aiServiceClient = axios.create({
  baseURL: AI_SERVICE_URL,
  timeout: AI_SERVICE_TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Middleware to handle AI service errors
const handleAIServiceError = (error, res) => {
  logger.error('AI service error:', error);

  if (error.code === 'ECONNREFUSED') {
    return res.status(503).json({
      error: 'AI service unavailable',
      message: 'The AI insights service is currently unavailable. Please try again later.'
    });
  }

  if (error.response) {
    return res.status(error.response.status).json({
      error: 'AI service error',
      message: error.response.data?.message || 'An error occurred while processing your request'
    });
  }

  return res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred while communicating with the AI service'
  });
};

/**
 * @swagger
 * /api/v1/ai/insights:
 *   get:
 *     summary: Get current AI insights
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current AI insights
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 summary:
 *                   type: object
 *                 anomalies:
 *                   type: array
 *                 threats:
 *                   type: array
 *                 patterns:
 *                   type: array
 */
router.get('/insights', authenticateToken, async (req, res) => {
  try {
    logger.info('Fetching AI insights');

    const response = await aiServiceClient.get('/api/insights');

    // Return the AI service data directly, not wrapped
    res.json(response.data);

  } catch (error) {
    handleAIServiceError(error, res);
  }
});

/**
 * @swagger
 * /api/v1/ai/analyze:
 *   post:
 *     summary: Trigger manual AI analysis
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               timeRange:
 *                 type: string
 *                 enum: [15m, 30m, 1h, 2h, 6h, 12h, 24h, 7d]
 *                 default: 1h
 *               logTypes:
 *                 type: array
 *                 items:
 *                   type: string
 *               options:
 *                 type: object
 */
router.post('/analyze', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '1h', logTypes = [], options = {} } = req.body;

    logger.info(`Triggering AI analysis for timeRange: ${timeRange}, logTypes: ${logTypes.join(',')}`);

    const response = await aiServiceClient.post('/api/analyze', {
      timeRange,
      logTypes,
      options
    });

    res.json({
      success: true,
      data: response.data,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    handleAIServiceError(error, res);
  }
});

/**
 * @swagger
 * /api/v1/ai/threats:
 *   get:
 *     summary: Get threat predictions
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/threats', authenticateToken, async (req, res) => {
  try {
    logger.info('Fetching threat predictions');

    const response = await aiServiceClient.get('/api/threats');

    // Return the AI service data directly, not wrapped
    res.json(response.data);

  } catch (error) {
    handleAIServiceError(error, res);
  }
});

/**
 * @swagger
 * /api/v1/ai/anomalies:
 *   get:
 *     summary: Get detected anomalies
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of anomalies to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of anomalies to skip
 */
router.get('/anomalies', authenticateToken, async (req, res) => {
  try {
    const { limit = 50, offset = 0 } = req.query;

    logger.info(`Fetching anomalies with limit: ${limit}, offset: ${offset}`);

    const response = await aiServiceClient.get('/api/anomalies', {
      params: { limit, offset }
    });

    // Return the AI service data directly, not wrapped
    res.json(response.data);

  } catch (error) {
    handleAIServiceError(error, res);
  }
});

/**
 * @swagger
 * /api/v1/ai/feedback:
 *   post:
 *     summary: Submit feedback for AI model improvement
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - anomalyId
 *               - isCorrect
 *             properties:
 *               anomalyId:
 *                 type: string
 *               feedback:
 *                 type: string
 *               isCorrect:
 *                 type: boolean
 */
router.post('/feedback', authenticateToken, async (req, res) => {
  try {
    const { anomalyId, feedback, isCorrect } = req.body;

    if (!anomalyId || typeof isCorrect !== 'boolean') {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'anomalyId and isCorrect are required fields'
      });
    }

    logger.info(`Submitting feedback for anomaly ${anomalyId}: ${isCorrect ? 'correct' : 'incorrect'}`);

    const response = await aiServiceClient.post('/api/feedback', {
      anomalyId,
      feedback,
      isCorrect
    });

    res.json({
      success: true,
      data: response.data,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    handleAIServiceError(error, res);
  }
});

/**
 * @swagger
 * /api/v1/ai/performance:
 *   get:
 *     summary: Get AI service performance metrics
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/performance', authenticateToken, async (req, res) => {
  try {
    logger.info('Fetching AI service performance metrics');

    const response = await aiServiceClient.get('/api/performance');

    // Return the AI service data directly, not wrapped
    res.json(response.data);

  } catch (error) {
    handleAIServiceError(error, res);
  }
});

/**
 * @swagger
 * /api/v1/ai/config:
 *   get:
 *     summary: Get AI configuration
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/config', authenticateToken, async (req, res) => {
  try {
    // Get active configuration from database (without .lean() to preserve methods)
    let activeConfig = await AIConfig.findOne({ isActive: true });

    // If no active config exists, create a default one
    if (!activeConfig) {
      logger.info('No active AI configuration found, creating default configuration');
      activeConfig = await AIConfig.createDefaultConfig(req.user.id);
    }

    // Get scheduling status
    const schedulingStatus = aiSchedulerService.getScheduleStatus();
    const configSchedule = schedulingStatus.schedules.find(s => s.configId === activeConfig._id.toString());

    // Generate scheduling config manually if method doesn't exist
    let schedulingConfig;
    try {
      schedulingConfig = activeConfig.getSchedulingConfig();
    } catch (error) {
      // Fallback if method doesn't exist
      schedulingConfig = {
        enabled: activeConfig.analysisSettings?.autoScheduling?.enabled || false,
        cronExpression: activeConfig.analysisSettings?.autoScheduling?.cronExpression || null,
        timezone: activeConfig.analysisSettings?.autoScheduling?.timezone || 'UTC',
        maxConcurrentRuns: activeConfig.analysisSettings?.autoScheduling?.maxConcurrentRuns || 1,
        skipIfRunning: activeConfig.analysisSettings?.autoScheduling?.skipIfRunning || true,
        interval: activeConfig.analysisSettings?.interval || 300,
        timeRange: activeConfig.analysisSettings?.timeRange || '1h'
      };
    }

    const response = {
      id: activeConfig._id,
      name: activeConfig.name,
      description: activeConfig.description,
      version: activeConfig.version,
      isActive: activeConfig.isActive,
      analysisSettings: activeConfig.analysisSettings,
      anomalyDetection: activeConfig.anomalyDetection,
      threatPrediction: activeConfig.threatPrediction,
      patternMatching: activeConfig.patternMatching,
      alerting: activeConfig.alerting,
      performance: activeConfig.performance,
      dataRetention: activeConfig.dataRetention,
      modelSettings: activeConfig.modelSettings,
      scheduling: {
        ...schedulingConfig,
        isScheduled: !!configSchedule,
        nextRun: configSchedule?.nextRun || null,
        status: configSchedule?.isRunning ? 'running' : 'idle'
      },
      createdAt: activeConfig.createdAt,
      updatedAt: activeConfig.updatedAt
    };

    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching AI config:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch AI configuration'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/config:
 *   put:
 *     summary: Update AI configuration
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               analysisInterval:
 *                 type: integer
 *               anomalyThreshold:
 *                 type: number
 *               enabledFeatures:
 *                 type: object
 */
router.put('/config', authenticateToken, async (req, res) => {
  try {
    logger.info('Updating AI configuration:', req.body);

    // Get active configuration
    let activeConfig = await AIConfig.findOne({ isActive: true });

    if (!activeConfig) {
      return res.status(404).json({
        error: 'Configuration not found',
        message: 'No active AI configuration found'
      });
    }

    // Update configuration fields
    const updateFields = req.body;

    // Handle nested object updates
    if (updateFields.analysisSettings) {
      activeConfig.analysisSettings = { ...activeConfig.analysisSettings, ...updateFields.analysisSettings };
    }
    if (updateFields.anomalyDetection) {
      activeConfig.anomalyDetection = { ...activeConfig.anomalyDetection, ...updateFields.anomalyDetection };
    }
    if (updateFields.threatPrediction) {
      activeConfig.threatPrediction = { ...activeConfig.threatPrediction, ...updateFields.threatPrediction };
    }
    if (updateFields.patternMatching) {
      activeConfig.patternMatching = { ...activeConfig.patternMatching, ...updateFields.patternMatching };
    }
    if (updateFields.alerting) {
      activeConfig.alerting = { ...activeConfig.alerting, ...updateFields.alerting };
    }
    if (updateFields.performance) {
      activeConfig.performance = { ...activeConfig.performance, ...updateFields.performance };
    }
    if (updateFields.dataRetention) {
      activeConfig.dataRetention = { ...activeConfig.dataRetention, ...updateFields.dataRetention };
    }
    if (updateFields.modelSettings) {
      activeConfig.modelSettings = { ...activeConfig.modelSettings, ...updateFields.modelSettings };
    }

    // Update metadata
    activeConfig.lastModifiedBy = req.user.id;

    // Validate configuration
    const validation = activeConfig.validateConfig();
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Configuration validation failed',
        errors: validation.errors
      });
    }

    // Save configuration
    await activeConfig.save();

    // Update scheduler if scheduling settings changed
    if (updateFields.analysisSettings?.autoScheduling || updateFields.analysisSettings?.interval) {
      try {
        await aiSchedulerService.updateSchedule(activeConfig._id.toString());
        logger.info('Updated scheduler configuration');
      } catch (schedulerError) {
        logger.error('Error updating scheduler:', schedulerError);
        // Don't fail the entire request if scheduler update fails
      }
    }

    // Return updated configuration
    let schedulingConfig;
    try {
      schedulingConfig = activeConfig.getSchedulingConfig();
    } catch (error) {
      // Fallback if method doesn't exist
      schedulingConfig = {
        enabled: activeConfig.analysisSettings?.autoScheduling?.enabled || false,
        cronExpression: activeConfig.analysisSettings?.autoScheduling?.cronExpression || null,
        timezone: activeConfig.analysisSettings?.autoScheduling?.timezone || 'UTC',
        maxConcurrentRuns: activeConfig.analysisSettings?.autoScheduling?.maxConcurrentRuns || 1,
        skipIfRunning: activeConfig.analysisSettings?.autoScheduling?.skipIfRunning || true,
        interval: activeConfig.analysisSettings?.interval || 300,
        timeRange: activeConfig.analysisSettings?.timeRange || '1h'
      };
    }

    const response = {
      id: activeConfig._id,
      name: activeConfig.name,
      version: activeConfig.version,
      analysisSettings: activeConfig.analysisSettings,
      anomalyDetection: activeConfig.anomalyDetection,
      threatPrediction: activeConfig.threatPrediction,
      patternMatching: activeConfig.patternMatching,
      alerting: activeConfig.alerting,
      performance: activeConfig.performance,
      dataRetention: activeConfig.dataRetention,
      modelSettings: activeConfig.modelSettings,
      scheduling: schedulingConfig,
      updatedAt: activeConfig.updatedAt
    };

    res.json({
      success: true,
      data: response,
      message: 'AI configuration updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error updating AI config:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update AI configuration'
    });
  }
});

/**
 * @route   POST /api/v1/ai/process-insights
 * @desc    Process AI insights and generate alerts/notifications
 * @access  Private
 */
router.post('/process-insights', authenticateToken, async (req, res) => {
  try {
    const insights = req.body;

    if (!insights) {
      return res.status(400).json({ error: 'AI insights data is required' });
    }

    logger.info('Processing AI insights for alert generation');

    const alerts = await aiAlertService.processInsights(insights);

    res.json({
      success: true,
      alertsGenerated: alerts.length,
      alerts: alerts.map(alert => ({
        type: alert.type,
        severity: alert.severity,
        title: alert.title
      }))
    });

  } catch (error) {
    logger.error('Error processing AI insights:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process AI insights'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/results:
 *   get:
 *     summary: Get AI analysis results
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: configId
 *         schema:
 *           type: string
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: string
 *           format: date-time
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: string
 *           format: date-time
 */
router.get('/results', authenticateToken, async (req, res) => {
  try {
    const { limit = 10, configId, startTime, endTime } = req.query;

    let results;

    if (startTime && endTime) {
      results = await AIAnalysisResult.getResultsByTimeRange(
        new Date(startTime),
        new Date(endTime),
        configId
      );
    } else {
      results = await AIAnalysisResult.getRecentResults(parseInt(limit), configId);
    }

    res.json({
      success: true,
      data: results,
      count: results.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching AI analysis results:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch analysis results'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/results/{analysisId}:
 *   get:
 *     summary: Get specific AI analysis result
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/results/:analysisId', authenticateToken, async (req, res) => {
  try {
    const { analysisId } = req.params;

    const result = await AIAnalysisResult.findOne({ analysisId })
      .populate('configId', 'name version');

    if (!result) {
      return res.status(404).json({
        error: 'Analysis result not found',
        message: `Analysis result with ID ${analysisId} not found`
      });
    }

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching AI analysis result:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch analysis result'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/scheduler/status:
 *   get:
 *     summary: Get scheduler status
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/scheduler/status', authenticateToken, async (req, res) => {
  try {
    const status = aiSchedulerService.getScheduleStatus();

    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching scheduler status:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch scheduler status'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/performance:
 *   get:
 *     summary: Get AI performance statistics
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/performance', authenticateToken, async (req, res) => {
  try {
    const { configId, days = 7 } = req.query;

    const stats = await AIAnalysisResult.getPerformanceStats(configId, parseInt(days));

    res.json({
      success: true,
      data: stats[0] || {},
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching AI performance stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch performance statistics'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/health:
 *   get:
 *     summary: Get AI system health status
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/health', authenticateToken, async (req, res) => {
  try {
    const healthMetrics = aiMonitoringService.getMetrics();
    const schedulerStatus = aiSchedulerService.getScheduleStatus();

    const healthStatus = {
      overall: healthMetrics.systemHealth || 'unknown',
      lastCheck: healthMetrics.lastHealthCheck,
      scheduler: {
        status: schedulerStatus.initialized ? 'healthy' : 'unhealthy',
        details: schedulerStatus
      },
      metrics: {
        totalAnalyses: healthMetrics.totalAnalyses,
        successfulAnalyses: healthMetrics.successfulAnalyses,
        failedAnalyses: healthMetrics.failedAnalyses,
        averageAnalysisTime: healthMetrics.averageAnalysisTime,
        successRate: healthMetrics.totalAnalyses > 0
          ? (healthMetrics.successfulAnalyses / healthMetrics.totalAnalyses)
          : 0
      },
      alerts: healthMetrics.alerts || []
    };

    res.json({
      success: true,
      data: healthStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching AI health status:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch health status'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/monitoring/metrics:
 *   get:
 *     summary: Get detailed monitoring metrics
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.get('/monitoring/metrics', authenticateToken, async (req, res) => {
  try {
    const metrics = aiMonitoringService.getMetrics();

    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching monitoring metrics:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch monitoring metrics'
    });
  }
});

/**
 * @swagger
 * /api/v1/ai/monitoring/trigger-health-check:
 *   post:
 *     summary: Trigger manual health check
 *     tags: [AI Insights]
 *     security:
 *       - bearerAuth: []
 */
router.post('/monitoring/trigger-health-check', authenticateToken, async (req, res) => {
  try {
    // Trigger a manual health check
    await aiMonitoringService.performHealthCheck();
    const metrics = aiMonitoringService.getMetrics();

    res.json({
      success: true,
      message: 'Health check completed',
      data: {
        systemHealth: metrics.systemHealth,
        lastHealthCheck: metrics.lastHealthCheck,
        alerts: metrics.alerts
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error triggering health check:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to trigger health check'
    });
  }
});

router.post('/alerts', async (req, res) => {
  try {
    const alertData = req.body;

    const aiServiceKey = req.headers['x-ai-service-key'];
    if (aiServiceKey !== (process.env.AI_SERVICE_KEY || 'ai-service-internal-key')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info('Received AI-generated alert:', alertData.title);

    const { getCorrelationEngine } = require('../services/correlationEngine');
    const correlationEngine = getCorrelationEngine();

    const mongoose = require('mongoose');
    const aiAlertRule = {
      _id: new mongoose.Types.ObjectId(),
      name: alertData.title,
      description: alertData.message,
      severity: alertData.severity,
      category: 'ai_insights',
      ruleType: 'ai_generated',
      actions: [{ type: 'create_alert', enabled: true }],
      tags: ['ai-generated'],
      priority: alertData.severity === 'critical' ? 10 : alertData.severity === 'high' ? 8 : 5
    };

    const alert = await correlationEngine.createAlert(aiAlertRule, {
      _id: null,
      message: alertData.message,
      timestamp: new Date(),
      source: alertData.source,
      metadata: alertData.metadata
    }, alertData.metadata);

    await correlationEngine.createAlertNotifications(alert);

    res.json({
      success: true,
      alertId: alert._id,
      message: 'AI alert created successfully'
    });

  } catch (error) {
    logger.error('Error processing AI alert:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process AI alert'
    });
  }
});

module.exports = router;

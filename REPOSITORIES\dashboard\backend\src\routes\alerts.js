const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { authenticate, authorize } = require('../middleware/auth');
const Alert = require('../models/Alert');
const AlertRule = require('../models/AlertRule');
const Log = require('../models/Log');
const { getCorrelationEngine } = require('../services/correlationEngine');
const { getDefaultRulesService } = require('../services/defaultRules');
const notificationService = require('../services/notificationService');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/alerts
 * @desc    Get all alerts with filtering and pagination
 * @access  Private
 */
router.get('/', authenticate, authorize(['view_alerts']), [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Limit must be between 1 and 1000'),
  query('status')
    .optional({ checkFalsy: true })
    .isIn(['new', 'acknowledged', 'investigating', 'resolved', 'false_positive'])
    .withMessage('Invalid status'),
  query('severity')
    .optional({ checkFalsy: true })
    .isIn(['critical', 'high', 'medium', 'low', 'informational'])
    .withMessage('Invalid severity'),
  query('startTime')
    .optional()
    .isISO8601()
    .withMessage('Start time must be in ISO8601 format'),
  query('endTime')
    .optional()
    .isISO8601()
    .withMessage('End time must be in ISO8601 format'),
  query('grouped')
    .optional()
    .isBoolean()
    .withMessage('Grouped must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const {
    page = 1,
    limit = 50,
    status,
    severity,
    startTime,
    endTime,
    assignedTo,
    ruleId,
    grouped = false,
  } = req.query;

  // Build query
  const query = {};

  if (status && status.trim()) query.status = status;
  if (severity && severity.trim()) query.severity = severity;
  if (assignedTo && assignedTo.trim()) query.assignedTo = assignedTo;
  if (ruleId && ruleId.trim()) query.ruleId = ruleId;

  // Time range filter
  if (startTime || endTime) {
    query.triggeredAt = {};
    if (startTime) query.triggeredAt.$gte = new Date(startTime);
    if (endTime) query.triggeredAt.$lte = new Date(endTime);
  }

  if (grouped === 'true' || grouped === true) {
    // Grouped alerts using aggregation pipeline
    const pipeline = [
      { $match: query },
      {
        $lookup: {
          from: 'alertrules',
          localField: 'ruleId',
          foreignField: '_id',
          as: 'ruleInfo'
        }
      },
      {
        $addFields: {
          ruleInfo: { $arrayElemAt: ['$ruleInfo', 0] }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'assignedTo',
          foreignField: '_id',
          as: 'assignedToInfo'
        }
      },
      {
        $addFields: {
          assignedToInfo: { $arrayElemAt: ['$assignedToInfo', 0] }
        }
      },
      {
        $group: {
          _id: {
            ruleId: '$ruleId',
            name: '$name',
            severity: '$severity',
            status: '$status'
          },
          count: { $sum: 1 },
          firstTriggered: {
            $min: {
              $ifNull: ['$triggeredAt', new Date()]
            }
          },
          lastTriggered: {
            $max: {
              $ifNull: ['$triggeredAt', new Date()]
            }
          },
          alerts: { $push: '$$ROOT' },
          ruleInfo: { $first: '$ruleInfo' },
          // Get the most recent assignment
          latestAssignment: { $last: '$assignedTo' },
          latestAssignmentInfo: { $last: '$assignedToInfo' }
        }
      },
      {
        $project: {
          groupKey: {
            $concat: [
              { $toString: '$_id.ruleId' },
              '_',
              '$_id.severity',
              '_',
              '$_id.status'
            ]
          },
          groupInfo: {
            ruleId: '$_id.ruleId',
            ruleName: { $ifNull: ['$ruleInfo.name', 'Unknown Rule'] },
            name: '$_id.name',
            severity: '$_id.severity',
            status: '$_id.status',
            count: '$count',
            firstTriggered: '$firstTriggered',
            lastTriggered: '$lastTriggered',
            assignedTo: '$latestAssignment',
            assignedToInfo: '$latestAssignmentInfo'
          },
          alerts: {
            $map: {
              input: '$alerts',
              as: 'alert',
              in: {
                _id: '$$alert._id',
                name: '$$alert.name',
                description: '$$alert.description',
                severity: '$$alert.severity',
                status: '$$alert.status',
                triggeredAt: '$$alert.triggeredAt',
                assignedTo: '$$alert.assignedTo',
                priority: '$$alert.priority',
                metadata: '$$alert.metadata'
              }
            }
          }
        }
      },
      { $sort: { 'groupInfo.lastTriggered': -1 } },
      { $skip: (page - 1) * limit },
      { $limit: parseInt(limit) }
    ];

    const [groupedAlerts, totalGroups] = await Promise.all([
      Alert.aggregate(pipeline),
      Alert.aggregate([
        { $match: query },
        {
          $group: {
            _id: {
              ruleId: '$ruleId',
              name: '$name',
              severity: '$severity',
              status: '$status'
            }
          }
        },
        { $count: 'total' }
      ])
    ]);

    const totalCount = totalGroups[0]?.total || 0;

    res.json({
      status: 'success',
      data: {
        alerts: groupedAlerts,
        grouped: true,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          limit: parseInt(limit),
          hasNextPage: page * limit < totalCount,
          hasPrevPage: page > 1,
        },
      },
    });
  } else {
    // Regular individual alerts
    const skip = (page - 1) * limit;

    const [alerts, totalCount] = await Promise.all([
      Alert.find(query)
        .populate('ruleId', 'name category ruleType')
        .populate('acknowledgedBy', 'username firstName lastName')
        .populate('resolvedBy', 'username firstName lastName')
        .populate('assignedTo', 'username firstName lastName')
        .sort({ triggeredAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Alert.countDocuments(query),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      status: 'success',
      data: {
        alerts,
        grouped: false,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          limit: parseInt(limit),
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  }
}));

/**
 * @route   GET /api/v1/alerts/statistics
 * @desc    Get alert statistics
 * @access  Private
 */
router.get('/statistics', authenticate, authorize(['view_alerts']), catchAsync(async (req, res) => {
  const { timeRange = 24 } = req.query;

  const statistics = await Alert.getStatistics(parseInt(timeRange));
  const trendingData = await Alert.getTrendingData(7);

  res.json({
    status: 'success',
    data: {
      statistics,
      trending: trendingData,
      timeRange: parseInt(timeRange),
    },
  });
}));

/**
 * @route   GET /api/v1/alerts/rules
 * @desc    Get alert rules with filtering
 * @access  Private
 */
router.get('/rules', authenticate, authorize(['view_alerts']), [
  query('enabled')
    .optional()
    .isBoolean()
    .withMessage('Enabled must be a boolean'),
  query('category')
    .optional()
    .isIn(['security', 'performance', 'system', 'application', 'network', 'compliance'])
    .withMessage('Invalid category'),
  query('severity')
    .optional()
    .isIn(['critical', 'high', 'medium', 'low', 'informational'])
    .withMessage('Invalid severity'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { enabled, category, severity, isDefault } = req.query;

  const query = {};
  if (enabled !== undefined) query.enabled = enabled === 'true';
  if (category) query.category = category;
  if (severity) query.severity = severity;
  if (isDefault !== undefined) query.isDefault = isDefault === 'true';

  const rules = await AlertRule.find(query)
    .populate('createdBy', 'username firstName lastName')
    .populate('lastModifiedBy', 'username firstName lastName')
    .sort({ priority: -1, createdAt: -1 })
    .lean();

  res.json({
    status: 'success',
    data: {
      rules,
      count: rules.length,
    },
  });
}));

/**
 * @route   GET /api/v1/alerts/:id
 * @desc    Get specific alert by ID
 * @access  Private
 */
router.get('/:id', authenticate, authorize(['view_alerts']), catchAsync(async (req, res) => {
  const { id } = req.params;

  const alert = await Alert.findById(id)
    .populate('ruleId')
    .populate('acknowledgedBy', 'username firstName lastName')
    .populate('resolvedBy', 'username firstName lastName')
    .populate('assignedTo', 'username firstName lastName')
    .populate('relatedLogs');

  if (!alert) {
    throw new AppError('Alert not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      alert,
    },
  });
}));

/**
 * @route   PATCH /api/v1/alerts/:id
 * @desc    Update alert (acknowledge, resolve, assign, etc.)
 * @access  Private
 */
router.patch('/:id', authenticate, authorize(['manage_alerts']), [
  body('status')
    .optional()
    .isIn(['new', 'acknowledged', 'investigating', 'resolved', 'false_positive'])
    .withMessage('Invalid status'),
  body('assignedTo')
    .optional()
    .isMongoId()
    .withMessage('Invalid user ID'),
  body('priority')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Priority must be between 1 and 10'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { id } = req.params;
  const { status, assignedTo, priority, note } = req.body;
  const userId = req.user._id;

  const alert = await Alert.findById(id);
  if (!alert) {
    throw new AppError('Alert not found', 404);
  }

  // Update fields
  if (status) {
    alert.status = status;
    if (status === 'acknowledged' && !alert.acknowledgedAt) {
      alert.acknowledgedAt = new Date();
      alert.acknowledgedBy = userId;
    }
    if (status === 'resolved' && !alert.resolvedAt) {
      alert.resolvedAt = new Date();
      alert.resolvedBy = userId;
    }
  }

  if (assignedTo) alert.assignedTo = assignedTo;
  if (priority) alert.priority = priority;

  // Add note if provided
  if (note) {
    alert.notes.push({
      content: note,
      createdBy: userId,
      createdAt: new Date(),
    });
  }

  await alert.save();

  // Create notification for status change
  if (status) {
    try {
      const statusMessages = {
        acknowledged: `Alert "${alert.name}" has been acknowledged`,
        investigating: `Alert "${alert.name}" is now under investigation`,
        resolved: `Alert "${alert.name}" has been resolved`,
        false_positive: `Alert "${alert.name}" has been marked as false positive`,
      };

      if (statusMessages[status]) {
        // Notify assigned user or all users with alert permissions
        const userIds = alert.assignedTo ? [alert.assignedTo] : null;

        await notificationService.createNotification(
          alert.assignedTo || userId,
          {
            title: 'Alert Status Update',
            message: statusMessages[status],
            type: status === 'resolved' ? 'success' : 'info',
            category: 'alert',
            severity: 'medium',
            sourceType: 'alert',
            sourceId: alert._id,
            actions: [{
              label: 'View Alert',
              url: `/alerts/${alert._id}`,
              style: 'primary',
            }],
          }
        );
      }
    } catch (error) {
      logger.error('Error creating alert status notification:', error);
      // Don't fail the request if notification fails
    }
  }

  // Populate for response
  await alert.populate('acknowledgedBy resolvedBy assignedTo', 'username firstName lastName');

  res.json({
    status: 'success',
    data: {
      alert,
    },
  });
}));

/**
 * @route   DELETE /api/v1/alerts/:id
 * @desc    Delete alert
 * @access  Private
 */
router.delete('/:id', authenticate, authorize(['manage_alerts']), catchAsync(async (req, res) => {
  const { id } = req.params;

  const alert = await Alert.findById(id);
  if (!alert) {
    throw new AppError('Alert not found', 404);
  }

  await Alert.findByIdAndDelete(id);

  res.json({
    status: 'success',
    message: 'Alert deleted successfully',
  });
}));

/**
 * @route   POST /api/v1/alerts/rules
 * @desc    Create new alert rule
 * @access  Private
 */
router.post('/rules', authenticate, authorize(['manage_alerts']), [
  body('name')
    .notEmpty()
    .withMessage('Rule name is required')
    .isLength({ max: 200 })
    .withMessage('Rule name must be less than 200 characters'),
  body('description')
    .notEmpty()
    .withMessage('Rule description is required')
    .isLength({ max: 1000 })
    .withMessage('Rule description must be less than 1000 characters'),
  body('severity')
    .isIn(['critical', 'high', 'medium', 'low', 'informational'])
    .withMessage('Invalid severity'),
  body('category')
    .isIn(['security', 'performance', 'system', 'application', 'network', 'compliance'])
    .withMessage('Invalid category'),
  body('ruleType')
    .isIn(['threshold', 'pattern', 'correlation', 'anomaly', 'sequence'])
    .withMessage('Invalid rule type'),
  body('conditions')
    .isObject()
    .withMessage('Conditions must be an object'),
  body('timeWindow.value')
    .isInt({ min: 1 })
    .withMessage('Time window value must be a positive integer'),
  body('timeWindow.unit')
    .isIn(['seconds', 'minutes', 'hours', 'days'])
    .withMessage('Invalid time window unit'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const ruleData = {
    ...req.body,
    createdBy: req.user._id,
    lastModifiedBy: req.user._id,
  };

  const rule = new AlertRule(ruleData);
  await rule.save();

  // Reload correlation engine rules
  try {
    const correlationEngine = getCorrelationEngine();
    await correlationEngine.loadRules();
  } catch (error) {
    logger.error('Failed to reload correlation engine rules:', error);
  }

  await rule.populate('createdBy lastModifiedBy', 'username firstName lastName');

  res.status(201).json({
    status: 'success',
    data: {
      rule,
    },
  });
}));

/**
 * @route   GET /api/v1/alerts/rules/:id
 * @desc    Get specific alert rule
 * @access  Private
 */
router.get('/rules/:id', authenticate, authorize(['view_alerts']), catchAsync(async (req, res) => {
  const { id } = req.params;

  const rule = await AlertRule.findById(id)
    .populate('createdBy', 'username firstName lastName')
    .populate('lastModifiedBy', 'username firstName lastName');

  if (!rule) {
    throw new AppError('Alert rule not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      rule,
    },
  });
}));

/**
 * @route   PUT /api/v1/alerts/rules/:id
 * @desc    Update alert rule
 * @access  Private
 */
router.put('/rules/:id', authenticate, authorize(['manage_alerts']), [
  body('name')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Rule name must be less than 200 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Rule description must be less than 1000 characters'),
  body('enabled')
    .optional()
    .isBoolean()
    .withMessage('Enabled must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { id } = req.params;
  const updateData = {
    ...req.body,
    lastModifiedBy: req.user._id,
  };

  const rule = await AlertRule.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true
  }).populate('createdBy lastModifiedBy', 'username firstName lastName');

  if (!rule) {
    throw new AppError('Alert rule not found', 404);
  }

  // Reload correlation engine rules
  try {
    const correlationEngine = getCorrelationEngine();
    await correlationEngine.loadRules();
  } catch (error) {
    logger.error('Failed to reload correlation engine rules:', error);
  }

  res.json({
    status: 'success',
    data: {
      rule,
    },
  });
}));

/**
 * @route   DELETE /api/v1/alerts/rules/:id
 * @desc    Delete alert rule
 * @access  Private
 */
router.delete('/rules/:id', authenticate, authorize(['manage_alerts']), catchAsync(async (req, res) => {
  const { id } = req.params;

  const rule = await AlertRule.findById(id);
  if (!rule) {
    throw new AppError('Alert rule not found', 404);
  }

  // Don't allow deletion of default rules
  if (rule.isDefault) {
    throw new AppError('Cannot delete default rules', 400);
  }

  await AlertRule.findByIdAndDelete(id);

  // Reload correlation engine rules
  try {
    const correlationEngine = getCorrelationEngine();
    await correlationEngine.loadRules();
  } catch (error) {
    logger.error('Failed to reload correlation engine rules:', error);
  }

  res.json({
    status: 'success',
    message: 'Alert rule deleted successfully',
  });
}));

/**
 * @route   POST /api/v1/alerts/rules/default/initialize
 * @desc    Initialize default alert rules
 * @access  Private (Admin only)
 */
router.post('/rules/default/initialize', authenticate, authorize(['manage_alerts']), catchAsync(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    throw new AppError('Only administrators can initialize default rules', 403);
  }

  const defaultRulesService = getDefaultRulesService();
  const result = await defaultRulesService.initializeDefaultRules(req.user._id);

  // Reload correlation engine rules
  try {
    const correlationEngine = getCorrelationEngine();
    await correlationEngine.loadRules();
  } catch (error) {
    logger.error('Failed to reload correlation engine rules:', error);
  }

  res.json({
    status: 'success',
    message: 'Default rules initialized successfully',
    data: result,
  });
}));

/**
 * @route   POST /api/v1/alerts/rules/:id/test
 * @desc    Test alert rule against sample data
 * @access  Private
 */
router.post('/rules/:id/test', authenticate, authorize(['manage_alerts']), [
  body('testData')
    .isObject()
    .withMessage('Test data must be an object'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { id } = req.params;
  const { testData } = req.body;

  const rule = await AlertRule.findById(id);
  if (!rule) {
    throw new AppError('Alert rule not found', 404);
  }

  try {
    const correlationEngine = getCorrelationEngine();

    // Create a test log entry
    const testLog = {
      logId: `test_${Date.now()}`,
      timestamp: new Date(),
      ...testData,
    };

    // Test the rule (this would be a simplified test)
    const testResult = {
      ruleId: rule._id,
      ruleName: rule.name,
      testLog,
      wouldTrigger: false, // This would be calculated based on rule evaluation
      message: 'Rule test completed successfully',
    };

    res.json({
      status: 'success',
      data: testResult,
    });

  } catch (error) {
    logger.error('Rule test failed:', error);
    throw new AppError('Rule test failed', 500);
  }
}));

module.exports = router;

const express = require('express');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize } = require('../middleware/auth');
const Log = require('../models/Log');
const Agent = require('../models/Agent');
const Alert = require('../models/Alert');
const User = require('../models/User');
const systemHealthService = require('../services/systemHealthService');

const router = express.Router();

/**
 * @route   GET /api/v1/dashboards/overview
 * @desc    Get dashboard overview data
 * @access  Private
 */
router.get('/overview', authorize(['view_logs']), catchAsync(async (req, res) => {
  const { timeRange = '24h' } = req.query;

  // Get comprehensive dashboard statistics
  const statistics = await Log.getStatistics(timeRange);

  // Calculate additional metrics
  const now = new Date();
  const previousPeriodStart = new Date(statistics.startTime.getTime() - (statistics.endTime.getTime() - statistics.startTime.getTime()));

  // Get previous period data for trend calculation
  const previousPeriodLogs = await Log.countDocuments({
    timestamp: {
      $gte: previousPeriodStart,
      $lt: statistics.startTime
    }
  });

  const previousCriticalEvents = await Log.countDocuments({
    timestamp: {
      $gte: previousPeriodStart,
      $lt: statistics.startTime
    },
    logLevel: { $in: ['critical', 'error'] }
  });

  // Calculate trends
  const logsTrend = previousPeriodLogs > 0
    ? ((statistics.totalLogs - previousPeriodLogs) / previousPeriodLogs * 100).toFixed(1)
    : 0;

  // Get real critical events from alerts instead of logs
  const criticalAlerts = await Alert.countDocuments({
    createdAt: { $gte: statistics.startTime },
    severity: { $in: ['critical', 'high'] },
    status: { $ne: 'resolved' }
  });

  // Calculate trend for critical alerts
  const previousCriticalAlerts = await Alert.countDocuments({
    createdAt: { $gte: previousPeriodStart, $lt: statistics.startTime },
    severity: { $in: ['critical', 'high'] },
    status: { $ne: 'resolved' }
  });

  const criticalTrend = previousCriticalAlerts > 0
    ? ((criticalAlerts - previousCriticalAlerts) / previousCriticalAlerts * 100).toFixed(1)
    : 0;

  // Override the critical events from logs with real alert data
  statistics.criticalEvents = criticalAlerts;

  // Get real active agents count from Agent collection
  const activeAgentsCount = await Agent.countDocuments({
    status: 'online',
    isActive: true,
    lastHeartbeat: { $gte: new Date(Date.now() - 5 * 60 * 1000) } // Last 5 minutes
  });

  // Get real alert data from Alert collection
  const realAlertSummary = await Alert.aggregate([
    {
      $match: {
        status: { $ne: 'resolved' },
        createdAt: { $gte: statistics.startTime }
      }
    },
    { $group: { _id: '$severity', count: { $sum: 1 } } }
  ]);

  // Convert to summary object
  const alertSummary = {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0
  };

  realAlertSummary.forEach(item => {
    alertSummary[item._id] = item.count;
  });

  const totalActiveAlerts = alertSummary.critical + alertSummary.high + alertSummary.medium + alertSummary.low;

  res.json({
    status: 'success',
    data: {
      overview: {
        totalLogs: statistics.totalLogs,
        logsTrend: parseFloat(logsTrend),
        criticalEvents: statistics.criticalEvents,
        criticalTrend: parseFloat(criticalTrend),
        activeAgents: activeAgentsCount,
        activeAlerts: totalActiveAlerts
      },
      statistics,
      alertSummary,
      timeRange
    }
  });
}));

/**
 * @route   GET /api/v1/dashboards/system-health
 * @desc    Get system health metrics
 * @access  Private
 */
router.get('/system-health', authorize(['view_logs']), catchAsync(async (req, res) => {
  // Get real system health data
  const healthData = await systemHealthService.getSystemHealth();

  res.json({
    status: 'success',
    data: {
      health: {
        database: {
          storage: healthData.database.storage,
          responseTime: healthData.database.responseTime,
          status: healthData.database.status,
          collections: healthData.database.collections
        },
        api: {
          responseTime: healthData.api.responseTime,
          requestsPerMinute: healthData.api.requestsPerMinute,
          status: healthData.api.status,
          uptime: healthData.api.uptime
        },
        logIngestion: {
          rate: healthData.logIngestion.rate,
          queueSize: healthData.logIngestion.queueSize,
          status: healthData.logIngestion.status,
          hourlyRate: healthData.logIngestion.hourlyRate
        },
        resources: healthData.resources,
        overall: healthData.overall
      },
      timestamp: healthData.timestamp
    }
  });
}));

/**
 * @route   GET /api/v1/dashboards/agent-status
 * @desc    Get agent status overview for dashboard
 * @access  Private
 */
router.get('/agent-status', authorize(['view_agents']), catchAsync(async (req, res) => {
  // Get agent status summary
  const statusCounts = await Agent.aggregate([
    { $match: { isActive: true } },
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  // Convert to summary object
  const summary = {
    total: 0,
    online: 0,
    offline: 0,
    warning: 0,
    error: 0
  };

  statusCounts.forEach(item => {
    summary[item._id] = item.count;
    summary.total += item.count;
  });

  // Get recent agents (last 10 with recent activity)
  const recentAgents = await Agent.find({ isActive: true })
    .sort({ lastHeartbeat: -1 })
    .limit(10)
    .select('agentId hostname status platform lastHeartbeat ipAddress metadata.performance')
    .lean();

  // Get platform distribution
  const platformCounts = await Agent.aggregate([
    { $match: { isActive: true } },
    { $group: { _id: '$platform', count: { $sum: 1 } } }
  ]);

  res.json({
    status: 'success',
    data: {
      summary,
      recentAgents,
      platformDistribution: platformCounts,
      lastUpdated: new Date()
    }
  });
}));

/**
 * @route   GET /api/v1/dashboards/recent-alerts
 * @desc    Get recent alerts overview for dashboard
 * @access  Private
 */
router.get('/recent-alerts', authorize(['view_alerts']), catchAsync(async (req, res) => {
  const now = new Date();
  const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last48Hours = new Date(now.getTime() - 48 * 60 * 60 * 1000);

  // Get alert summary by severity
  const alertSummary = await Alert.aggregate([
    { $match: { status: { $ne: 'resolved' }, createdAt: { $gte: last24Hours } } },
    { $group: { _id: '$severity', count: { $sum: 1 } } }
  ]);

  // Convert to summary object
  const summary = {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    total: 0
  };

  alertSummary.forEach(item => {
    summary[item._id] = item.count;
    summary.total += item.count;
  });

  // Get recent alerts
  const recentAlerts = await Alert.find({
    createdAt: { $gte: last24Hours }
  })
    .sort({ createdAt: -1 })
    .limit(10)
    .select('title message severity status createdAt acknowledgedAt resolvedAt')
    .lean();

  // Calculate trend (compare with previous 24 hours)
  const previousPeriodCount = await Alert.countDocuments({
    createdAt: { $gte: last48Hours, $lt: last24Hours }
  });

  let trend = { direction: 'stable', percentage: 0 };
  if (previousPeriodCount > 0) {
    const change = ((summary.total - previousPeriodCount) / previousPeriodCount) * 100;
    if (Math.abs(change) > 5) {
      trend = {
        direction: change > 0 ? 'up' : 'down',
        percentage: Math.round(Math.abs(change))
      };
    }
  }

  res.json({
    status: 'success',
    data: {
      summary,
      recentAlerts,
      trend,
      lastUpdated: new Date()
    }
  });
}));

/**
 * @route   GET /api/v1/dashboards/user-activity
 * @desc    Get user activity overview for dashboard
 * @access  Private
 */
router.get('/user-activity', authorize(['view_users']), catchAsync(async (req, res) => {
  const now = new Date();
  const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last48Hours = new Date(now.getTime() - 48 * 60 * 60 * 1000);

  // Get active users (users with recent activity)
  const activeUsers = await User.aggregate([
    {
      $match: {
        'activityLog.timestamp': { $gte: last24Hours }
      }
    },
    {
      $unwind: '$activityLog'
    },
    {
      $match: {
        'activityLog.timestamp': { $gte: last24Hours }
      }
    },
    {
      $group: {
        _id: '$_id',
        username: { $first: '$username' },
        firstName: { $first: '$firstName' },
        lastName: { $first: '$lastName' },
        lastActivity: { $max: '$activityLog.timestamp' },
        activityCount: { $sum: 1 }
      }
    },
    {
      $sort: { lastActivity: -1 }
    }
  ]);

  // Get recent activity
  const recentActivity = await User.aggregate([
    {
      $unwind: '$activityLog'
    },
    {
      $match: {
        'activityLog.timestamp': { $gte: last24Hours }
      }
    },
    {
      $project: {
        user: {
          username: '$username',
          firstName: '$firstName',
          lastName: '$lastName'
        },
        action: '$activityLog.action',
        timestamp: '$activityLog.timestamp',
        details: '$activityLog.details'
      }
    },
    {
      $sort: { timestamp: -1 }
    },
    {
      $limit: 20
    }
  ]);

  // Get active sessions (users with recent login and no logout)
  const activeSessions = await User.find({
    'sessions.isActive': true,
    'sessions.lastActivity': { $gte: new Date(now.getTime() - 30 * 60 * 1000) } // Last 30 minutes
  })
    .select('username firstName lastName sessions')
    .lean();

  // Calculate summary
  const summary = {
    activeUsers: activeUsers.length,
    totalSessions: activeSessions.reduce((sum, user) => sum + (user.sessions?.filter(s => s.isActive)?.length || 0), 0),
    avgSessionTime: 45, // Mock average session time in minutes
    trend: { direction: 'stable', percentage: 0 }
  };

  // Calculate trend (compare with previous 24 hours)
  const previousActiveUsers = await User.countDocuments({
    'activityLog.timestamp': { $gte: last48Hours, $lt: last24Hours }
  });

  if (previousActiveUsers > 0) {
    const change = ((summary.activeUsers - previousActiveUsers) / previousActiveUsers) * 100;
    if (Math.abs(change) > 5) {
      summary.trend = {
        direction: change > 0 ? 'up' : 'down',
        percentage: Math.round(Math.abs(change))
      };
    }
  }

  res.json({
    status: 'success',
    data: {
      summary,
      recentActivity,
      activeSessions: activeSessions.map(user => ({
        user: {
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        },
        sessionCount: user.sessions?.filter(s => s.isActive)?.length || 0
      })),
      lastUpdated: new Date()
    }
  });
}));

/**
 * @route   GET /api/v1/dashboards
 * @desc    Get all dashboards
 * @access  Private
 */
router.get('/', authorize(['view_dashboards']), catchAsync(async (req, res) => {
  // TODO: Implement dashboard retrieval
  res.json({
    status: 'success',
    message: 'Dashboard management not yet implemented',
    data: {
      dashboards: [],
    },
  });
}));

/**
 * @route   POST /api/v1/dashboards
 * @desc    Create new dashboard
 * @access  Private
 */
router.post('/', authorize(['manage_dashboards']), catchAsync(async (req, res) => {
  // TODO: Implement dashboard creation
  res.status(501).json({
    status: 'error',
    message: 'Dashboard creation not yet implemented',
  });
}));

module.exports = router;

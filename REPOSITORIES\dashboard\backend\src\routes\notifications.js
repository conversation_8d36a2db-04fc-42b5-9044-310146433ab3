const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { authenticateToken: authenticate, authorize } = require('../middleware/auth');
const Notification = require('../models/Notification');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/notifications
 * @desc    Get user notifications with filtering and pagination
 * @access  Private
 */
router.get('/', authenticate, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('category')
    .optional()
    .isIn(['alert', 'system', 'agent', 'report', 'security', 'user', 'general'])
    .withMessage('Invalid category'),
  query('type')
    .optional()
    .isIn(['info', 'success', 'warning', 'error'])
    .withMessage('Invalid type'),
  query('read')
    .optional()
    .isBoolean()
    .withMessage('Read must be a boolean'),
  query('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid severity'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const {
    page = 1,
    limit = 20,
    category,
    type,
    read,
    severity,
  } = req.query;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    category,
    type,
    read: read !== undefined ? read === 'true' : null,
    severity,
  };

  const result = await Notification.getUserNotifications(req.userId, options);

  res.json({
    status: 'success',
    data: result,
  });
}));

/**
 * @route   GET /api/v1/notifications/unread-count
 * @desc    Get count of unread notifications for the user
 * @access  Private
 */
router.get('/unread-count', authenticate, catchAsync(async (req, res) => {
  const count = await Notification.getUnreadCount(req.userId);

  res.json({
    status: 'success',
    data: {
      count,
    },
  });
}));

/**
 * @route   GET /api/v1/notifications/stats
 * @desc    Get notification statistics for the user
 * @access  Private
 */
router.get('/stats', authenticate, catchAsync(async (req, res) => {
  const stats = await Notification.getNotificationStats(req.userId);

  res.json({
    status: 'success',
    data: stats,
  });
}));

/**
 * @route   POST /api/v1/notifications
 * @desc    Create a new notification (admin only)
 * @access  Private (Admin)
 */
router.post('/', authenticate, authorize(['manage_users']), [
  body('userId')
    .isMongoId()
    .withMessage('Valid user ID is required'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('message')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message must be between 1 and 1000 characters'),
  body('type')
    .optional()
    .isIn(['info', 'success', 'warning', 'error'])
    .withMessage('Invalid type'),
  body('category')
    .optional()
    .isIn(['alert', 'system', 'agent', 'report', 'security', 'user', 'general'])
    .withMessage('Invalid category'),
  body('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid severity'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expires at must be a valid date'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const notificationData = {
    ...req.body,
    expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : null,
  };

  const notification = await Notification.createNotification(notificationData);

  logger.info(`Notification created for user ${req.body.userId}`, {
    notificationId: notification._id,
    createdBy: req.userId,
  });

  res.status(201).json({
    status: 'success',
    data: {
      notification,
    },
  });
}));

/**
 * @route   POST /api/v1/notifications/bulk
 * @desc    Create multiple notifications (admin only)
 * @access  Private (Admin)
 */
router.post('/bulk', authenticate, authorize(['manage_users']), [
  body('notifications')
    .isArray({ min: 1, max: 100 })
    .withMessage('Notifications must be an array with 1-100 items'),
  body('notifications.*.userId')
    .isMongoId()
    .withMessage('Valid user ID is required'),
  body('notifications.*.title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('notifications.*.message')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message must be between 1 and 1000 characters'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { notifications } = req.body;
  const batchId = new Date().getTime().toString();

  const notificationsWithBatch = notifications.map(notification => ({
    ...notification,
    batchId,
    expiresAt: notification.expiresAt ? new Date(notification.expiresAt) : null,
  }));

  const createdNotifications = await Notification.createBulkNotifications(notificationsWithBatch);

  logger.info(`Bulk notifications created`, {
    count: createdNotifications.length,
    batchId,
    createdBy: req.userId,
  });

  res.status(201).json({
    status: 'success',
    data: {
      notifications: createdNotifications,
      batchId,
      count: createdNotifications.length,
    },
  });
}));

/**
 * @route   PUT /api/v1/notifications/mark-read
 * @desc    Mark notifications as read
 * @access  Private
 */
router.put('/mark-read', authenticate, [
  body('notificationIds')
    .optional()
    .isArray()
    .withMessage('Notification IDs must be an array'),
  body('notificationIds.*')
    .optional()
    .isMongoId()
    .withMessage('Each notification ID must be valid'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { notificationIds } = req.body;

  const result = await Notification.markAsRead(req.userId, notificationIds);

  res.json({
    status: 'success',
    data: {
      modifiedCount: result.modifiedCount,
    },
  });
}));

/**
 * @route   DELETE /api/v1/notifications
 * @desc    Delete notifications
 * @access  Private
 */
router.delete('/', authenticate, [
  body('notificationIds')
    .isArray({ min: 1 })
    .withMessage('Notification IDs must be a non-empty array'),
  body('notificationIds.*')
    .isMongoId()
    .withMessage('Each notification ID must be valid'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { notificationIds } = req.body;

  const result = await Notification.deleteNotifications(req.userId, notificationIds);

  res.json({
    status: 'success',
    data: {
      deletedCount: result.deletedCount,
    },
  });
}));

/**
 * @route   GET /api/v1/notifications/:id
 * @desc    Get a specific notification
 * @access  Private
 */
router.get('/:id', authenticate, catchAsync(async (req, res) => {
  const { id } = req.params;

  const notification = await Notification.findOne({
    _id: id,
    userId: req.userId,
  });

  if (!notification) {
    throw new AppError('Notification not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      notification,
    },
  });
}));

/**
 * @route   PUT /api/v1/notifications/:id/read
 * @desc    Mark a specific notification as read
 * @access  Private
 */
router.put('/:id/read', authenticate, catchAsync(async (req, res) => {
  const { id } = req.params;

  const notification = await Notification.findOne({
    _id: id,
    userId: req.userId,
  });

  if (!notification) {
    throw new AppError('Notification not found', 404);
  }

  await notification.markAsRead();

  res.json({
    status: 'success',
    data: {
      notification,
    },
  });
}));

/**
 * @route   DELETE /api/v1/notifications/:id
 * @desc    Delete a specific notification
 * @access  Private
 */
router.delete('/:id', authenticate, catchAsync(async (req, res) => {
  const { id } = req.params;

  const result = await Notification.deleteOne({
    _id: id,
    userId: req.userId,
  });

  if (result.deletedCount === 0) {
    throw new AppError('Notification not found', 404);
  }

  res.json({
    status: 'success',
    message: 'Notification deleted successfully',
  });
}));

module.exports = router;

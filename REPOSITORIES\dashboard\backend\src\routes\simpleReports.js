const express = require('express');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize } = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');
const Log = require('../models/Log');
const SimpleReport = require('../models/SimpleReport');
const moment = require('moment');
const logger = require('../config/logger');

const router = express.Router();

/**
 * @route   POST /api/v1/simple-reports/generate
 * @desc    Generate report from log data with filters
 * @access  Private
 */
router.post('/generate', authorize(['view_reports']), catchAsync(async (req, res) => {
  const {
    startDate,
    endDate,
    logCategories = [],
    logLevels = [],
    agentTypes = [],
    searchKeywords = ''
  } = req.body;

  // Validate required fields
  if (!startDate || !endDate) {
    throw new AppError('Start date and end date are required', 400);
  }

  // Build MongoDB query
  const query = {
    timestamp: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  };

  // Add filters if provided
  if (logCategories.length > 0) {
    query.category = { $in: logCategories };
  }

  if (logLevels.length > 0) {
    query.logLevel = { $in: logLevels };
  }

  if (agentTypes.length > 0) {
    // Map agent types to metadata query
    const agentQuery = [];
    if (agentTypes.includes('Windows')) {
      agentQuery.push({ 'metadata.sourceMetadata.os': /windows/i });
    }
    if (agentTypes.includes('Linux')) {
      agentQuery.push({ 'metadata.sourceMetadata.os': /linux/i });
    }
    if (agentQuery.length > 0) {
      query.$or = agentQuery;
    }
  }

  if (searchKeywords.trim()) {
    query.$text = { $search: searchKeywords };
  }

  try {
    // Execute query with limit of 1000 records
    const logs = await Log.find(query)
      .select('timestamp host logLevel category message source metadata.agentId')
      .sort({ timestamp: -1 })
      .limit(1000)
      .lean();

    // Get total count for pagination info
    const totalCount = await Log.countDocuments(query);

    // Format response data
    const formattedLogs = logs.map(log => ({
      timestamp: log.timestamp,
      hostname: log.host,
      logLevel: log.logLevel,
      category: log.category || 'General',
      message: log.message,
      source: log.source,
      agentType: getAgentType(log.metadata?.sourceMetadata?.os || 'Unknown'),
      agentId: log.metadata?.agentId || 'N/A'
    }));

    res.json({
      status: 'success',
      data: {
        logs: formattedLogs,
        totalRecords: totalCount,
        returnedRecords: logs.length,
        filters: {
          startDate,
          endDate,
          logCategories,
          logLevels,
          agentTypes,
          searchKeywords
        },
        generatedAt: new Date()
      }
    });

  } catch (error) {
    logger.error('Report generation failed:', error);
    throw new AppError('Failed to generate report', 500);
  }
}));

/**
 * @route   POST /api/v1/simple-reports/save
 * @desc    Save report with query params and result snapshot
 * @access  Private
 */
router.post('/save', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const {
    name,
    description,
    filters,
    resultSnapshot
  } = req.body;

  if (!name || !filters || !resultSnapshot) {
    throw new AppError('Name, filters, and result snapshot are required', 400);
  }

  const savedReport = new SimpleReport({
    reportId: uuidv4(),
    name,
    description,
    owner: req.user.id,
    filters,
    resultSnapshot
  });

  await savedReport.save();

  res.status(201).json({
    status: 'success',
    data: { report: savedReport }
  });
}));

/**
 * @route   GET /api/v1/simple-reports/list
 * @desc    Get saved reports for logged-in user
 * @access  Private
 */
router.get('/list', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { page = 1, limit = 20 } = req.query;
  const userId = req.user.id;

  const reports = await SimpleReport.find({ owner: userId })
    .select('reportId name description filters.startDate filters.endDate resultSnapshot.totalRecords createdAt')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .lean();

  const total = await SimpleReport.countDocuments({ owner: userId });

  res.json({
    status: 'success',
    data: {
      reports,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * @route   GET /api/v1/simple-reports/:id
 * @desc    Get specific saved report content
 * @access  Private
 */
router.get('/:id', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await SimpleReport.findOne({
    reportId: req.params.id,
    owner: req.user.id
  });

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  res.json({
    status: 'success',
    data: { report }
  });
}));

/**
 * @route   DELETE /api/v1/simple-reports/:id
 * @desc    Delete saved report
 * @access  Private
 */
router.delete('/:id', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const report = await SimpleReport.findOneAndDelete({
    reportId: req.params.id,
    owner: req.user.id
  });

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  res.json({
    status: 'success',
    message: 'Report deleted successfully'
  });
}));

// Helper function to determine agent type from OS info
function getAgentType(osInfo) {
  if (!osInfo || osInfo === 'Unknown') return 'Unknown';

  const os = osInfo.toLowerCase();
  if (os.includes('windows')) return 'Windows';
  if (os.includes('linux')) return 'Linux';
  if (os.includes('darwin') || os.includes('mac')) return 'macOS';
  return 'Unknown';
}

module.exports = router;

const express = require('express');
const { body, validationResult } = require('express-validator');
const SystemSettings = require('../models/SystemSettings');
const { authenticateToken, authorize } = require('../middleware/auth');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { getEmailService } = require('../services/emailService');
const logger = require('../utils/logger');
const logRetentionService = require('../services/logRetentionService');
const storageService = require('../services/storageService');
const logExportService = require('../services/logExportService');
const configService = require('../services/configService');

const router = express.Router();

/**
 * @route   GET /api/v1/settings/system
 * @desc    Get system settings (admin only)
 * @access  Private (Admin)
 */
router.get('/system', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const settings = await SystemSettings.getCurrentSettings();

  res.json({
    status: 'success',
    data: {
      settings,
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/system/log-retention
 * @desc    Update log retention settings
 * @access  Private (Admin)
 */
router.put('/system/log-retention', authenticateToken, authorize(['system_admin']), [
  body('defaultRetentionDays')
    .optional()
    .isInt({ min: 1, max: 3650 })
    .withMessage('Default retention days must be between 1 and 3650'),
  body('autoArchiveEnabled')
    .optional()
    .isBoolean()
    .withMessage('Auto archive enabled must be a boolean'),
  body('archiveCompressionEnabled')
    .optional()
    .isBoolean()
    .withMessage('Archive compression enabled must be a boolean'),
  body('archiveLocation')
    .optional()
    .isIn(['local', 's3', 'azure', 'gcp'])
    .withMessage('Archive location must be local, s3, azure, or gcp'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const settings = await SystemSettings.getCurrentSettings();
  const { defaultRetentionDays, autoArchiveEnabled, archiveCompressionEnabled, archiveLocation } = req.body;

  // Update log retention settings
  if (defaultRetentionDays !== undefined) {
    settings.logRetention.defaultRetentionDays = defaultRetentionDays;
  }
  if (autoArchiveEnabled !== undefined) {
    settings.logRetention.autoArchiveEnabled = autoArchiveEnabled;
  }
  if (archiveCompressionEnabled !== undefined) {
    settings.logRetention.archiveCompressionEnabled = archiveCompressionEnabled;
  }
  if (archiveLocation !== undefined) {
    settings.logRetention.archiveLocation = archiveLocation;
  }

  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info('Log retention settings updated', { userId: req.userId, changes: req.body });

  res.json({
    status: 'success',
    message: 'Log retention settings updated successfully',
    data: {
      logRetention: settings.logRetention,
    },
  });
}));

/**
 * @route   POST /api/v1/settings/system/retention-policies
 * @desc    Create new retention policy
 * @access  Private (Admin)
 */
router.post('/system/retention-policies', authenticateToken, authorize(['system_admin']), [
  body('name')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Policy name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-_]+$/)
    .withMessage('Policy name can only contain letters, numbers, spaces, hyphens, and underscores'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('retentionDays')
    .isInt({ min: 1, max: 3650 })
    .withMessage('Retention days must be between 1 and 3650'),
  body('logSources')
    .optional()
    .isArray()
    .withMessage('Log sources must be an array'),
  body('logSources.*')
    .optional()
    .isIn(['System', 'Application', 'Security', 'Network', 'Custom'])
    .withMessage('Invalid log source'),
  body('logLevels')
    .optional()
    .isArray()
    .withMessage('Log levels must be an array'),
  body('logLevels.*')
    .optional()
    .isIn(['critical', 'error', 'warning', 'info', 'debug'])
    .withMessage('Invalid log level'),
  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
  body('priority')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Priority must be between 1 and 100'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const settings = await SystemSettings.getCurrentSettings();
  const { name, description, retentionDays, logSources, logLevels, isDefault, priority } = req.body;

  // Check if policy name already exists (case-insensitive)
  const existingPolicy = settings.logRetention.retentionPolicies.find(
    p => p.name.toLowerCase() === name.toLowerCase()
  );
  if (existingPolicy) {
    throw new AppError('A retention policy with this name already exists', 400);
  }

  // Validate that at least one filter is specified if not default
  if (!isDefault && (!logSources || logSources.length === 0) && (!logLevels || logLevels.length === 0)) {
    throw new AppError('Non-default policies must specify at least one log source or log level filter', 400);
  }

  // If this is set as default, unset other default policies
  if (isDefault) {
    settings.logRetention.retentionPolicies.forEach(policy => {
      policy.isDefault = false;
    });
  }

  const newPolicy = {
    name: name.trim(),
    description: description?.trim() || '',
    retentionDays,
    logSources: logSources || [],
    logLevels: logLevels || [],
    isDefault: isDefault || false,
    priority: priority || 50,
    createdAt: new Date(),
  };

  settings.logRetention.retentionPolicies.push(newPolicy);
  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info(`New retention policy created: ${name}`, {
    userId: req.userId,
    policyId: newPolicy._id,
    retentionDays,
    isDefault
  });

  res.status(201).json({
    status: 'success',
    message: 'Retention policy created successfully',
    data: {
      policy: settings.logRetention.retentionPolicies[settings.logRetention.retentionPolicies.length - 1],
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/system/retention-policies/:policyId
 * @desc    Update retention policy
 * @access  Private (Admin)
 */
router.put('/system/retention-policies/:policyId', authenticateToken, authorize(['system_admin']), [
  body('name')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Policy name cannot be empty'),
  body('description')
    .optional()
    .trim()
    .isString()
    .withMessage('Description must be a string'),
  body('retentionDays')
    .optional()
    .isInt({ min: 1, max: 3650 })
    .withMessage('Retention days must be between 1 and 3650'),
  body('logSources')
    .optional()
    .isArray()
    .withMessage('Log sources must be an array'),
  body('logLevels')
    .optional()
    .isArray()
    .withMessage('Log levels must be an array'),
  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { policyId } = req.params;
  const settings = await SystemSettings.getCurrentSettings();

  const policy = settings.logRetention.retentionPolicies.id(policyId);
  if (!policy) {
    throw new AppError('Retention policy not found', 404);
  }

  const { name, description, retentionDays, logSources, logLevels, isDefault } = req.body;

  // Check if new name conflicts with existing policies
  if (name && name !== policy.name) {
    const existingPolicy = settings.logRetention.retentionPolicies.find(p => p.name === name && p._id.toString() !== policyId);
    if (existingPolicy) {
      throw new AppError('A retention policy with this name already exists', 400);
    }
  }

  // If this is set as default, unset other default policies
  if (isDefault) {
    settings.logRetention.retentionPolicies.forEach(p => {
      if (p._id.toString() !== policyId) {
        p.isDefault = false;
      }
    });
  }

  // Update fields
  if (name !== undefined) policy.name = name;
  if (description !== undefined) policy.description = description;
  if (retentionDays !== undefined) policy.retentionDays = retentionDays;
  if (logSources !== undefined) policy.logSources = logSources;
  if (logLevels !== undefined) policy.logLevels = logLevels;
  if (isDefault !== undefined) policy.isDefault = isDefault;

  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info(`Retention policy updated: ${policy.name}`, { userId: req.userId, policyId });

  res.json({
    status: 'success',
    message: 'Retention policy updated successfully',
    data: {
      policy,
    },
  });
}));

/**
 * @route   DELETE /api/v1/settings/system/retention-policies/:policyId
 * @desc    Delete retention policy
 * @access  Private (Admin)
 */
router.delete('/system/retention-policies/:policyId', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const { policyId } = req.params;
  const settings = await SystemSettings.getCurrentSettings();

  const policy = settings.logRetention.retentionPolicies.id(policyId);
  if (!policy) {
    throw new AppError('Retention policy not found', 404);
  }

  // Prevent deletion if it's the only policy
  if (settings.logRetention.retentionPolicies.length === 1) {
    throw new AppError('Cannot delete the last retention policy', 400);
  }

  const policyName = policy.name;
  settings.logRetention.retentionPolicies.pull(policyId);
  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info(`Retention policy deleted: ${policyName}`, { userId: req.userId, policyId });

  res.json({
    status: 'success',
    message: 'Retention policy deleted successfully',
  });
}));

/**
 * @route   PUT /api/v1/settings/system/notifications
 * @desc    Update system notification settings
 * @access  Private (Admin)
 */
router.put('/system/notifications', authenticateToken, authorize(['system_admin']), [
  body('emailSettings.enabled')
    .optional()
    .isBoolean()
    .withMessage('Email enabled must be a boolean'),
  body('emailSettings.smtpHost')
    .optional()
    .trim()
    .isString()
    .withMessage('SMTP host must be a string'),
  body('emailSettings.smtpPort')
    .optional()
    .isInt({ min: 1, max: 65535 })
    .withMessage('SMTP port must be between 1 and 65535'),
  body('emailSettings.fromAddress')
    .optional()
    .isEmail()
    .withMessage('From address must be a valid email'),
  body('emailSettings.recipients.agentAlerts')
    .optional()
    .isArray()
    .withMessage('Agent alert recipients must be an array'),
  body('emailSettings.recipients.agentAlerts.*')
    .optional()
    .isEmail()
    .withMessage('Agent alert recipients must be valid email addresses'),
  body('emailSettings.recipients.systemAlerts')
    .optional()
    .isArray()
    .withMessage('System alert recipients must be an array'),
  body('emailSettings.recipients.systemAlerts.*')
    .optional()
    .isEmail()
    .withMessage('System alert recipients must be valid email addresses'),
  body('emailSettings.recipients.useAdminUsers')
    .optional()
    .isBoolean()
    .withMessage('Use admin users must be a boolean'),
  body('emailSettings.userNotifications.sendWelcomeEmails')
    .optional()
    .isBoolean()
    .withMessage('Send welcome emails must be a boolean'),
  body('emailSettings.userNotifications.sendLoginNotifications')
    .optional()
    .isBoolean()
    .withMessage('Send login notifications must be a boolean'),
  body('emailSettings.userNotifications.sendPasswordResetNotifications')
    .optional()
    .isBoolean()
    .withMessage('Send password reset notifications must be a boolean'),
  body('webhookSettings.enabled')
    .optional()
    .isBoolean()
    .withMessage('Webhook enabled must be a boolean'),
  body('webhookSettings.defaultWebhookUrl')
    .optional()
    .isURL()
    .withMessage('Webhook URL must be a valid URL'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const settings = await SystemSettings.getCurrentSettings();
  const { emailSettings, webhookSettings, slackIntegration } = req.body;

  // Update email settings
  if (emailSettings) {
    Object.keys(emailSettings).forEach(key => {
      // Allow updating any valid email setting field, even if it's currently undefined
      const validEmailFields = ['enabled', 'smtpHost', 'smtpPort', 'smtpSecure', 'smtpUser', 'smtpPassword', 'fromAddress', 'fromName', 'recipients', 'userNotifications'];
      if (validEmailFields.includes(key)) {
        if (key === 'recipients') {
          // Handle nested recipients object
          settings.systemNotifications.emailSettings.recipients = {
            ...settings.systemNotifications.emailSettings.recipients,
            ...emailSettings.recipients
          };
        } else if (key === 'userNotifications') {
          // Handle nested userNotifications object
          settings.systemNotifications.emailSettings.userNotifications = {
            ...settings.systemNotifications.emailSettings.userNotifications,
            ...emailSettings.userNotifications
          };
        } else {
          settings.systemNotifications.emailSettings[key] = emailSettings[key];
        }
      }
    });
  }

  // Update webhook settings
  if (webhookSettings) {
    Object.keys(webhookSettings).forEach(key => {
      // Allow updating any valid webhook setting field, even if it's currently undefined
      const validWebhookFields = ['enabled', 'defaultWebhookUrl', 'retryAttempts', 'timeout'];
      if (validWebhookFields.includes(key)) {
        settings.systemNotifications.webhookSettings[key] = webhookSettings[key];
      }
    });
  }

  // Update Slack integration
  if (slackIntegration) {
    Object.keys(slackIntegration).forEach(key => {
      // Allow updating any valid Slack setting field, even if it's currently undefined
      const validSlackFields = ['enabled', 'webhookUrl', 'channel', 'username'];
      if (validSlackFields.includes(key)) {
        settings.systemNotifications.slackIntegration[key] = slackIntegration[key];
      }
    });
  }

  settings.modifiedBy = req.userId;
  await settings.save();

  logger.info('System notification settings updated', { userId: req.userId });

  res.json({
    status: 'success',
    message: 'Notification settings updated successfully',
    data: {
      systemNotifications: settings.systemNotifications,
    },
  });
}));

/**
 * @route   POST /api/v1/settings/system/test-email
 * @desc    Send test email to verify email configuration
 * @access  Private (Admin)
 */
router.post('/system/test-email', authenticateToken, authorize(['system_admin']), [
  body('recipient')
    .isEmail()
    .withMessage('Valid recipient email is required'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { recipient } = req.body;
  const emailService = getEmailService();

  try {
    // Validate email configuration first
    const validation = await emailService.validateConfiguration();
    if (!validation.valid) {
      throw new AppError(validation.message, 400);
    }

    // Send test email
    const result = await emailService.sendTestEmail(recipient);

    logger.info('Test email sent successfully', {
      recipient,
      service: validation.service,
      userId: req.userId
    });

    res.json({
      status: 'success',
      message: 'Test email sent successfully',
      data: {
        recipient,
        service: validation.service,
        messageId: result.id || result.messageId,
      },
    });
  } catch (error) {
    logger.error('Failed to send test email:', error);
    throw new AppError(`Failed to send test email: ${error.message}`, 500);
  }
}));

/**
 * @route   GET /api/v1/settings/system/email-status
 * @desc    Check email configuration status
 * @access  Private (Admin)
 */
router.get('/system/email-status', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const emailService = getEmailService();
  const validation = await emailService.validateConfiguration();

  res.json({
    status: 'success',
    data: {
      configured: validation.valid,
      service: validation.service || null,
      message: validation.message,
    },
  });
}));

/**
 * @route   GET /api/v1/settings/system/retention-stats
 * @desc    Get log retention statistics
 * @access  Private (Admin)
 */
router.get('/system/retention-stats', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const retentionStats = logRetentionService.getStats();
  const storageStats = await storageService.getStorageStats();

  res.json({
    status: 'success',
    data: {
      retention: retentionStats,
      storage: storageStats,
    },
  });
}));

/**
 * @route   POST /api/v1/settings/system/trigger-cleanup
 * @desc    Manually trigger log retention cleanup
 * @access  Private (Admin)
 */
router.post('/system/trigger-cleanup', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  try {
    const stats = await logRetentionService.triggerCleanup();

    logger.info('Manual log retention cleanup triggered', { userId: req.userId });

    res.json({
      status: 'success',
      message: 'Log retention cleanup completed',
      data: stats,
    });
  } catch (error) {
    if (error.message === 'Cleanup is already running') {
      throw new AppError('Log retention cleanup is already running', 409);
    }
    throw error;
  }
}));

/**
 * @route   GET /api/v1/settings/system/archived-log/:logId
 * @desc    Retrieve an archived log
 * @access  Private (Admin)
 */
router.get('/system/archived-log/:logId', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  const { logId } = req.params;

  try {
    const archiveData = await storageService.retrieveArchive(logId);

    // Decompress if needed
    let logData;
    if (config.logStorage.compression.enabled) {
      const zlib = require('zlib');
      const { promisify } = require('util');

      if (config.logStorage.compression.algorithm === 'brotli') {
        const brotliDecompress = promisify(zlib.brotliDecompress);
        const decompressed = await brotliDecompress(archiveData);
        logData = JSON.parse(decompressed.toString());
      } else {
        const gunzip = promisify(zlib.gunzip);
        const decompressed = await gunzip(archiveData);
        logData = JSON.parse(decompressed.toString());
      }
    } else {
      logData = JSON.parse(archiveData.toString());
    }

    res.json({
      status: 'success',
      data: {
        log: logData,
      },
    });
  } catch (error) {
    if (error.message.includes('Archive not found')) {
      throw new AppError('Archived log not found', 404);
    }
    throw error;
  }
}));

/**
 * @route   POST /api/v1/settings/system/export-logs
 * @desc    Export logs based on criteria
 * @access  Private (Admin)
 */
router.post('/system/export-logs', authenticateToken, authorize(['system_admin']), [
  body('format')
    .optional()
    .isIn(['json', 'csv', 'txt'])
    .withMessage('Format must be json, csv, or txt'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  body('sources')
    .optional()
    .isArray()
    .withMessage('Sources must be an array'),
  body('levels')
    .optional()
    .isArray()
    .withMessage('Levels must be an array'),
  body('hosts')
    .optional()
    .isArray()
    .withMessage('Hosts must be an array'),
  body('limit')
    .optional()
    .isInt({ min: 1, max: 100000 })
    .withMessage('Limit must be between 1 and 100000'),
  body('includeArchived')
    .optional()
    .isBoolean()
    .withMessage('Include archived must be a boolean'),
  body('compress')
    .optional()
    .isBoolean()
    .withMessage('Compress must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { format = 'json', ...criteria } = req.body;

  try {
    const exportResult = await logExportService.exportLogs(criteria, format);

    logger.info('Log export requested', {
      userId: req.userId,
      format,
      criteria,
      filename: exportResult.filename,
    });

    res.json({
      status: 'success',
      message: 'Log export completed successfully',
      data: exportResult,
    });
  } catch (error) {
    logger.error('Log export failed:', error);
    throw new AppError(`Log export failed: ${error.message}`, 500);
  }
}));

/**
 * @route   POST /api/v1/settings/system/create-backup
 * @desc    Create a backup of all logs
 * @access  Private (Admin)
 */
router.post('/system/create-backup', authenticateToken, authorize(['system_admin']), [
  body('includeArchived')
    .optional()
    .isBoolean()
    .withMessage('Include archived must be a boolean'),
  body('compress')
    .optional()
    .isBoolean()
    .withMessage('Compress must be a boolean'),
  body('maxAge')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Max age must be between 1 and 365 days'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const options = req.body;

  try {
    const backupResult = await logExportService.createBackup(options);

    logger.info('Backup created', {
      userId: req.userId,
      backupDir: backupResult.backupDir,
      totalSize: backupResult.totalSize,
    });

    res.json({
      status: 'success',
      message: 'Backup created successfully',
      data: backupResult,
    });
  } catch (error) {
    logger.error('Backup creation failed:', error);
    throw new AppError(`Backup creation failed: ${error.message}`, 500);
  }
}));

/**
 * @route   GET /api/v1/settings/system/export-stats
 * @desc    Get export and backup statistics
 * @access  Private (Admin)
 */
router.get('/system/export-stats', authenticateToken, authorize(['system_admin']), catchAsync(async (req, res) => {
  try {
    const exportStats = await logExportService.getExportStats();

    res.json({
      status: 'success',
      data: exportStats,
    });
  } catch (error) {
    logger.error('Failed to get export stats:', error);
    throw new AppError(`Failed to get export stats: ${error.message}`, 500);
  }
}));

/**
 * @route   PUT /api/v1/settings/system
 * @desc    Update system settings
 * @access  Private (Admin)
 */
router.put('/system', authenticateToken, authorize(['system_admin']), [
  body('logStorage.localPaths.logsPath')
    .optional()
    .isString()
    .withMessage('Logs path must be a string'),
  body('logStorage.localPaths.storagePath')
    .optional()
    .isString()
    .withMessage('Storage path must be a string'),
  body('logStorage.localPaths.archivePath')
    .optional()
    .isString()
    .withMessage('Archive path must be a string'),
  body('logStorage.externalStorage.type')
    .optional()
    .isIn(['local', 's3', 'azure', 'gcp'])
    .withMessage('Invalid storage type'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  try {
    const updatedSettings = await configService.updateSettings(req.body);

    logger.info('System settings updated', {
      userId: req.userId,
      updates: Object.keys(req.body),
    });

    res.json({
      status: 'success',
      message: 'System settings updated successfully',
      data: {
        settings: updatedSettings,
      },
    });
  } catch (error) {
    logger.error('Failed to update system settings:', error);
    throw new AppError(`Failed to update system settings: ${error.message}`, 500);
  }
}));

/**
 * @route   POST /api/v1/settings/system/test-storage
 * @desc    Test storage connection
 * @access  Private (Admin)
 */
router.post('/system/test-storage', authenticateToken, authorize(['system_admin']), [
  body('type')
    .isIn(['local', 's3', 'azure', 'gcp'])
    .withMessage('Invalid storage type'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  try {
    const result = await configService.testStorageConnection(req.body);

    logger.info('Storage connection tested', {
      userId: req.userId,
      storageType: req.body.type,
      success: result.success,
    });

    res.json({
      status: 'success',
      message: result.message,
      data: result,
    });
  } catch (error) {
    logger.error('Storage connection test failed:', error);
    throw new AppError(`Storage connection test failed: ${error.message}`, 500);
  }
}));

module.exports = router;

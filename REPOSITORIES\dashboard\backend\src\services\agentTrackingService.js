const Agent = require('../models/Agent');
const User = require('../models/User');
const SystemSettings = require('../models/SystemSettings');
const { getEmailService } = require('./emailService');
const notificationService = require('./notificationService');
const logger = require('../utils/logger');

class AgentTrackingService {
  constructor() {
    this.updateInterval = 60000; // 1 minute
    this.intervalId = null;
    this.isRunning = false;
  }

  /**
   * Start the agent tracking service
   */
  start() {
    if (this.isRunning) {
      logger.warn('Agent tracking service is already running');
      return;
    }

    logger.info('Starting agent tracking service');
    this.isRunning = true;

    // Run immediately
    this.updateAgentStatuses();

    // Set up periodic updates
    this.intervalId = setInterval(() => {
      this.updateAgentStatuses();
    }, this.updateInterval);
  }

  /**
   * Stop the agent tracking service
   */
  stop() {
    if (!this.isRunning) {
      logger.warn('Agent tracking service is not running');
      return;
    }

    logger.info('Stopping agent tracking service');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Send agent status notification emails
   */
  async sendAgentStatusNotifications(agents, newStatus) {
    try {
      // Only send notifications for critical status changes
      if (newStatus !== 'offline') {
        return;
      }

      const emailService = getEmailService();

      // Get configured recipients for agent alerts
      const recipients = await emailService.getConfiguredRecipients('agentAlerts');

      if (recipients.length === 0) {
        logger.warn('No recipients configured for agent status notifications');
        return;
      }

      for (const agent of agents) {
        try {
          await emailService.sendAgentStatusNotificationEmail(recipients, {
            agentName: agent.name,
            hostname: agent.hostname,
            status: newStatus,
            lastHeartbeat: agent.lastHeartbeat ? agent.lastHeartbeat.toLocaleString() : 'Unknown'
          });

          logger.info(`Agent status notification sent for ${agent.name} (${newStatus}) to ${recipients.length} recipients`);
        } catch (error) {
          logger.error(`Failed to send agent status notification for ${agent.name}:`, error);
        }
      }
    } catch (error) {
      logger.error('Failed to send agent status notifications:', error);
    }
  }

  /**
   * Update agent statuses based on last heartbeat
   */
  async updateAgentStatuses() {
    try {
      const now = new Date();
      const twoMinutesAgo = new Date(now.getTime() - 2 * 60 * 1000);
      const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);

      // Get agents that will change to offline status for notifications
      const agentsGoingOffline = await Agent.find({
        isActive: true,
        lastHeartbeat: { $lt: tenMinutesAgo },
        status: { $ne: 'offline' }
      });

      // Update agents to online status (heartbeat within 2 minutes)
      const onlineResult = await Agent.updateMany(
        {
          isActive: true,
          lastHeartbeat: { $gte: twoMinutesAgo },
          status: { $ne: 'online' }
        },
        {
          $set: { status: 'online' }
        }
      );

      // Update agents to warning status (heartbeat between 2-10 minutes ago)
      const warningResult = await Agent.updateMany(
        {
          isActive: true,
          lastHeartbeat: { $gte: tenMinutesAgo, $lt: twoMinutesAgo },
          status: { $ne: 'warning' }
        },
        {
          $set: { status: 'warning' }
        }
      );

      // Update agents to offline status (heartbeat more than 10 minutes ago)
      const offlineResult = await Agent.updateMany(
        {
          isActive: true,
          lastHeartbeat: { $lt: tenMinutesAgo },
          status: { $ne: 'offline' }
        },
        {
          $set: { status: 'offline' }
        }
      );

      // Send notifications for agents going offline
      if (agentsGoingOffline.length > 0) {
        setImmediate(() => {
          this.sendAgentStatusNotifications(agentsGoingOffline, 'offline');
          // Also create in-app notifications
          this.createAgentStatusNotifications(agentsGoingOffline, 'offline');
        });
      }

      const totalUpdated = onlineResult.modifiedCount + warningResult.modifiedCount + offlineResult.modifiedCount;

      if (totalUpdated > 0) {
        logger.info(`Updated agent statuses: ${onlineResult.modifiedCount} online, ${warningResult.modifiedCount} warning, ${offlineResult.modifiedCount} offline`);
      }

      // Log statistics periodically
      const stats = await this.getAgentStatistics();
      logger.debug(`Agent statistics: ${stats.total} total, ${stats.online} online, ${stats.warning} warning, ${stats.offline} offline, ${stats.error} error`);

    } catch (error) {
      logger.error('Failed to update agent statuses:', error);
    }
  }

  /**
   * Get current agent statistics
   */
  async getAgentStatistics() {
    try {
      const [totalAgents, statusCounts] = await Promise.all([
        Agent.countDocuments({ isActive: true }),
        Agent.aggregate([
          { $match: { isActive: true } },
          { $group: { _id: '$status', count: { $sum: 1 } } }
        ])
      ]);

      const stats = {
        total: totalAgents,
        online: 0,
        offline: 0,
        warning: 0,
        error: 0
      };

      statusCounts.forEach(item => {
        stats[item._id] = item.count;
      });

      return stats;
    } catch (error) {
      logger.error('Failed to get agent statistics:', error);
      return {
        total: 0,
        online: 0,
        offline: 0,
        warning: 0,
        error: 0
      };
    }
  }

  /**
   * Get agents that need attention (offline, warning, or error status)
   */
  async getAgentsNeedingAttention() {
    try {
      const agents = await Agent.find({
        isActive: true,
        status: { $in: ['offline', 'warning', 'error'] }
      })
        .sort({ lastHeartbeat: 1 }) // Oldest first
        .select('agentId hostname status lastHeartbeat platform')
        .lean();

      return agents;
    } catch (error) {
      logger.error('Failed to get agents needing attention:', error);
      return [];
    }
  }

  /**
   * Clean up old inactive agents (optional - for maintenance)
   */
  async cleanupInactiveAgents(daysInactive = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysInactive);

      const result = await Agent.updateMany(
        {
          isActive: true,
          lastHeartbeat: { $lt: cutoffDate }
        },
        {
          $set: {
            isActive: false,
            status: 'offline'
          }
        }
      );

      if (result.modifiedCount > 0) {
        logger.info(`Cleaned up ${result.modifiedCount} inactive agents (inactive for ${daysInactive} days)`);
      }

      return result.modifiedCount;
    } catch (error) {
      logger.error('Failed to cleanup inactive agents:', error);
      return 0;
    }
  }

  /**
   * Create in-app notifications for agent status changes
   */
  async createAgentStatusNotifications(agents, status) {
    try {
      for (const agent of agents) {
        try {
          // Get users who should receive agent notifications
          const users = await User.find({
            status: 'active',
            'permissions': { $in: ['view_agents'] },
          }).select('_id');

          if (users.length === 0) {
            continue;
          }

          const userIds = users.map(user => user._id);

          // Create notifications using the notification service
          await notificationService.createAgentNotification(agent, status, userIds);

          logger.debug(`Created agent status notifications for ${userIds.length} users for agent: ${agent.hostname}`);
        } catch (error) {
          logger.error(`Failed to create agent status notification for ${agent.hostname}:`, error);
        }
      }
    } catch (error) {
      logger.error('Failed to create agent status notifications:', error);
    }
  }

  /**
   * Force update agent status
   */
  async forceUpdateAgentStatus(agentId, status) {
    try {
      const agent = await Agent.findOne({ agentId });
      if (!agent) {
        throw new Error('Agent not found');
      }

      const previousStatus = agent.status;
      agent.status = status;
      if (status === 'offline') {
        // Don't update lastHeartbeat for manual offline status
      } else {
        agent.lastHeartbeat = new Date();
        agent.lastSeen = new Date();
      }

      await agent.save();
      logger.info(`Force updated agent ${agentId} status to ${status}`);

      // Create notification for manual status change
      if (previousStatus !== status) {
        setImmediate(() => {
          this.createAgentStatusNotifications([agent], status);
        });
      }

      return agent;
    } catch (error) {
      logger.error(`Failed to force update agent ${agentId} status:`, error);
      throw error;
    }
  }
}

// Create singleton instance
const agentTrackingService = new AgentTrackingService();

module.exports = agentTrackingService;

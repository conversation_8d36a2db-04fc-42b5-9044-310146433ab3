const logger = require('../utils/logger');
const User = require('../models/User');
const Notification = require('../models/Notification');

class AIAlertService {
  constructor() {
    this.alertThresholds = {
      anomaly: {
        minConfidence: 70,
        riskLevels: ['high', 'critical']
      },
      securityPattern: {
        severities: ['high', 'critical']
      },
      threatLevel: {
        levels: ['high', 'critical']
      }
    };
  }

  /**
   * Process AI insights and generate alerts
   */
  async processInsights(insights) {
    try {
      const alerts = [];

      // Generate alerts for high-confidence anomalies
      if (insights.anomalies && insights.anomalies.length > 0) {
        for (const anomaly of insights.anomalies) {
          if (this.shouldAlertForAnomaly(anomaly)) {
            alerts.push(this.createAnomalyAlert(anomaly));
          }
        }
      }

      // Generate alerts for critical security patterns
      if (insights.securityPatterns && insights.securityPatterns.length > 0) {
        for (const pattern of insights.securityPatterns) {
          if (this.shouldAlertForPattern(pattern)) {
            alerts.push(this.createPatternAlert(pattern));
          }
        }
      }

      // Generate alerts for elevated threat levels
      if (insights.summary && this.shouldAlertForThreatLevel(insights.summary)) {
        alerts.push(this.createThreatLevelAlert(insights.summary));
      }

      // Create notifications for all alerts
      if (alerts.length > 0) {
        await this.createNotifications(alerts);
        logger.info(`Generated ${alerts.length} AI-driven alerts`);
      }

      return alerts;
    } catch (error) {
      logger.error('Error processing AI insights for alerts:', error);
      return [];
    }
  }

  shouldAlertForAnomaly(anomaly) {
    return anomaly.confidence >= this.alertThresholds.anomaly.minConfidence ||
           this.alertThresholds.anomaly.riskLevels.includes(anomaly.riskLevel?.toLowerCase());
  }

  shouldAlertForPattern(pattern) {
    return this.alertThresholds.securityPattern.severities.includes(pattern.severity?.toLowerCase());
  }

  shouldAlertForThreatLevel(summary) {
    return this.alertThresholds.threatLevel.levels.includes(summary.riskLevel?.toLowerCase());
  }

  createAnomalyAlert(anomaly) {
    return {
      type: 'ai_anomaly',
      severity: this.mapRiskToSeverity(anomaly.riskLevel),
      title: `High-Confidence Anomaly Detected`,
      message: `${anomaly.description} (Confidence: ${anomaly.confidence}%)`,
      metadata: {
        anomalyId: anomaly.id,
        confidence: anomaly.confidence,
        riskLevel: anomaly.riskLevel,
        logSource: anomaly.sourceType,
        timestamp: anomaly.timestamp
      }
    };
  }

  createPatternAlert(pattern) {
    return {
      type: 'ai_security_pattern',
      severity: pattern.severity,
      title: `Security Pattern Detected: ${pattern.name}`,
      message: pattern.description,
      metadata: {
        patternId: pattern.id,
        patternType: pattern.type,
        mitreId: pattern.techniqueId,
        confidence: pattern.confidence,
        category: pattern.category
      }
    };
  }

  createThreatLevelAlert(summary) {
    return {
      type: 'ai_threat_level',
      severity: summary.riskLevel.toLowerCase(),
      title: `Elevated Threat Level: ${summary.riskLevel}`,
      message: `AI analysis indicates ${summary.riskLevel.toLowerCase()} threat level based on ${summary.logsAnalyzed} logs analyzed.`,
      metadata: {
        riskLevel: summary.riskLevel,
        logsAnalyzed: summary.logsAnalyzed,
        analysisTime: summary.analysisTime,
        anomalies: summary.anomalies,
        patterns: summary.patterns
      }
    };
  }

  mapRiskToSeverity(riskLevel) {
    const mapping = {
      'low': 'low',
      'medium': 'medium',
      'high': 'high',
      'critical': 'critical'
    };
    return mapping[riskLevel?.toLowerCase()] || 'medium';
  }

  async createNotifications(alerts) {
    try {
      // Get users who should receive AI alerts
      const users = await User.find({
        status: 'active',
        $or: [
          { role: { $in: ['admin', 'system_admin'] } },
          { 'permissions': { $in: ['view_alerts', 'manage_alerts'] } }
        ]
      }).select('_id preferences');

      for (const alert of alerts) {
        // Filter users based on their AI alert preferences
        const eligibleUsers = users.filter(user => this.shouldNotifyUser(user, alert));

        if (eligibleUsers.length === 0) {
          continue;
        }

        // Create notification data
        const notificationData = {
          title: alert.title,
          message: alert.message,
          type: this.mapSeverityToNotificationType(alert.severity),
          category: 'security',
          severity: alert.severity,
          sourceType: 'ai',
          metadata: {
            aiAlertType: alert.type,
            ...alert.metadata
          },
          actions: [{
            label: 'View AI Insights',
            url: '/ai-insights',
            style: 'primary'
          }]
        };

        // Create notifications for eligible users
        for (const user of eligibleUsers) {
          try {
            await Notification.createNotification({
              userId: user._id,
              ...notificationData
            });
          } catch (error) {
            logger.error(`Failed to create AI alert notification for user ${user._id}:`, error);
          }
        }

        logger.info(`Created AI alert notifications for ${eligibleUsers.length} users: ${alert.title}`);
      }
    } catch (error) {
      logger.error('Error creating AI alert notifications:', error);
    }
  }

  shouldNotifyUser(user, alert) {
    const aiPrefs = user.preferences?.notifications?.ai || {};
    const severityPrefs = user.preferences?.notifications?.alerts || {};

    // Check if user has AI notifications enabled (default: true)
    const aiEnabled = aiPrefs.enabled !== false;

    // Check if user wants alerts of this severity
    const severityEnabled = alert.severity === 'critical' || alert.severity === 'high'
      ? severityPrefs[alert.severity] !== false
      : severityPrefs[alert.severity] === true;

    // Check specific AI alert type preferences
    const typeEnabled = aiPrefs[this.getAlertTypeKey(alert.type)] !== false;

    return aiEnabled && severityEnabled && typeEnabled;
  }

  getAlertTypeKey(alertType) {
    const mapping = {
      'ai_anomaly': 'anomalies',
      'ai_security_pattern': 'securityPatterns',
      'ai_threat_level': 'threatLevel'
    };
    return mapping[alertType] || 'anomalies';
  }

  mapSeverityToNotificationType(severity) {
    const mapping = {
      'low': 'info',
      'medium': 'warning',
      'high': 'warning',
      'critical': 'error'
    };
    return mapping[severity] || 'info';
  }
}

module.exports = AIAlertService;

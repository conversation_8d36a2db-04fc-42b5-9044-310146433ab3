const cron = require('node-cron');
const logger = require('../utils/logger');
const AIAnalysisResult = require('../models/AIAnalysisResult');
const AIConfig = require('../models/AIConfig');
const aiSchedulerService = require('./aiSchedulerService');
const dataRetentionService = require('./dataRetentionService');
const notificationService = require('./notificationService');

class AIMonitoringService {
  constructor() {
    this.initialized = false;
    this.monitoringJob = null;
    this.healthCheckJob = null;
    this.metrics = {
      totalAnalyses: 0,
      successfulAnalyses: 0,
      failedAnalyses: 0,
      averageAnalysisTime: 0,
      lastHealthCheck: null,
      systemHealth: 'unknown',
      alerts: []
    };
    this.alertThresholds = {
      maxFailureRate: 0.2, // 20% failure rate threshold
      maxAnalysisTime: 300000, // 5 minutes in milliseconds
      minSuccessRate: 0.8, // 80% success rate threshold
      maxConsecutiveFailures: 3
    };
  }

  async initialize() {
    try {
      logger.info('Initializing AI Monitoring Service...');
      
      // Load initial metrics
      await this.loadMetrics();
      
      // Schedule monitoring jobs
      this.scheduleMonitoringJobs();
      
      this.initialized = true;
      logger.info('AI Monitoring Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Monitoring Service:', error);
      throw error;
    }
  }

  async loadMetrics() {
    try {
      // Load metrics from the last 24 hours
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const results = await AIAnalysisResult.aggregate([
        {
          $match: {
            'execution.startedAt': { $gte: last24Hours }
          }
        },
        {
          $group: {
            _id: null,
            totalAnalyses: { $sum: 1 },
            successfulAnalyses: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            failedAnalyses: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
            },
            averageAnalysisTime: { $avg: '$execution.duration' }
          }
        }
      ]);

      if (results.length > 0) {
        const stats = results[0];
        this.metrics.totalAnalyses = stats.totalAnalyses;
        this.metrics.successfulAnalyses = stats.successfulAnalyses;
        this.metrics.failedAnalyses = stats.failedAnalyses;
        this.metrics.averageAnalysisTime = stats.averageAnalysisTime || 0;
      }

      logger.info('Loaded AI monitoring metrics');
    } catch (error) {
      logger.error('Error loading AI monitoring metrics:', error);
    }
  }

  scheduleMonitoringJobs() {
    // Health check every 5 minutes
    this.healthCheckJob = cron.schedule('*/5 * * * *', async () => {
      await this.performHealthCheck();
    }, {
      scheduled: true,
      timezone: 'UTC'
    });

    // Comprehensive monitoring every 15 minutes
    this.monitoringJob = cron.schedule('*/15 * * * *', async () => {
      await this.performMonitoring();
    }, {
      scheduled: true,
      timezone: 'UTC'
    });

    logger.info('Scheduled AI monitoring jobs');
  }

  async performHealthCheck() {
    try {
      const healthStatus = {
        timestamp: new Date(),
        scheduler: await this.checkSchedulerHealth(),
        dataRetention: await this.checkDataRetentionHealth(),
        recentAnalyses: await this.checkRecentAnalysesHealth(),
        systemResources: await this.checkSystemResourcesHealth()
      };

      // Calculate overall health
      const healthScores = Object.values(healthStatus).filter(item => 
        typeof item === 'object' && item.score !== undefined
      );
      
      const averageScore = healthScores.reduce((sum, item) => sum + item.score, 0) / healthScores.length;
      
      if (averageScore >= 0.8) {
        this.metrics.systemHealth = 'healthy';
      } else if (averageScore >= 0.6) {
        this.metrics.systemHealth = 'warning';
      } else {
        this.metrics.systemHealth = 'critical';
      }

      this.metrics.lastHealthCheck = healthStatus.timestamp;

      // Generate alerts if needed
      await this.checkForAlerts(healthStatus);

      logger.debug(`Health check completed - System health: ${this.metrics.systemHealth}`);
    } catch (error) {
      logger.error('Error during health check:', error);
      this.metrics.systemHealth = 'error';
    }
  }

  async checkSchedulerHealth() {
    try {
      const schedulerStatus = aiSchedulerService.getScheduleStatus();
      
      let score = 1.0;
      const issues = [];

      if (!schedulerStatus.initialized) {
        score -= 0.5;
        issues.push('Scheduler not initialized');
      }

      if (schedulerStatus.scheduledJobs === 0) {
        score -= 0.3;
        issues.push('No scheduled jobs');
      }

      return {
        score: Math.max(0, score),
        status: score >= 0.8 ? 'healthy' : score >= 0.5 ? 'warning' : 'critical',
        details: schedulerStatus,
        issues
      };
    } catch (error) {
      return {
        score: 0,
        status: 'error',
        error: error.message,
        issues: ['Scheduler health check failed']
      };
    }
  }

  async checkDataRetentionHealth() {
    try {
      const retentionStatus = dataRetentionService.getRetentionStatus();
      
      let score = 1.0;
      const issues = [];

      if (!retentionStatus.initialized) {
        score -= 0.5;
        issues.push('Data retention service not initialized');
      }

      return {
        score: Math.max(0, score),
        status: score >= 0.8 ? 'healthy' : 'warning',
        details: retentionStatus,
        issues
      };
    } catch (error) {
      return {
        score: 0,
        status: 'error',
        error: error.message,
        issues: ['Data retention health check failed']
      };
    }
  }

  async checkRecentAnalysesHealth() {
    try {
      const last30Minutes = new Date(Date.now() - 30 * 60 * 1000);
      
      const recentAnalyses = await AIAnalysisResult.find({
        'execution.startedAt': { $gte: last30Minutes }
      }).sort({ 'execution.startedAt': -1 });

      let score = 1.0;
      const issues = [];

      if (recentAnalyses.length === 0) {
        // Check if there should be analyses based on active configs
        const activeConfigs = await AIConfig.find({ 
          isActive: true,
          'analysisSettings.autoScheduling.enabled': true 
        });
        
        if (activeConfigs.length > 0) {
          score -= 0.3;
          issues.push('No recent analyses despite active configurations');
        }
      } else {
        const failedCount = recentAnalyses.filter(a => a.status === 'failed').length;
        const failureRate = failedCount / recentAnalyses.length;
        
        if (failureRate > this.alertThresholds.maxFailureRate) {
          score -= 0.4;
          issues.push(`High failure rate: ${Math.round(failureRate * 100)}%`);
        }

        // Check for stuck analyses
        const stuckAnalyses = recentAnalyses.filter(a => 
          a.status === 'running' && 
          (Date.now() - new Date(a.execution.startedAt).getTime()) > this.alertThresholds.maxAnalysisTime
        );

        if (stuckAnalyses.length > 0) {
          score -= 0.3;
          issues.push(`${stuckAnalyses.length} analyses appear to be stuck`);
        }
      }

      return {
        score: Math.max(0, score),
        status: score >= 0.8 ? 'healthy' : score >= 0.5 ? 'warning' : 'critical',
        details: {
          recentCount: recentAnalyses.length,
          failedCount: recentAnalyses.filter(a => a.status === 'failed').length,
          runningCount: recentAnalyses.filter(a => a.status === 'running').length
        },
        issues
      };
    } catch (error) {
      return {
        score: 0,
        status: 'error',
        error: error.message,
        issues: ['Recent analyses health check failed']
      };
    }
  }

  async checkSystemResourcesHealth() {
    try {
      const memoryUsage = process.memoryUsage();
      const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
      
      let score = 1.0;
      const issues = [];

      // Check memory usage (warning at 1GB, critical at 2GB)
      if (memoryUsageMB > 2048) {
        score -= 0.5;
        issues.push(`High memory usage: ${Math.round(memoryUsageMB)}MB`);
      } else if (memoryUsageMB > 1024) {
        score -= 0.2;
        issues.push(`Elevated memory usage: ${Math.round(memoryUsageMB)}MB`);
      }

      return {
        score: Math.max(0, score),
        status: score >= 0.8 ? 'healthy' : score >= 0.5 ? 'warning' : 'critical',
        details: {
          memoryUsageMB: Math.round(memoryUsageMB),
          uptime: process.uptime()
        },
        issues
      };
    } catch (error) {
      return {
        score: 0,
        status: 'error',
        error: error.message,
        issues: ['System resources health check failed']
      };
    }
  }

  async checkForAlerts(healthStatus) {
    try {
      const alerts = [];

      // Check each component for critical issues
      Object.entries(healthStatus).forEach(([component, status]) => {
        if (typeof status === 'object' && status.status === 'critical') {
          alerts.push({
            type: 'critical',
            component,
            message: `${component} is in critical state`,
            issues: status.issues || [],
            timestamp: new Date()
          });
        }
      });

      // Check for consecutive failures
      const recentFailures = await this.getConsecutiveFailures();
      if (recentFailures >= this.alertThresholds.maxConsecutiveFailures) {
        alerts.push({
          type: 'warning',
          component: 'analysis',
          message: `${recentFailures} consecutive analysis failures detected`,
          timestamp: new Date()
        });
      }

      // Store alerts and send notifications
      if (alerts.length > 0) {
        this.metrics.alerts = alerts;
        await this.sendAlerts(alerts);
      }

    } catch (error) {
      logger.error('Error checking for alerts:', error);
    }
  }

  async getConsecutiveFailures() {
    try {
      const recentAnalyses = await AIAnalysisResult.find({})
        .sort({ 'execution.startedAt': -1 })
        .limit(10);

      let consecutiveFailures = 0;
      for (const analysis of recentAnalyses) {
        if (analysis.status === 'failed') {
          consecutiveFailures++;
        } else {
          break;
        }
      }

      return consecutiveFailures;
    } catch (error) {
      logger.error('Error getting consecutive failures:', error);
      return 0;
    }
  }

  async sendAlerts(alerts) {
    try {
      for (const alert of alerts) {
        logger.warn(`AI System Alert [${alert.type}]: ${alert.message}`, {
          component: alert.component,
          issues: alert.issues
        });

        // Send notification if notification service is available
        if (notificationService && typeof notificationService.sendAlert === 'function') {
          await notificationService.sendAlert({
            title: `AI System Alert: ${alert.component}`,
            message: alert.message,
            severity: alert.type,
            metadata: {
              component: alert.component,
              issues: alert.issues
            }
          });
        }
      }
    } catch (error) {
      logger.error('Error sending alerts:', error);
    }
  }

  async performMonitoring() {
    try {
      logger.info('Performing comprehensive AI monitoring...');
      
      // Update metrics
      await this.loadMetrics();
      
      // Check for performance degradation
      await this.checkPerformanceTrends();
      
      logger.info('Comprehensive monitoring completed');
    } catch (error) {
      logger.error('Error during comprehensive monitoring:', error);
    }
  }

  async checkPerformanceTrends() {
    try {
      // Analyze performance trends over the last week
      const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      const performanceStats = await AIAnalysisResult.getPerformanceStats(null, 7);
      
      if (performanceStats.length > 0) {
        const stats = performanceStats[0];
        
        // Check if average analysis time is increasing
        if (stats.avgDuration > this.alertThresholds.maxAnalysisTime) {
          logger.warn(`Average analysis time is high: ${Math.round(stats.avgDuration / 1000)}s`);
        }
        
        // Check success rate
        const successRate = stats.successfulAnalyses / stats.totalAnalyses;
        if (successRate < this.alertThresholds.minSuccessRate) {
          logger.warn(`Low success rate detected: ${Math.round(successRate * 100)}%`);
        }
      }
    } catch (error) {
      logger.error('Error checking performance trends:', error);
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      initialized: this.initialized
    };
  }

  async shutdown() {
    logger.info('Shutting down AI Monitoring Service...');
    
    if (this.healthCheckJob) {
      this.healthCheckJob.stop();
    }
    
    if (this.monitoringJob) {
      this.monitoringJob.stop();
    }
    
    this.initialized = false;
    logger.info('AI Monitoring Service shutdown complete');
  }
}

module.exports = new AIMonitoringService();

const cron = require('node-cron');
const logger = require('../utils/logger');
const AIConfig = require('../models/AIConfig');
const AIAnalysisResult = require('../models/AIAnalysisResult');
const { v4: uuidv4 } = require('uuid');

class AISchedulerService {
  constructor() {
    this.scheduledJobs = new Map(); // configId -> cron job
    this.runningAnalyses = new Map(); // analysisId -> analysis info
    this.initialized = false;
    this.shutdownInProgress = false;
    this.recoveryInProgress = false;
  }

  async initialize() {
    try {
      logger.info('Initializing AI Scheduler Service...');

      // Perform system restart recovery
      await this.performStartupRecovery();

      // Load all active configurations and schedule them
      await this.loadActiveConfigurations();

      // Schedule cleanup job for expired results
      this.scheduleCleanupJob();

      this.initialized = true;
      this.initTime = Date.now();
      logger.info('AI Scheduler Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Scheduler Service:', error);
      throw error;
    }
  }

  async performStartupRecovery() {
    try {
      this.recoveryInProgress = true;
      logger.info('Performing startup recovery...');

      // Find any analyses that were running when the system shut down
      const stuckAnalyses = await AIAnalysisResult.find({
        status: 'running',
        'execution.startedAt': {
          $lt: new Date(Date.now() - 30 * 60 * 1000) // Started more than 30 minutes ago
        }
      });

      if (stuckAnalyses.length > 0) {
        logger.info(`Found ${stuckAnalyses.length} stuck analyses from previous session`);

        for (const analysis of stuckAnalyses) {
          try {
            // Mark as failed due to system restart
            await analysis.markFailed('System restart - analysis interrupted');
            logger.info(`Marked analysis ${analysis.analysisId} as failed due to system restart`);
          } catch (error) {
            logger.error(`Error marking analysis ${analysis.analysisId} as failed:`, error);
          }
        }
      }

      // Clean up any orphaned temporary data or locks
      await this.cleanupOrphanedData();

      logger.info('Startup recovery completed');
    } catch (error) {
      logger.error('Error during startup recovery:', error);
      // Don't fail initialization due to recovery issues
    } finally {
      this.recoveryInProgress = false;
    }
  }

  async cleanupOrphanedData() {
    try {
      // Remove any temporary files or cleanup locks that might be left over
      // This is a placeholder for any cleanup logic you might need

      // For example, you could clean up any temporary analysis files
      // or reset any distributed locks that might be stuck

      logger.info('Orphaned data cleanup completed');
    } catch (error) {
      logger.error('Error during orphaned data cleanup:', error);
    }
  }

  async loadActiveConfigurations() {
    try {
      const activeConfigs = await AIConfig.find({
        isActive: true,
        'analysisSettings.autoScheduling.enabled': true
      });

      logger.info(`Found ${activeConfigs.length} active configurations with scheduling enabled`);

      for (const config of activeConfigs) {
        await this.scheduleAnalysis(config);
      }
    } catch (error) {
      logger.error('Error loading active configurations:', error);
      throw error;
    }
  }

  async scheduleAnalysis(config) {
    try {
      const configId = config._id.toString();
      const schedulingConfig = config.getSchedulingConfig();

      // Stop existing job if any
      if (this.scheduledJobs.has(configId)) {
        this.scheduledJobs.get(configId).stop();
        this.scheduledJobs.delete(configId);
      }

      if (!schedulingConfig.enabled) {
        logger.info(`Scheduling disabled for config ${config.name}`);
        return;
      }

      logger.info(`Scheduling analysis for config ${config.name} with cron: ${schedulingConfig.cronExpression}`);

      // Create and start the cron job
      const job = cron.schedule(schedulingConfig.cronExpression, async () => {
        await this.executeScheduledAnalysis(config);
      }, {
        scheduled: true,
        timezone: schedulingConfig.timezone
      });

      this.scheduledJobs.set(configId, job);
      logger.info(`Successfully scheduled analysis for config ${config.name}`);
    } catch (error) {
      logger.error(`Error scheduling analysis for config ${config.name}:`, error);
      throw error;
    }
  }

  async executeScheduledAnalysis(config) {
    const analysisId = uuidv4();
    const configId = config._id.toString();

    try {
      // Check if system is shutting down
      if (this.shutdownInProgress) {
        logger.info(`Skipping analysis ${analysisId} - system shutdown in progress`);
        return;
      }

      logger.info(`Starting scheduled analysis ${analysisId} for config ${config.name}`);

      // Check for duplicate/concurrent runs
      const duplicateCheck = await this.checkForDuplicateRuns(config, analysisId);
      if (!duplicateCheck.canProceed) {
        logger.warn(`Skipping analysis ${analysisId} - ${duplicateCheck.reason}`);
        return;
      }

      // Check if we should skip due to running analysis
      if (config.analysisSettings.autoScheduling.skipIfRunning) {
        const runningCount = Array.from(this.runningAnalyses.values())
          .filter(analysis => analysis.configId === configId).length;

        if (runningCount >= config.analysisSettings.autoScheduling.maxConcurrentRuns) {
          logger.warn(`Skipping analysis ${analysisId} - max concurrent runs (${config.analysisSettings.autoScheduling.maxConcurrentRuns}) reached`);
          return;
        }
      }

      // Create analysis result record
      const analysisResult = new AIAnalysisResult({
        analysisId,
        configId: config._id,
        type: 'periodic',
        status: 'running',
        analysisParameters: {
          timeRange: config.analysisSettings.timeRange,
          startTime: this.calculateStartTime(config.analysisSettings.timeRange),
          endTime: new Date(),
          logTypes: config.analysisSettings.enabledLogTypes,
          maxLogs: config.analysisSettings.maxLogsPerAnalysis
        }
      });

      await analysisResult.save();

      // Set retention expiry
      await analysisResult.setRetentionExpiry(config.dataRetention.analysisResultsRetentionDays);

      // Track running analysis
      this.runningAnalyses.set(analysisId, {
        configId,
        startTime: new Date(),
        analysisResult
      });

      // Execute the actual analysis
      await this.performAnalysis(analysisId, config, analysisResult);

    } catch (error) {
      logger.error(`Error in scheduled analysis ${analysisId}:`, error);

      // Mark analysis as failed
      if (this.runningAnalyses.has(analysisId)) {
        const { analysisResult } = this.runningAnalyses.get(analysisId);
        await analysisResult.markFailed(error.message);
        this.runningAnalyses.delete(analysisId);
      }
    }
  }

  async performAnalysis(analysisId, config, analysisResult) {
    try {
      // This would integrate with the AI service
      // For now, we'll simulate the analysis call
      const aiServiceUrl = process.env.AI_SERVICE_URL || 'http://localhost:3002';

      // Make request to AI service for analysis
      const analysisParams = {
        timeRange: config.analysisSettings.timeRange,
        logTypes: config.analysisSettings.enabledLogTypes,
        maxLogs: config.analysisSettings.maxLogsPerAnalysis,
        configId: config._id.toString()
      };

      logger.info(`Requesting analysis from AI service for ${analysisId}`);

      // Broadcast analysis started
      this.broadcastAnalysisUpdate(analysisResult, 'started');

      // Simulate AI service call (replace with actual HTTP request)
      const results = await this.callAIService(analysisParams);

      // Update analysis result with findings
      await analysisResult.markCompleted(results);

      logger.info(`Completed scheduled analysis ${analysisId} successfully`);

      // Broadcast analysis completed with results
      this.broadcastAnalysisUpdate(analysisResult, 'completed', results);

      // Generate alerts if needed
      await this.processAnalysisResults(analysisResult, config);

    } catch (error) {
      logger.error(`Error performing analysis ${analysisId}:`, error);

      // Broadcast analysis failed
      this.broadcastAnalysisUpdate(analysisResult, 'failed', null, error.message);

      throw error;
    } finally {
      // Remove from running analyses
      this.runningAnalyses.delete(analysisId);
    }
  }

  async callAIService(params) {
    // This is a placeholder for the actual AI service integration
    // In a real implementation, this would make an HTTP request to the AI service

    // Simulate analysis results
    return {
      summary: {
        totalLogs: Math.floor(Math.random() * 10000),
        anomalies: Math.floor(Math.random() * 50),
        threats: Math.floor(Math.random() * 10),
        patterns: Math.floor(Math.random() * 20),
        riskLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)],
        confidence: Math.floor(Math.random() * 100)
      },
      anomalies: [],
      threats: [],
      patterns: [],
      performance: {
        memoryUsage: { peak: 512, average: 256 },
        cpuUsage: { peak: 80, average: 45 },
        processingRate: 1000,
        cacheHitRate: 0.85
      }
    };
  }

  async processAnalysisResults(analysisResult, config) {
    try {
      // Generate alerts based on analysis results and configuration
      if (config.alerting.enabled) {
        const riskLevels = ['low', 'medium', 'high', 'critical'];
        const thresholdIndex = riskLevels.indexOf(config.alerting.riskLevelThreshold);
        const resultRiskIndex = riskLevels.indexOf(analysisResult.summary.riskLevel);

        if (resultRiskIndex >= thresholdIndex) {
          logger.info(`Generating alert for analysis ${analysisResult.analysisId} - risk level: ${analysisResult.summary.riskLevel}`);
          // Here you would integrate with your alerting system

          // Broadcast high-risk analysis alert
          this.broadcastHighRiskAlert(analysisResult, config);
        }
      }
    } catch (error) {
      logger.error(`Error processing analysis results for ${analysisResult.analysisId}:`, error);
    }
  }

  broadcastAnalysisUpdate(analysisResult, status, results = null, errorMessage = null) {
    try {
      // Get WebSocket server instance
      const wsServer = global.wsServer;
      if (!wsServer) {
        logger.debug('WebSocket server not available for broadcasting');
        return;
      }

      const updateData = {
        analysisId: analysisResult.analysisId,
        configId: analysisResult.configId,
        type: analysisResult.type,
        status: status,
        timestamp: new Date().toISOString()
      };

      if (status === 'completed' && results) {
        updateData.summary = results.summary;
        updateData.performance = results.performance;
        updateData.duration = analysisResult.execution.duration;
      }

      if (status === 'failed' && errorMessage) {
        updateData.error = errorMessage;
      }

      // Broadcast to all users subscribed to AI analysis updates
      wsServer.broadcast('ai:analysis-update', updateData);

      // Also broadcast specific events for different statuses
      switch (status) {
        case 'completed':
          wsServer.broadcastAIAnalysisComplete({
            analysisId: analysisResult.analysisId,
            summary: results?.summary,
            type: analysisResult.type,
            automated: true
          });

          // Update insights if there are significant findings
          if (results?.summary?.anomalies > 0 || results?.summary?.threats > 0) {
            wsServer.broadcastAIInsightsUpdated({
              source: 'automated-analysis',
              analysisId: analysisResult.analysisId,
              summary: results.summary,
              timestamp: new Date().toISOString()
            });
          }
          break;

        case 'started':
          wsServer.broadcast('ai:analysis-started', {
            analysisId: analysisResult.analysisId,
            configId: analysisResult.configId,
            type: analysisResult.type,
            automated: true
          });
          break;

        case 'failed':
          wsServer.broadcast('ai:analysis-failed', {
            analysisId: analysisResult.analysisId,
            configId: analysisResult.configId,
            error: errorMessage,
            automated: true
          });
          break;
      }

      logger.debug(`Broadcasted analysis ${status} for ${analysisResult.analysisId}`);
    } catch (error) {
      logger.error(`Error broadcasting analysis update:`, error);
    }
  }

  broadcastHighRiskAlert(analysisResult, config) {
    try {
      const wsServer = global.wsServer;
      if (!wsServer) return;

      wsServer.broadcast('ai:high-risk-alert', {
        analysisId: analysisResult.analysisId,
        configName: config.name,
        riskLevel: analysisResult.summary.riskLevel,
        summary: {
          totalLogs: analysisResult.summary.totalLogs,
          anomalies: analysisResult.summary.anomalies,
          threats: analysisResult.summary.threats,
          patterns: analysisResult.summary.patterns
        },
        automated: true,
        timestamp: new Date().toISOString()
      });

      logger.info(`Broadcasted high-risk alert for analysis ${analysisResult.analysisId}`);
    } catch (error) {
      logger.error(`Error broadcasting high-risk alert:`, error);
    }
  }

  calculateStartTime(timeRange) {
    const now = new Date();
    const timeRangeMap = {
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '12h': 12 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000
    };

    const milliseconds = timeRangeMap[timeRange] || timeRangeMap['1h'];
    return new Date(now.getTime() - milliseconds);
  }

  scheduleCleanupJob() {
    // Run cleanup daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
      await this.performCleanup();
    }, {
      timezone: 'UTC'
    });

    logger.info('Scheduled daily cleanup job at 2 AM UTC');
  }

  async performCleanup() {
    try {
      logger.info('Starting scheduled cleanup of expired analysis results...');

      const result = await AIAnalysisResult.deleteMany({
        'retention.expiresAt': { $lt: new Date() }
      });

      logger.info(`Cleanup completed: removed ${result.deletedCount} expired analysis results`);
    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }

  async updateSchedule(configId) {
    try {
      const config = await AIConfig.findById(configId);
      if (!config) {
        throw new Error(`Configuration ${configId} not found`);
      }

      await this.scheduleAnalysis(config);
      logger.info(`Updated schedule for configuration ${config.name}`);
    } catch (error) {
      logger.error(`Error updating schedule for config ${configId}:`, error);
      throw error;
    }
  }

  async removeSchedule(configId) {
    try {
      if (this.scheduledJobs.has(configId)) {
        this.scheduledJobs.get(configId).stop();
        this.scheduledJobs.delete(configId);
        logger.info(`Removed schedule for configuration ${configId}`);
      }
    } catch (error) {
      logger.error(`Error removing schedule for config ${configId}:`, error);
      throw error;
    }
  }

  getScheduleStatus() {
    const schedules = [];
    for (const [configId, job] of this.scheduledJobs.entries()) {
      schedules.push({
        configId,
        isRunning: job.running,
        nextRun: job.nextDate ? job.nextDate().toISOString() : null
      });
    }

    const runningAnalyses = Array.from(this.runningAnalyses.entries()).map(([analysisId, info]) => ({
      analysisId,
      configId: info.configId,
      startTime: info.startTime.toISOString(),
      duration: Date.now() - info.startTime.getTime()
    }));

    return {
      initialized: this.initialized,
      scheduledJobs: schedules.length,
      runningAnalyses: runningAnalyses.length,
      schedules,
      runningAnalyses
    };
  }

  async checkForDuplicateRuns(config, analysisId) {
    try {
      const configId = config._id.toString();

      // Check for recent running analyses in the database
      const recentRunning = await AIAnalysisResult.findOne({
        configId: configId,
        status: 'running',
        'execution.startedAt': {
          $gte: new Date(Date.now() - 5 * 60 * 1000) // Started within last 5 minutes
        }
      });

      if (recentRunning) {
        return {
          canProceed: false,
          reason: `Analysis ${recentRunning.analysisId} is already running for this configuration`
        };
      }

      // Check for very recent completed analyses (prevent rapid-fire scheduling)
      const recentCompleted = await AIAnalysisResult.findOne({
        configId: configId,
        status: 'completed',
        'execution.completedAt': {
          $gte: new Date(Date.now() - 60 * 1000) // Completed within last minute
        }
      });

      if (recentCompleted) {
        return {
          canProceed: false,
          reason: `Analysis ${recentCompleted.analysisId} completed too recently`
        };
      }

      return { canProceed: true };
    } catch (error) {
      logger.error('Error checking for duplicate runs:', error);
      // Allow the analysis to proceed if we can't check for duplicates
      return { canProceed: true };
    }
  }

  async shutdown() {
    logger.info('Shutting down AI Scheduler Service...');
    this.shutdownInProgress = true;

    // Wait for running analyses to complete (with timeout)
    await this.waitForRunningAnalyses(30000); // 30 second timeout

    // Stop all scheduled jobs
    for (const [configId, job] of this.scheduledJobs.entries()) {
      job.stop();
      logger.info(`Stopped scheduled job for config ${configId}`);
    }

    this.scheduledJobs.clear();
    this.initialized = false;

    logger.info('AI Scheduler Service shutdown complete');
  }

  async waitForRunningAnalyses(timeoutMs = 30000) {
    const startTime = Date.now();

    while (this.runningAnalyses.size > 0 && (Date.now() - startTime) < timeoutMs) {
      logger.info(`Waiting for ${this.runningAnalyses.size} running analyses to complete...`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    }

    if (this.runningAnalyses.size > 0) {
      logger.warn(`Shutdown timeout reached with ${this.runningAnalyses.size} analyses still running`);

      // Mark remaining analyses as cancelled
      for (const [analysisId, analysisInfo] of this.runningAnalyses.entries()) {
        try {
          await analysisInfo.analysisResult.markFailed('System shutdown - analysis cancelled');
          logger.info(`Marked analysis ${analysisId} as cancelled due to shutdown`);
        } catch (error) {
          logger.error(`Error marking analysis ${analysisId} as cancelled:`, error);
        }
      }

      this.runningAnalyses.clear();
    } else {
      logger.info('All running analyses completed gracefully');
    }
  }

  // Health check method
  async getHealthStatus() {
    return {
      initialized: this.initialized,
      shutdownInProgress: this.shutdownInProgress,
      recoveryInProgress: this.recoveryInProgress,
      scheduledJobs: this.scheduledJobs.size,
      runningAnalyses: this.runningAnalyses.size,
      uptime: this.initialized ? Date.now() - this.initTime : 0
    };
  }

  // Method to handle configuration changes that require scheduler restart
  async handleConfigurationChange(configId) {
    try {
      logger.info(`Handling configuration change for ${configId}`);

      // Stop existing job for this config
      await this.removeSchedule(configId);

      // Reload and reschedule
      await this.updateSchedule(configId);

      logger.info(`Configuration change handled successfully for ${configId}`);
    } catch (error) {
      logger.error(`Error handling configuration change for ${configId}:`, error);
      throw error;
    }
  }
}

module.exports = new AISchedulerService();

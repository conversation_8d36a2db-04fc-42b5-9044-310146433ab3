const SystemSettings = require('../models/SystemSettings');
const config = require('../config');
const logger = require('../utils/logger');

class ConfigService {
  constructor() {
    this.cachedSettings = null;
    this.cacheExpiry = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get current system settings with caching
   */
  async getSettings() {
    const now = Date.now();

    // Return cached settings if still valid
    if (this.cachedSettings && this.cacheExpiry && now < this.cacheExpiry) {
      return this.cachedSettings;
    }

    try {
      // Fetch fresh settings from database
      const settings = await SystemSettings.getCurrentSettings();

      // Cache the settings
      this.cachedSettings = settings;
      this.cacheExpiry = now + this.cacheTimeout;

      return settings;
    } catch (error) {
      logger.error('Failed to fetch system settings:', error);

      // Return cached settings if available, otherwise fallback
      if (this.cachedSettings) {
        logger.warn('Using cached settings due to database error');
        return this.cachedSettings;
      }

      // Return default settings as fallback
      return this.getDefaultSettings();
    }
  }

  /**
   * Invalidate cache to force refresh
   */
  invalidateCache() {
    this.cachedSettings = null;
    this.cacheExpiry = null;
    logger.debug('Configuration cache invalidated');
  }

  /**
   * Get log retention configuration
   */
  async getLogRetentionConfig() {
    const settings = await this.getSettings();

    return {
      defaultRetentionDays: settings.logRetention.defaultRetentionDays,
      retentionPolicies: settings.logRetention.retentionPolicies,
      autoArchiveEnabled: settings.logRetention.autoArchiveEnabled,
      archiveCompressionEnabled: settings.logRetention.archiveCompressionEnabled,
      archiveLocation: settings.logRetention.archiveLocation,
      cleanupJob: settings.logRetention.cleanupJob,
      archiveRetentionDays: settings.logRetention.archiveRetentionDays,
      compressionSettings: settings.logRetention.compressionSettings,
    };
  }

  /**
   * Get log storage configuration
   */
  async getLogStorageConfig() {
    const settings = await this.getSettings();

    return {
      localPaths: settings.logStorage?.localPaths || {
        logsPath: config.logStorage.localPath,
        storagePath: config.logStorage.localPath,
        archivePath: config.logStorage.archivePath,
        exportsPath: './logs/exports',
      },
      externalStorage: settings.logStorage?.externalStorage || {
        type: 'local',
        s3: {},
        azure: {},
        gcp: {},
      },
    };
  }

  /**
   * Get effective storage configuration for services
   */
  async getEffectiveStorageConfig() {
    const storageConfig = await this.getLogStorageConfig();
    const retentionConfig = await this.getLogRetentionConfig();

    return {
      // Storage paths
      localPath: storageConfig.localPaths.storagePath,
      archivePath: storageConfig.localPaths.archivePath,
      exportsPath: storageConfig.localPaths.exportsPath,

      // External storage
      externalStorage: {
        type: storageConfig.externalStorage.type,
        s3: storageConfig.externalStorage.s3,
        azure: storageConfig.externalStorage.azure,
        gcp: storageConfig.externalStorage.gcp,
      },

      // Compression
      compression: {
        enabled: retentionConfig.archiveCompressionEnabled,
        algorithm: retentionConfig.compressionSettings?.algorithm || 'gzip',
        level: retentionConfig.compressionSettings?.level || 6,
      },

      // Retention
      retention: {
        enableAutoDelete: retentionConfig.cleanupJob?.enabled || false,
        archiveBeforeDelete: retentionConfig.autoArchiveEnabled,
        archiveRetentionDays: retentionConfig.archiveRetentionDays,
        cleanupJobInterval: (retentionConfig.cleanupJob?.intervalHours || 24) * 60 * 60 * 1000,
        cleanupBatchSize: retentionConfig.cleanupJob?.batchSize || 1000,
      },
    };
  }

  /**
   * Update configuration and invalidate cache
   */
  async updateSettings(updates) {
    try {
      const settings = await SystemSettings.getCurrentSettings();

      // Apply updates
      Object.keys(updates).forEach(key => {
        if (updates[key] !== undefined) {
          settings[key] = updates[key];
        }
      });

      await settings.save();

      // Invalidate cache
      this.invalidateCache();

      // Trigger service reloads for configuration changes
      await this.triggerServiceReloads(updates);

      logger.info('System settings updated', { updates: Object.keys(updates) });

      return settings;
    } catch (error) {
      logger.error('Failed to update system settings:', error);
      throw error;
    }
  }

  /**
   * Trigger service reloads when configuration changes
   */
  async triggerServiceReloads(updates) {
    try {
      // Check if log retention or storage settings changed
      const needsRetentionReload = updates.logRetention || updates.logStorage;

      if (needsRetentionReload) {
        // Dynamically import services to avoid circular dependencies
        const logRetentionService = require('./logRetentionService');
        const storageService = require('./storageService');
        const logExportService = require('./logExportService');

        // Reload configurations
        await Promise.all([
          logRetentionService.reloadConfiguration().catch(error => {
            logger.error('Failed to reload log retention service:', error);
          }),
          storageService.reloadConfiguration().catch(error => {
            logger.error('Failed to reload storage service:', error);
          }),
          logExportService.reloadConfiguration().catch(error => {
            logger.error('Failed to reload log export service:', error);
          }),
        ]);

        logger.info('Services reloaded after configuration update');
      }
    } catch (error) {
      logger.error('Failed to trigger service reloads:', error);
      // Don't throw here as the configuration update was successful
    }
  }

  /**
   * Test storage connection
   */
  async testStorageConnection(storageConfig) {
    try {
      const { type } = storageConfig;

      switch (type) {
        case 'local':
          return await this.testLocalStorage(storageConfig);
        case 's3':
          return await this.testS3Connection(storageConfig.s3);
        case 'azure':
          return await this.testAzureConnection(storageConfig.azure);
        case 'gcp':
          return await this.testGCPConnection(storageConfig.gcp);
        default:
          throw new Error(`Unsupported storage type: ${type}`);
      }
    } catch (error) {
      logger.error('Storage connection test failed:', error);
      throw error;
    }
  }

  /**
   * Test local storage
   */
  async testLocalStorage(storageConfig) {
    const fs = require('fs').promises;
    const path = require('path');

    const testPaths = [
      storageConfig.logsPath,
      storageConfig.storagePath,
      storageConfig.archivePath,
      storageConfig.exportsPath,
    ];

    for (const testPath of testPaths) {
      try {
        await fs.access(testPath);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // Try to create the directory
          await fs.mkdir(testPath, { recursive: true });
        } else {
          throw new Error(`Cannot access path ${testPath}: ${error.message}`);
        }
      }
    }

    return { success: true, message: 'Local storage paths are accessible' };
  }

  /**
   * Test S3 connection
   */
  async testS3Connection(s3Config) {
    try {
      const AWS = require('aws-sdk');

      const s3 = new AWS.S3({
        accessKeyId: s3Config.accessKeyId,
        secretAccessKey: s3Config.secretAccessKey,
        region: s3Config.region,
        endpoint: s3Config.endpoint,
      });

      // Test by listing objects (limited to 1)
      await s3.listObjectsV2({
        Bucket: s3Config.bucket,
        MaxKeys: 1,
      }).promise();

      return { success: true, message: 'S3 connection successful' };
    } catch (error) {
      throw new Error(`S3 connection failed: ${error.message}`);
    }
  }

  /**
   * Test Azure connection
   */
  async testAzureConnection(azureConfig) {
    try {
      const { BlobServiceClient } = require('@azure/storage-blob');

      const blobServiceClient = BlobServiceClient.fromConnectionString(azureConfig.connectionString);
      const containerClient = blobServiceClient.getContainerClient(azureConfig.containerName);

      // Test by checking if container exists
      await containerClient.getProperties();

      return { success: true, message: 'Azure Blob Storage connection successful' };
    } catch (error) {
      throw new Error(`Azure connection failed: ${error.message}`);
    }
  }

  /**
   * Test GCP connection
   */
  async testGCPConnection(gcpConfig) {
    try {
      const { Storage } = require('@google-cloud/storage');

      const storage = new Storage({
        projectId: gcpConfig.projectId,
        keyFilename: gcpConfig.keyFilename,
      });

      const bucket = storage.bucket(gcpConfig.bucketName);

      // Test by checking if bucket exists
      const [exists] = await bucket.exists();

      if (!exists) {
        throw new Error('Bucket does not exist');
      }

      return { success: true, message: 'Google Cloud Storage connection successful' };
    } catch (error) {
      throw new Error(`GCP connection failed: ${error.message}`);
    }
  }

  /**
   * Get default settings fallback
   */
  getDefaultSettings() {
    return {
      logRetention: {
        defaultRetentionDays: 90,
        retentionPolicies: [],
        autoArchiveEnabled: true,
        archiveCompressionEnabled: true,
        archiveLocation: 'local',
        cleanupJob: {
          enabled: true,
          intervalHours: 24,
          batchSize: 1000,
        },
        archiveRetentionDays: 2555,
        compressionSettings: {
          algorithm: 'gzip',
          level: 6,
        },
      },
      logStorage: {
        localPaths: {
          logsPath: './logs',
          storagePath: './logs/storage',
          archivePath: './logs/archive',
          exportsPath: './logs/exports',
        },
        externalStorage: {
          type: 'local',
          s3: {},
          azure: {},
          gcp: {},
        },
      },
    };
  }
}

module.exports = new ConfigService();

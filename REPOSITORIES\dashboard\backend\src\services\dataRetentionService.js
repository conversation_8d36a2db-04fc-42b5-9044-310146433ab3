const cron = require('node-cron');
const logger = require('../utils/logger');
const AIAnalysisResult = require('../models/AIAnalysisResult');
const AIConfig = require('../models/AIConfig');
const AIFeedback = require('../models/AIFeedback');

class DataRetentionService {
  constructor() {
    this.initialized = false;
    this.cleanupJobs = new Map(); // configId -> cron job
    this.compressionJobs = new Map(); // configId -> cron job
  }

  async initialize() {
    try {
      logger.info('Initializing Data Retention Service...');
      
      // Load all active configurations and schedule retention jobs
      await this.loadRetentionPolicies();
      
      // Schedule global cleanup job (fallback)
      this.scheduleGlobalCleanup();
      
      this.initialized = true;
      logger.info('Data Retention Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Data Retention Service:', error);
      throw error;
    }
  }

  async loadRetentionPolicies() {
    try {
      const configs = await AIConfig.find({ isActive: true });
      
      logger.info(`Loading retention policies for ${configs.length} configurations`);
      
      for (const config of configs) {
        await this.scheduleRetentionJobs(config);
      }
    } catch (error) {
      logger.error('Error loading retention policies:', error);
      throw error;
    }
  }

  async scheduleRetentionJobs(config) {
    try {
      const configId = config._id.toString();
      const retentionConfig = config.dataRetention;

      // Stop existing jobs if any
      this.stopJobsForConfig(configId);

      if (!retentionConfig.autoCleanup.enabled) {
        logger.info(`Auto cleanup disabled for config ${config.name}`);
        return;
      }

      // Schedule cleanup job
      const cleanupJob = cron.schedule(retentionConfig.autoCleanup.schedule, async () => {
        await this.performCleanup(config);
      }, {
        scheduled: true,
        timezone: 'UTC'
      });

      this.cleanupJobs.set(configId, cleanupJob);

      // Schedule compression job if enabled
      if (retentionConfig.compressionSettings.enabled) {
        const compressionJob = cron.schedule('0 3 * * *', async () => { // Daily at 3 AM
          await this.performCompression(config);
        }, {
          scheduled: true,
          timezone: 'UTC'
        });

        this.compressionJobs.set(configId, compressionJob);
      }

      logger.info(`Scheduled retention jobs for config ${config.name}`);
    } catch (error) {
      logger.error(`Error scheduling retention jobs for config ${config.name}:`, error);
      throw error;
    }
  }

  async performCleanup(config) {
    const configId = config._id.toString();
    
    try {
      logger.info(`Starting cleanup for config ${config.name}`);
      
      const retentionConfig = config.dataRetention;
      const batchSize = retentionConfig.autoCleanup.batchSize || 1000;
      
      // Calculate expiry dates
      const now = new Date();
      const analysisResultsExpiry = new Date(now.getTime() - (retentionConfig.analysisResultsRetentionDays * 24 * 60 * 60 * 1000));
      const feedbackExpiry = new Date(now.getTime() - (retentionConfig.feedbackRetentionDays * 24 * 60 * 60 * 1000));
      const insightsExpiry = new Date(now.getTime() - (retentionConfig.insightsRetentionDays * 24 * 60 * 60 * 1000));

      let totalDeleted = 0;

      // Clean up analysis results
      const analysisResultsDeleted = await this.cleanupAnalysisResults(configId, analysisResultsExpiry, batchSize);
      totalDeleted += analysisResultsDeleted;

      // Clean up feedback
      const feedbackDeleted = await this.cleanupFeedback(configId, feedbackExpiry, batchSize);
      totalDeleted += feedbackDeleted;

      // Clean up old insights (if you have a separate insights collection)
      // const insightsDeleted = await this.cleanupInsights(configId, insightsExpiry, batchSize);
      // totalDeleted += insightsDeleted;

      logger.info(`Cleanup completed for config ${config.name}: ${totalDeleted} records deleted`);
      
      // Update cleanup metrics
      await this.updateCleanupMetrics(configId, totalDeleted);
      
    } catch (error) {
      logger.error(`Error during cleanup for config ${configId}:`, error);
    }
  }

  async cleanupAnalysisResults(configId, expiryDate, batchSize) {
    try {
      let totalDeleted = 0;
      let hasMore = true;

      while (hasMore) {
        const result = await AIAnalysisResult.deleteMany({
          configId: configId,
          'execution.startedAt': { $lt: expiryDate }
        }).limit(batchSize);

        totalDeleted += result.deletedCount;
        hasMore = result.deletedCount === batchSize;

        if (hasMore) {
          // Small delay to prevent overwhelming the database
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.info(`Deleted ${totalDeleted} expired analysis results for config ${configId}`);
      return totalDeleted;
    } catch (error) {
      logger.error(`Error cleaning up analysis results for config ${configId}:`, error);
      return 0;
    }
  }

  async cleanupFeedback(configId, expiryDate, batchSize) {
    try {
      let totalDeleted = 0;
      let hasMore = true;

      while (hasMore) {
        const result = await AIFeedback.deleteMany({
          configId: configId,
          createdAt: { $lt: expiryDate }
        }).limit(batchSize);

        totalDeleted += result.deletedCount;
        hasMore = result.deletedCount === batchSize;

        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.info(`Deleted ${totalDeleted} expired feedback records for config ${configId}`);
      return totalDeleted;
    } catch (error) {
      logger.error(`Error cleaning up feedback for config ${configId}:`, error);
      return 0;
    }
  }

  async performCompression(config) {
    const configId = config._id.toString();
    
    try {
      logger.info(`Starting compression for config ${config.name}`);
      
      const compressionConfig = config.dataRetention.compressionSettings;
      const compressAfterDate = new Date(Date.now() - (compressionConfig.compressAfterDays * 24 * 60 * 60 * 1000));
      
      // Find uncompressed analysis results older than the threshold
      const resultsToCompress = await AIAnalysisResult.find({
        configId: configId,
        'execution.startedAt': { $lt: compressAfterDate },
        'retention.isCompressed': { $ne: true }
      }).limit(100); // Process in batches

      let compressedCount = 0;

      for (const result of resultsToCompress) {
        try {
          await this.compressAnalysisResult(result);
          compressedCount++;
        } catch (error) {
          logger.error(`Error compressing analysis result ${result.analysisId}:`, error);
        }
      }

      logger.info(`Compression completed for config ${config.name}: ${compressedCount} records compressed`);
      
    } catch (error) {
      logger.error(`Error during compression for config ${configId}:`, error);
    }
  }

  async compressAnalysisResult(analysisResult) {
    try {
      // Calculate original size
      const originalData = JSON.stringify(analysisResult.toObject());
      const originalSize = Buffer.byteLength(originalData, 'utf8');

      // Compress detailed results (keep only summaries for old data)
      const compressedResult = {
        ...analysisResult.toObject(),
        anomalies: analysisResult.anomalies.slice(0, 5), // Keep only top 5
        threats: analysisResult.threats.slice(0, 3),     // Keep only top 3
        patterns: analysisResult.patterns.slice(0, 5)    // Keep only top 5
      };

      // Calculate compressed size
      const compressedData = JSON.stringify(compressedResult);
      const compressedSize = Buffer.byteLength(compressedData, 'utf8');
      const compressionRatio = compressedSize / originalSize;

      // Update the document
      analysisResult.anomalies = compressedResult.anomalies;
      analysisResult.threats = compressedResult.threats;
      analysisResult.patterns = compressedResult.patterns;
      analysisResult.retention.isCompressed = true;
      analysisResult.retention.originalSize = originalSize;
      analysisResult.retention.compressedSize = compressedSize;
      analysisResult.retention.compressionRatio = compressionRatio;

      await analysisResult.save();

      logger.debug(`Compressed analysis result ${analysisResult.analysisId}: ${originalSize} -> ${compressedSize} bytes (${Math.round(compressionRatio * 100)}%)`);
      
    } catch (error) {
      logger.error(`Error compressing analysis result ${analysisResult.analysisId}:`, error);
      throw error;
    }
  }

  async updateCleanupMetrics(configId, deletedCount) {
    try {
      // You could store cleanup metrics in a separate collection
      // For now, just log the information
      logger.info(`Cleanup metrics for config ${configId}: ${deletedCount} records deleted at ${new Date().toISOString()}`);
    } catch (error) {
      logger.error(`Error updating cleanup metrics for config ${configId}:`, error);
    }
  }

  scheduleGlobalCleanup() {
    // Global cleanup job as a fallback - runs daily at 4 AM
    cron.schedule('0 4 * * *', async () => {
      await this.performGlobalCleanup();
    }, {
      timezone: 'UTC'
    });
    
    logger.info('Scheduled global cleanup job at 4 AM UTC');
  }

  async performGlobalCleanup() {
    try {
      logger.info('Starting global cleanup...');
      
      // Clean up any orphaned records or records with expired TTL
      const expiredResults = await AIAnalysisResult.deleteMany({
        'retention.expiresAt': { $lt: new Date() }
      });

      logger.info(`Global cleanup completed: removed ${expiredResults.deletedCount} expired records`);
    } catch (error) {
      logger.error('Error during global cleanup:', error);
    }
  }

  stopJobsForConfig(configId) {
    if (this.cleanupJobs.has(configId)) {
      this.cleanupJobs.get(configId).stop();
      this.cleanupJobs.delete(configId);
    }

    if (this.compressionJobs.has(configId)) {
      this.compressionJobs.get(configId).stop();
      this.compressionJobs.delete(configId);
    }
  }

  async updateRetentionPolicy(configId) {
    try {
      const config = await AIConfig.findById(configId);
      if (!config) {
        throw new Error(`Configuration ${configId} not found`);
      }

      await this.scheduleRetentionJobs(config);
      logger.info(`Updated retention policy for configuration ${config.name}`);
    } catch (error) {
      logger.error(`Error updating retention policy for config ${configId}:`, error);
      throw error;
    }
  }

  getRetentionStatus() {
    return {
      initialized: this.initialized,
      activeCleanupJobs: this.cleanupJobs.size,
      activeCompressionJobs: this.compressionJobs.size,
      cleanupJobs: Array.from(this.cleanupJobs.keys()),
      compressionJobs: Array.from(this.compressionJobs.keys())
    };
  }

  async shutdown() {
    logger.info('Shutting down Data Retention Service...');
    
    // Stop all jobs
    for (const [configId, job] of this.cleanupJobs.entries()) {
      job.stop();
      logger.info(`Stopped cleanup job for config ${configId}`);
    }

    for (const [configId, job] of this.compressionJobs.entries()) {
      job.stop();
      logger.info(`Stopped compression job for config ${configId}`);
    }
    
    this.cleanupJobs.clear();
    this.compressionJobs.clear();
    this.initialized = false;
    
    logger.info('Data Retention Service shutdown complete');
  }
}

module.exports = new DataRetentionService();

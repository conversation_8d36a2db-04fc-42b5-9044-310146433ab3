const fs = require('fs').promises;
const path = require('path');
const { createWriteStream } = require('fs');
const { Transform } = require('stream');
const zlib = require('zlib');
const { promisify } = require('util');

const Log = require('../models/Log');
const config = require('../config');
const logger = require('../utils/logger');
const storageService = require('./storageService');
const configService = require('./configService');

const gzip = promisify(zlib.gzip);

class LogExportService {
  constructor() {
    this.exportPath = null;
    this.maxExportSize = 100 * 1024 * 1024; // 100MB
    this.supportedFormats = ['json', 'csv', 'txt'];
    this.configLoaded = false;
  }

  /**
   * Get current export path
   */
  async getExportPath() {
    if (!this.configLoaded || !this.exportPath) {
      const storageConfig = await configService.getEffectiveStorageConfig();
      this.exportPath = storageConfig.exportsPath;
      this.configLoaded = true;
    }
    return this.exportPath;
  }

  /**
   * Reload configuration
   */
  async reloadConfiguration() {
    this.configLoaded = false;
    const storageConfig = await configService.getEffectiveStorageConfig();
    this.exportPath = storageConfig.exportsPath;

    // Ensure new export directory exists
    await fs.mkdir(this.exportPath, { recursive: true });

    logger.info('Log export service configuration reloaded');
    return true;
  }

  /**
   * Initialize the export service
   */
  async initialize() {
    try {
      const exportPath = await this.getExportPath();
      await fs.mkdir(exportPath, { recursive: true });
      logger.info('Log export service initialized');
    } catch (error) {
      logger.error('Failed to initialize log export service:', error);
      throw error;
    }
  }

  /**
   * Export logs based on criteria
   */
  async exportLogs(criteria, format = 'json', options = {}) {
    try {
      const {
        startDate,
        endDate,
        sources,
        levels,
        hosts,
        limit = 10000,
        includeArchived = false,
        compress = true,
      } = criteria;

      // Build query
      const query = this.buildExportQuery({
        startDate,
        endDate,
        sources,
        levels,
        hosts,
        includeArchived,
      });

      // Generate export filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `logs-export-${timestamp}.${format}${compress ? '.gz' : ''}`;
      const filePath = path.join(this.exportPath, filename);

      // Export logs
      const exportStats = await this.performExport(query, filePath, format, {
        limit,
        compress,
        ...options,
      });

      logger.info('Log export completed', {
        filename,
        format,
        ...exportStats,
      });

      return {
        filename,
        filePath,
        format,
        compressed: compress,
        ...exportStats,
      };
    } catch (error) {
      logger.error('Log export failed:', error);
      throw error;
    }
  }

  /**
   * Build MongoDB query for export
   */
  buildExportQuery({ startDate, endDate, sources, levels, hosts, includeArchived }) {
    const query = {};

    // Date range
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    // Sources filter
    if (sources && sources.length > 0) {
      query.source = { $in: sources };
    }

    // Levels filter
    if (levels && levels.length > 0) {
      query.level = { $in: levels };
    }

    // Hosts filter
    if (hosts && hosts.length > 0) {
      query.host = { $in: hosts };
    }

    // Archived filter
    if (!includeArchived) {
      query.archived = { $ne: true };
    }

    return query;
  }

  /**
   * Perform the actual export
   */
  async performExport(query, filePath, format, options) {
    const { limit, compress } = options;

    let totalRecords = 0;
    let exportedRecords = 0;
    let fileSize = 0;

    // Get total count
    totalRecords = await Log.countDocuments(query);

    // Create write stream
    let writeStream = createWriteStream(filePath);

    if (compress) {
      const gzipStream = zlib.createGzip();
      gzipStream.pipe(writeStream);
      writeStream = gzipStream;
    }

    // Create format transformer
    const transformer = this.createFormatTransformer(format);
    transformer.pipe(writeStream);

    // Write header for CSV
    if (format === 'csv') {
      transformer.write(this.getCsvHeader() + '\n');
    }

    // Stream logs from database
    const cursor = Log.find(query)
      .limit(limit)
      .sort({ timestamp: 1 })
      .cursor();

    for (let log = await cursor.next(); log != null; log = await cursor.next()) {
      const formattedLog = this.formatLogForExport(log, format);
      transformer.write(formattedLog);
      exportedRecords++;

      // Check file size limit
      if (writeStream.bytesWritten > this.maxExportSize) {
        logger.warn('Export file size limit reached', {
          limit: this.maxExportSize,
          current: writeStream.bytesWritten,
        });
        break;
      }
    }

    // Close streams
    transformer.end();
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    // Get final file size
    const stats = await fs.stat(filePath);
    fileSize = stats.size;

    return {
      totalRecords,
      exportedRecords,
      fileSize,
      fileSizeFormatted: this.formatFileSize(fileSize),
    };
  }

  /**
   * Create format transformer stream
   */
  createFormatTransformer(format) {
    return new Transform({
      objectMode: true,
      transform(chunk, encoding, callback) {
        this.push(chunk);
        callback();
      },
    });
  }

  /**
   * Format log entry for export
   */
  formatLogForExport(log, format) {
    switch (format) {
      case 'json':
        return JSON.stringify(log.toObject()) + '\n';

      case 'csv':
        return this.formatLogAsCsv(log) + '\n';

      case 'txt':
        return this.formatLogAsText(log) + '\n';

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Get CSV header
   */
  getCsvHeader() {
    return [
      'timestamp',
      'logId',
      'source',
      'level',
      'message',
      'host',
      'severity',
      'tags',
      'archived',
    ].join(',');
  }

  /**
   * Format log as CSV
   */
  formatLogAsCsv(log) {
    const escapeCsv = (value) => {
      if (value === null || value === undefined) return '';
      const str = String(value);
      if (str.includes(',') || str.includes('"') || str.includes('\n')) {
        return `"${str.replace(/"/g, '""')}"`;
      }
      return str;
    };

    return [
      escapeCsv(log.timestamp?.toISOString()),
      escapeCsv(log.logId),
      escapeCsv(log.source),
      escapeCsv(log.level),
      escapeCsv(log.message),
      escapeCsv(log.host),
      escapeCsv(log.severity),
      escapeCsv(log.tags?.join(';')),
      escapeCsv(log.archived),
    ].join(',');
  }

  /**
   * Format log as plain text
   */
  formatLogAsText(log) {
    return `[${log.timestamp?.toISOString()}] ${log.level?.toUpperCase()} ${log.source} ${log.host}: ${log.message}`;
  }

  /**
   * Create backup of logs
   */
  async createBackup(options = {}) {
    try {
      const {
        includeArchived = true,
        compress = true,
        maxAge = 30, // days
      } = options;

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = path.join(this.exportPath, 'backups', timestamp);
      await fs.mkdir(backupDir, { recursive: true });

      const backupStats = {
        timestamp: new Date(),
        files: [],
        totalSize: 0,
      };

      // Backup active logs
      const activeLogsFile = path.join(backupDir, 'active-logs.json.gz');
      const activeStats = await this.exportLogs(
        { includeArchived: false },
        'json',
        { compress: true }
      );

      // Move to backup directory
      await fs.rename(activeStats.filePath, activeLogsFile);
      backupStats.files.push({
        name: 'active-logs.json.gz',
        size: activeStats.fileSize,
        records: activeStats.exportedRecords,
      });

      // Backup archived logs if requested
      if (includeArchived) {
        const archivedLogsFile = path.join(backupDir, 'archived-logs.json.gz');
        const archivedStats = await this.exportLogs(
          { includeArchived: true },
          'json',
          { compress: true }
        );

        await fs.rename(archivedStats.filePath, archivedLogsFile);
        backupStats.files.push({
          name: 'archived-logs.json.gz',
          size: archivedStats.fileSize,
          records: archivedStats.exportedRecords,
        });
      }

      // Copy storage files
      await this.copyStorageFiles(backupDir);

      // Calculate total size
      backupStats.totalSize = backupStats.files.reduce((sum, file) => sum + file.size, 0);

      // Clean up old backups
      await this.cleanupOldBackups(maxAge);

      logger.info('Backup created successfully', {
        backupDir,
        ...backupStats,
      });

      return {
        backupDir,
        ...backupStats,
      };
    } catch (error) {
      logger.error('Backup creation failed:', error);
      throw error;
    }
  }

  /**
   * Copy storage files to backup
   */
  async copyStorageFiles(backupDir) {
    try {
      const storageDir = config.logStorage.archivePath;
      const backupStorageDir = path.join(backupDir, 'storage');

      await fs.mkdir(backupStorageDir, { recursive: true });

      const files = await fs.readdir(storageDir);
      for (const file of files) {
        const srcPath = path.join(storageDir, file);
        const destPath = path.join(backupStorageDir, file);
        await fs.copyFile(srcPath, destPath);
      }
    } catch (error) {
      logger.warn('Failed to copy storage files:', error);
    }
  }

  /**
   * Clean up old backups
   */
  async cleanupOldBackups(maxAge) {
    try {
      const backupsDir = path.join(this.exportPath, 'backups');
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - maxAge);

      const backups = await fs.readdir(backupsDir);

      for (const backup of backups) {
        const backupPath = path.join(backupsDir, backup);
        const stats = await fs.stat(backupPath);

        if (stats.isDirectory() && stats.mtime < cutoffDate) {
          await fs.rmdir(backupPath, { recursive: true });
          logger.info(`Cleaned up old backup: ${backup}`);
        }
      }
    } catch (error) {
      logger.warn('Failed to cleanup old backups:', error);
    }
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * Get export statistics
   */
  async getExportStats() {
    try {
      const files = await fs.readdir(this.exportPath);
      const stats = {
        totalExports: 0,
        totalSize: 0,
        exports: [],
      };

      for (const file of files) {
        if (file === 'backups') continue;

        const filePath = path.join(this.exportPath, file);
        const fileStats = await fs.stat(filePath);

        stats.totalExports++;
        stats.totalSize += fileStats.size;
        stats.exports.push({
          filename: file,
          size: fileStats.size,
          sizeFormatted: this.formatFileSize(fileStats.size),
          created: fileStats.birthtime,
        });
      }

      return stats;
    } catch (error) {
      logger.error('Failed to get export stats:', error);
      return { totalExports: 0, totalSize: 0, exports: [] };
    }
  }
}

module.exports = new LogExportService();

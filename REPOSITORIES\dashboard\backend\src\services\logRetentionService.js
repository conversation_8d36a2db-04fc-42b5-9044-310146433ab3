const cron = require('node-cron');
const fs = require('fs').promises;
const path = require('path');
const zlib = require('zlib');
const { promisify } = require('util');

const Log = require('../models/Log');
const SystemSettings = require('../models/SystemSettings');
const config = require('../config');
const logger = require('../utils/logger');
const storageService = require('./storageService');
const configService = require('./configService');

const gzip = promisify(zlib.gzip);
const brotliCompress = promisify(zlib.brotliCompress);

class LogRetentionService {
  constructor() {
    this.isRunning = false;
    this.cronJob = null;
    this.stats = {
      lastRun: null,
      logsProcessed: 0,
      logsArchived: 0,
      logsDeleted: 0,
      errors: 0,
    };
  }

  /**
   * Initialize the log retention service
   */
  async initialize() {
    try {
      // Get current configuration
      const storageConfig = await configService.getEffectiveStorageConfig();

      // Ensure storage directories exist
      await this.ensureDirectories(storageConfig);

      // Start the cleanup job if enabled
      if (storageConfig.retention.enableAutoDelete) {
        await this.startCleanupJob();
        logger.info('Log retention service initialized and cleanup job started');
      } else {
        logger.info('Log retention service initialized (cleanup disabled)');
      }
    } catch (error) {
      logger.error('Failed to initialize log retention service:', error);
      throw error;
    }
  }

  /**
   * Ensure required directories exist
   */
  async ensureDirectories(storageConfig) {
    const directories = [
      storageConfig.localPath,
      storageConfig.archivePath,
      storageConfig.exportsPath,
      path.dirname(config.logging.file),
    ];

    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
        logger.debug(`Ensured directory exists: ${dir}`);
      } catch (error) {
        logger.error(`Failed to create directory ${dir}:`, error);
        throw error;
      }
    }
  }

  /**
   * Start the automated cleanup job
   */
  async startCleanupJob() {
    if (this.cronJob) {
      this.cronJob.stop();
    }

    // Get current configuration
    const storageConfig = await configService.getEffectiveStorageConfig();
    const intervalHours = storageConfig.retention.cleanupJobInterval / (60 * 60 * 1000);

    // Create cron expression based on interval
    let cronExpression;
    if (intervalHours >= 24) {
      // Daily at 2 AM
      cronExpression = '0 2 * * *';
    } else if (intervalHours >= 12) {
      // Twice daily
      cronExpression = '0 2,14 * * *';
    } else if (intervalHours >= 6) {
      // Every 6 hours
      cronExpression = '0 */6 * * *';
    } else {
      // Every hour
      cronExpression = '0 * * * *';
    }

    this.cronJob = cron.schedule(cronExpression, async () => {
      await this.runCleanup();
    }, {
      scheduled: true,
      timezone: 'UTC',
    });

    logger.info(`Log retention cleanup job scheduled with expression: ${cronExpression}`);
  }

  /**
   * Stop the cleanup job
   */
  stopCleanupJob() {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      logger.info('Log retention cleanup job stopped');
    }
  }

  /**
   * Run the cleanup process
   */
  async runCleanup() {
    if (this.isRunning) {
      logger.warn('Log retention cleanup already running, skipping this cycle');
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      logger.info('Starting log retention cleanup process');

      // Reset stats for this run
      this.stats = {
        lastRun: new Date(),
        logsProcessed: 0,
        logsArchived: 0,
        logsDeleted: 0,
        errors: 0,
      };

      // Get current system settings
      const settings = await SystemSettings.getCurrentSettings();

      // Process retention policies
      await this.processRetentionPolicies(settings);

      // Clean up old archives if enabled
      const storageConfig = await configService.getEffectiveStorageConfig();
      if (storageConfig.retention.archiveBeforeDelete) {
        await this.cleanupOldArchives();
      }

      const duration = Date.now() - startTime;
      logger.info(`Log retention cleanup completed in ${duration}ms`, this.stats);

    } catch (error) {
      this.stats.errors++;
      logger.error('Log retention cleanup failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Process retention policies
   */
  async processRetentionPolicies(settings) {
    const policies = settings.logRetention.retentionPolicies;
    const defaultRetentionDays = settings.logRetention.defaultRetentionDays;

    // Process each retention policy
    for (const policy of policies) {
      try {
        await this.processPolicy(policy);
      } catch (error) {
        this.stats.errors++;
        logger.error(`Failed to process retention policy ${policy.name}:`, error);
      }
    }

    // Process logs without specific policies using default retention
    await this.processDefaultRetention(defaultRetentionDays);
  }

  /**
   * Process a specific retention policy
   */
  async processPolicy(policy) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - policy.retentionDays);

    const query = {
      timestamp: { $lt: cutoffDate },
      archived: { $ne: true },
    };

    // Add policy-specific filters
    if (policy.logSources && policy.logSources.length > 0) {
      query.source = { $in: policy.logSources };
    }

    if (policy.logLevels && policy.logLevels.length > 0) {
      query.logLevel = { $in: policy.logLevels };
    }

    const storageConfig = await configService.getEffectiveStorageConfig();
    const logsToProcess = await Log.find(query)
      .limit(storageConfig.retention.cleanupBatchSize)
      .sort({ timestamp: 1 });

    logger.info(`Processing ${logsToProcess.length} logs for policy: ${policy.name}`);

    for (const log of logsToProcess) {
      try {
        if (storageConfig.retention.archiveBeforeDelete) {
          await this.archiveLog(log);
          this.stats.logsArchived++;
        } else {
          await this.deleteLog(log);
          this.stats.logsDeleted++;
        }
        this.stats.logsProcessed++;
      } catch (error) {
        this.stats.errors++;
        logger.error(`Failed to process log ${log._id}:`, error);
      }
    }
  }

  /**
   * Process default retention for logs without specific policies
   */
  async processDefaultRetention(retentionDays) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const query = {
      timestamp: { $lt: cutoffDate },
      archived: { $ne: true },
      retentionPolicy: { $in: [null, 'default'] },
    };

    const storageConfig = await configService.getEffectiveStorageConfig();
    const logsToProcess = await Log.find(query)
      .limit(storageConfig.retention.cleanupBatchSize)
      .sort({ timestamp: 1 });

    logger.info(`Processing ${logsToProcess.length} logs with default retention`);

    for (const log of logsToProcess) {
      try {
        if (storageConfig.retention.archiveBeforeDelete) {
          await this.archiveLog(log);
          this.stats.logsArchived++;
        } else {
          await this.deleteLog(log);
          this.stats.logsDeleted++;
        }
        this.stats.logsProcessed++;
      } catch (error) {
        this.stats.errors++;
        logger.error(`Failed to process log ${log._id}:`, error);
      }
    }
  }

  /**
   * Archive a log entry
   */
  async archiveLog(log) {
    try {
      const storageConfig = await configService.getEffectiveStorageConfig();

      // Create archive data
      const archiveData = {
        _id: log._id,
        logId: log.logId,
        timestamp: log.timestamp,
        source: log.source,
        logLevel: log.logLevel, // Use correct field name
        message: log.message,
        host: log.host,
        additionalFields: log.additionalFields,
        tags: log.tags,
        metadata: log.metadata,
        archivedAt: new Date(),
      };

      // Compress if enabled
      let data = JSON.stringify(archiveData);
      if (storageConfig.compression.enabled) {
        const buffer = Buffer.from(data);
        const compressed = storageConfig.compression.algorithm === 'brotli'
          ? await brotliCompress(buffer, { level: storageConfig.compression.level })
          : await gzip(buffer, { level: storageConfig.compression.level });
        data = compressed;
      }

      // Store archive
      await storageService.storeArchive(log._id.toString(), data);

      // Mark as archived in database
      await Log.findByIdAndUpdate(log._id, {
        archived: true,
        archivedAt: new Date(),
      });

      logger.debug(`Archived log ${log._id}`);
    } catch (error) {
      logger.error(`Failed to archive log ${log._id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a log entry
   */
  async deleteLog(log) {
    try {
      await Log.findByIdAndDelete(log._id);
      logger.debug(`Deleted log ${log._id}`);
    } catch (error) {
      logger.error(`Failed to delete log ${log._id}:`, error);
      throw error;
    }
  }

  /**
   * Clean up old archives
   */
  async cleanupOldArchives() {
    const storageConfig = await configService.getEffectiveStorageConfig();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - storageConfig.retention.archiveRetentionDays);

    try {
      await storageService.cleanupOldArchives(cutoffDate);
      logger.info('Old archives cleanup completed');
    } catch (error) {
      logger.error('Failed to cleanup old archives:', error);
      throw error;
    }
  }

  /**
   * Get retention service statistics
   */
  getStats() {
    return {
      ...this.stats,
      isRunning: this.isRunning,
      nextRun: this.cronJob ? this.cronJob.nextDate() : null,
    };
  }

  /**
   * Manually trigger cleanup (for testing or admin purposes)
   */
  async triggerCleanup() {
    if (this.isRunning) {
      throw new Error('Cleanup is already running');
    }

    logger.info('Manually triggering log retention cleanup');
    await this.runCleanup();
    return this.getStats();
  }

  /**
   * Reload configuration and restart cleanup job
   */
  async reloadConfiguration() {
    try {
      logger.info('Reloading log retention configuration');

      // Stop current job
      this.stopCleanupJob();

      // Get updated configuration
      const storageConfig = await configService.getEffectiveStorageConfig();

      // Ensure directories exist with new paths
      await this.ensureDirectories(storageConfig);

      // Restart cleanup job if enabled
      if (storageConfig.retention.enableAutoDelete) {
        await this.startCleanupJob();
        logger.info('Log retention service reloaded and cleanup job restarted');
      } else {
        logger.info('Log retention service reloaded (cleanup disabled)');
      }

      return true;
    } catch (error) {
      logger.error('Failed to reload log retention configuration:', error);
      throw error;
    }
  }
}

module.exports = new LogRetentionService();

const Notification = require('../models/Notification');
const User = require('../models/User');
const logger = require('../utils/logger');

class NotificationService {
  /**
   * Create a notification for a specific user
   */
  async createNotification(userId, notificationData) {
    try {
      // Check if user exists and get their notification preferences
      const user = await User.findById(userId).select('preferences.notifications');
      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      const preferences = user.preferences?.notifications || {};

      // Check if user has in-app notifications enabled
      if (preferences.inApp === false) {
        logger.debug(`Skipping notification for user ${userId} - in-app notifications disabled`);
        return null;
      }

      // Check category-specific preferences
      if (notificationData.category === 'alert' && preferences.alerts) {
        const severity = notificationData.severity || 'medium';
        if (preferences.alerts[severity] === false) {
          logger.debug(`Skipping ${severity} alert notification for user ${userId} - disabled in preferences`);
          return null;
        }
      }

      // Check other category preferences if they exist
      if (notificationData.category === 'system' && preferences.system === false) {
        logger.debug(`Skipping system notification for user ${userId} - disabled in preferences`);
        return null;
      }

      if (notificationData.category === 'agent' && preferences.agent === false) {
        logger.debug(`Skipping agent notification for user ${userId} - disabled in preferences`);
        return null;
      }

      const notification = await Notification.createNotification({
        userId,
        ...notificationData,
      });

      logger.info(`Notification created for user ${userId}`, {
        notificationId: notification._id,
        type: notification.type,
        category: notification.category,
      });

      // Trigger real-time notification if WebSocket is available
      this.broadcastNotification(userId, notification);

      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Create notifications for multiple users
   */
  async createBulkNotifications(userIds, notificationData) {
    try {
      const notifications = [];
      const batchId = new Date().getTime().toString();

      for (const userId of userIds) {
        try {
          const notification = await this.createNotification(userId, {
            ...notificationData,
            batchId,
          });
          if (notification) {
            notifications.push(notification);
          }
        } catch (error) {
          logger.error(`Failed to create notification for user ${userId}:`, error);
        }
      }

      logger.info(`Bulk notifications created`, {
        count: notifications.length,
        batchId,
      });

      return notifications;
    } catch (error) {
      logger.error('Error creating bulk notifications:', error);
      throw error;
    }
  }

  /**
   * Create alert-based notification
   */
  async createAlertNotification(alert, userIds = null) {
    try {
      // If no specific users provided, notify all users with appropriate permissions
      if (!userIds) {
        const users = await User.find({
          status: 'active',
          'permissions': { $in: ['view_alerts'] },
        }).select('_id');
        userIds = users.map(user => user._id);
      }

      const notificationData = {
        title: `${alert.severity.toUpperCase()} Alert: ${alert.name}`,
        message: alert.description,
        type: this.getSeverityType(alert.severity),
        category: 'alert',
        severity: alert.severity,
        sourceType: 'alert',
        sourceId: alert._id,
        actions: [{
          label: 'View Alert',
          url: `/alerts/${alert._id}`,
          style: 'primary',
        }],
        metadata: {
          alertId: alert._id,
          ruleId: alert.ruleId,
          triggeredAt: alert.triggeredAt,
        },
      };

      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      logger.error('Error creating alert notification:', error);
      throw error;
    }
  }

  /**
   * Create agent status notification
   */
  async createAgentNotification(agent, status, userIds = null) {
    try {
      if (!userIds) {
        const users = await User.find({
          status: 'active',
          'permissions': { $in: ['view_agents'] },
        }).select('_id');
        userIds = users.map(user => user._id);
      }

      const statusMessages = {
        offline: `Agent ${agent.hostname} has gone offline`,
        online: `Agent ${agent.hostname} is back online`,
        error: `Agent ${agent.hostname} is experiencing errors`,
        warning: `Agent ${agent.hostname} has warnings`,
      };

      const statusTypes = {
        offline: 'error',
        online: 'success',
        error: 'error',
        warning: 'warning',
      };

      const notificationData = {
        title: `Agent Status: ${agent.hostname}`,
        message: statusMessages[status] || `Agent ${agent.hostname} status changed to ${status}`,
        type: statusTypes[status] || 'info',
        category: 'agent',
        severity: status === 'offline' || status === 'error' ? 'high' : 'medium',
        sourceType: 'agent',
        sourceId: agent._id,
        actions: [{
          label: 'View Agent',
          url: `/agents/${agent._id}`,
          style: 'primary',
        }],
        metadata: {
          agentId: agent._id,
          hostname: agent.hostname,
          previousStatus: agent.status,
          newStatus: status,
        },
      };

      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      logger.error('Error creating agent notification:', error);
      throw error;
    }
  }

  /**
   * Create system notification
   */
  async createSystemNotification(title, message, type = 'info', userIds = null) {
    try {
      if (!userIds) {
        const users = await User.find({
          status: 'active',
          role: { $in: ['admin', 'system_admin'] },
        }).select('_id');
        userIds = users.map(user => user._id);
      }

      const notificationData = {
        title,
        message,
        type,
        category: 'system',
        severity: type === 'error' ? 'high' : 'medium',
      };

      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      logger.error('Error creating system notification:', error);
      throw error;
    }
  }

  /**
   * Create report notification
   */
  async createReportNotification(report, userIds) {
    try {
      const notificationData = {
        title: `Report Ready: ${report.name}`,
        message: `Your ${report.type} report has been generated and is ready for review.`,
        type: 'success',
        category: 'report',
        severity: 'low',
        sourceType: 'report',
        sourceId: report._id,
        actions: [{
          label: 'View Report',
          url: `/reports/${report._id}`,
          style: 'primary',
        }],
        metadata: {
          reportId: report._id,
          reportType: report.type,
          generatedAt: new Date(),
        },
      };

      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      logger.error('Error creating report notification:', error);
      throw error;
    }
  }

  /**
   * Create user-specific notification (login, password change, etc.)
   */
  async createUserNotification(userId, type, metadata = {}) {
    try {
      const notificationTypes = {
        login: {
          title: 'New Login Detected',
          message: `A new login was detected from ${metadata.location || 'unknown location'} using ${metadata.device || 'unknown device'}.`,
          type: 'info',
          category: 'security',
          severity: 'medium',
        },
        password_changed: {
          title: 'Password Changed',
          message: 'Your password has been successfully changed.',
          type: 'success',
          category: 'security',
          severity: 'medium',
        },
        account_locked: {
          title: 'Account Locked',
          message: 'Your account has been locked due to multiple failed login attempts.',
          type: 'warning',
          category: 'security',
          severity: 'high',
        },
      };

      const notificationData = notificationTypes[type];
      if (!notificationData) {
        throw new Error(`Unknown user notification type: ${type}`);
      }

      return await this.createNotification(userId, {
        ...notificationData,
        metadata,
      });
    } catch (error) {
      logger.error('Error creating user notification:', error);
      throw error;
    }
  }

  /**
   * Broadcast notification via WebSocket
   */
  broadcastNotification(userId, notification) {
    try {
      // Get WebSocket server instance
      const WebSocketServer = require('../websocket');
      const wsServer = global.wsServer;

      if (wsServer) {
        wsServer.sendToUser(userId.toString(), {
          type: 'notification',
          notification: {
            id: notification._id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            category: notification.category,
            severity: notification.severity,
            createdAt: notification.createdAt,
            actions: notification.actions,
          },
        });
      }
    } catch (error) {
      logger.error('Error broadcasting notification:', error);
    }
  }

  /**
   * Helper method to map alert severity to notification type
   */
  getSeverityType(severity) {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'info';
      default:
        return 'info';
    }
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpiredNotifications() {
    try {
      const result = await Notification.cleanupExpiredNotifications();
      if (result.deletedCount > 0) {
        logger.info(`Cleaned up ${result.deletedCount} expired notifications`);
      }
      return result;
    } catch (error) {
      logger.error('Error cleaning up expired notifications:', error);
      throw error;
    }
  }
}

module.exports = new NotificationService();

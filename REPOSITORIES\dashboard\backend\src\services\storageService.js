const fs = require('fs').promises;
const path = require('path');
const config = require('../config');
const logger = require('../utils/logger');
const configService = require('./configService');

class StorageService {
  constructor() {
    this.storageConfig = null;
    this.configLoaded = false;
  }

  /**
   * Get current storage configuration
   */
  async getStorageConfig() {
    if (!this.configLoaded || !this.storageConfig) {
      this.storageConfig = await configService.getEffectiveStorageConfig();
      this.configLoaded = true;
    }
    return this.storageConfig;
  }

  /**
   * Reload storage configuration
   */
  async reloadConfiguration() {
    this.configLoaded = false;
    this.storageConfig = await configService.getEffectiveStorageConfig();
    logger.info('Storage service configuration reloaded');
    return this.storageConfig;
  }

  /**
   * Store an archive file
   */
  async storeArchive(logId, data) {
    const storageConfig = await this.getStorageConfig();
    const fileName = this.generateArchiveFileName(logId);

    switch (storageConfig.externalStorage.type) {
      case 'local':
        return await this.storeLocal(fileName, data, storageConfig);
      case 's3':
        return await this.storeS3(fileName, data, storageConfig);
      case 'azure':
        return await this.storeAzure(fileName, data, storageConfig);
      case 'gcp':
        return await this.storeGCP(fileName, data, storageConfig);
      default:
        throw new Error(`Unsupported storage type: ${storageConfig.externalStorage.type}`);
    }
  }

  /**
   * Store file locally
   */
  async storeLocal(fileName, data, storageConfig) {
    const archivePath = storageConfig ? storageConfig.archivePath : config.logStorage.archivePath;
    const filePath = path.join(archivePath, fileName);

    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // Write file
      await fs.writeFile(filePath, data);

      logger.debug(`Stored archive locally: ${filePath}`);
      return { location: filePath, type: 'local' };
    } catch (error) {
      logger.error(`Failed to store archive locally: ${error.message}`);
      throw error;
    }
  }

  /**
   * Store file in Amazon S3
   */
  async storeS3(fileName, data, storageConfig) {
    try {
      // Lazy load AWS SDK to avoid dependency issues if not used
      const AWS = require('aws-sdk');

      const s3Config = storageConfig ? storageConfig.externalStorage.s3 : config.logStorage.externalStorage.s3;
      const s3 = new AWS.S3({
        accessKeyId: s3Config.accessKeyId,
        secretAccessKey: s3Config.secretAccessKey,
        region: s3Config.region,
        endpoint: s3Config.endpoint,
      });

      const key = `${s3Config.prefix}${fileName}`;

      const params = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: data,
        ContentType: 'application/json',
        ServerSideEncryption: 'AES256',
      };

      const result = await s3.upload(params).promise();

      logger.debug(`Stored archive in S3: ${result.Location}`);
      return { location: result.Location, type: 's3', key };
    } catch (error) {
      logger.error(`Failed to store archive in S3: ${error.message}`);
      throw error;
    }
  }

  /**
   * Store file in Azure Blob Storage
   */
  async storeAzure(fileName, data, storageConfig) {
    try {
      // Lazy load Azure SDK
      const { BlobServiceClient } = require('@azure/storage-blob');

      const azureConfig = storageConfig ? storageConfig.externalStorage.azure : config.logStorage.externalStorage.azure;
      const blobServiceClient = BlobServiceClient.fromConnectionString(azureConfig.connectionString);
      const containerClient = blobServiceClient.getContainerClient(azureConfig.containerName);

      const blobName = `${azureConfig.prefix}${fileName}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      await blockBlobClient.upload(data, data.length, {
        blobHTTPHeaders: { blobContentType: 'application/json' },
      });

      const url = blockBlobClient.url;

      logger.debug(`Stored archive in Azure: ${url}`);
      return { location: url, type: 'azure', blobName };
    } catch (error) {
      logger.error(`Failed to store archive in Azure: ${error.message}`);
      throw error;
    }
  }

  /**
   * Store file in Google Cloud Storage
   */
  async storeGCP(fileName, data, storageConfig) {
    try {
      // Lazy load GCP SDK
      const { Storage } = require('@google-cloud/storage');

      const gcpConfig = storageConfig ? storageConfig.externalStorage.gcp : config.logStorage.externalStorage.gcp;
      const storage = new Storage({
        projectId: gcpConfig.projectId,
        keyFilename: gcpConfig.keyFilename,
      });

      const bucket = storage.bucket(gcpConfig.bucketName);
      const fileName_with_prefix = `${gcpConfig.prefix}${fileName}`;
      const file = bucket.file(fileName_with_prefix);

      await file.save(data, {
        metadata: {
          contentType: 'application/json',
        },
      });

      const publicUrl = `gs://${gcpConfig.bucketName}/${fileName_with_prefix}`;

      logger.debug(`Stored archive in GCP: ${publicUrl}`);
      return { location: publicUrl, type: 'gcp', fileName: fileName_with_prefix };
    } catch (error) {
      logger.error(`Failed to store archive in GCP: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieve an archived log
   */
  async retrieveArchive(logId) {
    const fileName = this.generateArchiveFileName(logId);

    switch (this.storageType) {
      case 'local':
        return await this.retrieveLocal(fileName);
      case 's3':
        return await this.retrieveS3(fileName);
      case 'azure':
        return await this.retrieveAzure(fileName);
      case 'gcp':
        return await this.retrieveGCP(fileName);
      default:
        throw new Error(`Unsupported storage type: ${this.storageType}`);
    }
  }

  /**
   * Retrieve file from local storage
   */
  async retrieveLocal(fileName) {
    const filePath = path.join(this.archivePath, fileName);

    try {
      const data = await fs.readFile(filePath);
      return data;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Archive not found: ${fileName}`);
      }
      throw error;
    }
  }

  /**
   * Retrieve file from S3
   */
  async retrieveS3(fileName) {
    try {
      const AWS = require('aws-sdk');

      const s3Config = config.logStorage.externalStorage.s3;
      const s3 = new AWS.S3({
        accessKeyId: s3Config.accessKeyId,
        secretAccessKey: s3Config.secretAccessKey,
        region: s3Config.region,
      });

      const key = `${s3Config.prefix}${fileName}`;

      const params = {
        Bucket: s3Config.bucket,
        Key: key,
      };

      const result = await s3.getObject(params).promise();
      return result.Body;
    } catch (error) {
      if (error.code === 'NoSuchKey') {
        throw new Error(`Archive not found: ${fileName}`);
      }
      throw error;
    }
  }

  /**
   * Retrieve file from Azure
   */
  async retrieveAzure(fileName) {
    try {
      const { BlobServiceClient } = require('@azure/storage-blob');

      const azureConfig = config.logStorage.externalStorage.azure;
      const blobServiceClient = BlobServiceClient.fromConnectionString(azureConfig.connectionString);
      const containerClient = blobServiceClient.getContainerClient(azureConfig.containerName);

      const blobName = `${azureConfig.prefix}${fileName}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      const downloadResponse = await blockBlobClient.download();
      const chunks = [];

      for await (const chunk of downloadResponse.readableStreamBody) {
        chunks.push(chunk);
      }

      return Buffer.concat(chunks);
    } catch (error) {
      if (error.statusCode === 404) {
        throw new Error(`Archive not found: ${fileName}`);
      }
      throw error;
    }
  }

  /**
   * Retrieve file from GCP
   */
  async retrieveGCP(fileName) {
    try {
      const { Storage } = require('@google-cloud/storage');

      const gcpConfig = config.logStorage.externalStorage.gcp;
      const storage = new Storage({
        projectId: gcpConfig.projectId,
        keyFilename: gcpConfig.keyFilename,
      });

      const bucket = storage.bucket(gcpConfig.bucketName);
      const fileName_with_prefix = `${gcpConfig.prefix}${fileName}`;
      const file = bucket.file(fileName_with_prefix);

      const [contents] = await file.download();
      return contents;
    } catch (error) {
      if (error.code === 404) {
        throw new Error(`Archive not found: ${fileName}`);
      }
      throw error;
    }
  }

  /**
   * Clean up old archives
   */
  async cleanupOldArchives(cutoffDate) {
    switch (this.storageType) {
      case 'local':
        return await this.cleanupLocalArchives(cutoffDate);
      case 's3':
        return await this.cleanupS3Archives(cutoffDate);
      case 'azure':
        return await this.cleanupAzureArchives(cutoffDate);
      case 'gcp':
        return await this.cleanupGCPArchives(cutoffDate);
      default:
        logger.warn(`Cleanup not implemented for storage type: ${this.storageType}`);
    }
  }

  /**
   * Clean up local archives
   */
  async cleanupLocalArchives(cutoffDate) {
    try {
      const files = await fs.readdir(this.archivePath);
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.archivePath, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
          logger.debug(`Deleted old archive: ${filePath}`);
        }
      }

      logger.info(`Cleaned up ${deletedCount} old local archives`);
      return deletedCount;
    } catch (error) {
      logger.error(`Failed to cleanup local archives: ${error.message}`);
      throw error;
    }
  }

  /**
   * Clean up S3 archives
   */
  async cleanupS3Archives(cutoffDate) {
    try {
      const AWS = require('aws-sdk');

      const s3Config = config.logStorage.externalStorage.s3;
      const s3 = new AWS.S3({
        accessKeyId: s3Config.accessKeyId,
        secretAccessKey: s3Config.secretAccessKey,
        region: s3Config.region,
      });

      const listParams = {
        Bucket: s3Config.bucket,
        Prefix: s3Config.prefix,
      };

      const objects = await s3.listObjectsV2(listParams).promise();
      let deletedCount = 0;

      for (const object of objects.Contents || []) {
        if (object.LastModified < cutoffDate) {
          await s3.deleteObject({
            Bucket: s3Config.bucket,
            Key: object.Key,
          }).promise();

          deletedCount++;
          logger.debug(`Deleted old S3 archive: ${object.Key}`);
        }
      }

      logger.info(`Cleaned up ${deletedCount} old S3 archives`);
      return deletedCount;
    } catch (error) {
      logger.error(`Failed to cleanup S3 archives: ${error.message}`);
      throw error;
    }
  }

  /**
   * Clean up Azure archives
   */
  async cleanupAzureArchives(cutoffDate) {
    try {
      const { BlobServiceClient } = require('@azure/storage-blob');

      const azureConfig = config.logStorage.externalStorage.azure;
      const blobServiceClient = BlobServiceClient.fromConnectionString(azureConfig.connectionString);
      const containerClient = blobServiceClient.getContainerClient(azureConfig.containerName);

      let deletedCount = 0;

      for await (const blob of containerClient.listBlobsFlat({ prefix: azureConfig.prefix })) {
        if (blob.properties.lastModified < cutoffDate) {
          await containerClient.deleteBlob(blob.name);
          deletedCount++;
          logger.debug(`Deleted old Azure archive: ${blob.name}`);
        }
      }

      logger.info(`Cleaned up ${deletedCount} old Azure archives`);
      return deletedCount;
    } catch (error) {
      logger.error(`Failed to cleanup Azure archives: ${error.message}`);
      throw error;
    }
  }

  /**
   * Clean up GCP archives
   */
  async cleanupGCPArchives(cutoffDate) {
    try {
      const { Storage } = require('@google-cloud/storage');

      const gcpConfig = config.logStorage.externalStorage.gcp;
      const storage = new Storage({
        projectId: gcpConfig.projectId,
        keyFilename: gcpConfig.keyFilename,
      });

      const bucket = storage.bucket(gcpConfig.bucketName);
      const [files] = await bucket.getFiles({ prefix: gcpConfig.prefix });

      let deletedCount = 0;

      for (const file of files) {
        const [metadata] = await file.getMetadata();
        const lastModified = new Date(metadata.timeCreated);

        if (lastModified < cutoffDate) {
          await file.delete();
          deletedCount++;
          logger.debug(`Deleted old GCP archive: ${file.name}`);
        }
      }

      logger.info(`Cleaned up ${deletedCount} old GCP archives`);
      return deletedCount;
    } catch (error) {
      logger.error(`Failed to cleanup GCP archives: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate archive file name
   */
  generateArchiveFileName(logId) {
    const date = new Date().toISOString().split('T')[0];
    const extension = config.logStorage.compression.enabled ? '.gz' : '.json';
    return `${date}_${logId}${extension}`;
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    const stats = {
      type: this.storageType,
      totalArchives: 0,
      totalSize: 0,
      oldestArchive: null,
      newestArchive: null,
    };

    try {
      if (this.storageType === 'local') {
        const files = await fs.readdir(this.archivePath);
        stats.totalArchives = files.length;

        let totalSize = 0;
        let oldestDate = null;
        let newestDate = null;

        for (const file of files) {
          const filePath = path.join(this.archivePath, file);
          const fileStats = await fs.stat(filePath);

          totalSize += fileStats.size;

          if (!oldestDate || fileStats.mtime < oldestDate) {
            oldestDate = fileStats.mtime;
          }

          if (!newestDate || fileStats.mtime > newestDate) {
            newestDate = fileStats.mtime;
          }
        }

        stats.totalSize = totalSize;
        stats.oldestArchive = oldestDate;
        stats.newestArchive = newestDate;
      }
    } catch (error) {
      logger.error(`Failed to get storage stats: ${error.message}`);
    }

    return stats;
  }
}

module.exports = new StorageService();

const { describe, it, beforeEach, afterEach, expect, jest } = require('@jest/globals');
const mongoose = require('mongoose');
const AIAnalysisResult = require('../models/AIAnalysisResult');
const AIConfig = require('../models/AIConfig');

// Mock dependencies
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    stop: jest.fn(),
    running: true
  }))
}));

jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../services/aiSchedulerService', () => ({
  getScheduleStatus: jest.fn(() => ({
    initialized: true,
    scheduledJobs: 1,
    runningAnalyses: 0
  }))
}));

jest.mock('../services/dataRetentionService', () => ({
  getRetentionStatus: jest.fn(() => ({
    initialized: true,
    activeCleanupJobs: 1,
    activeCompressionJobs: 1
  }))
}));

// Import after mocking
const aiMonitoringService = require('../services/aiMonitoringService');

describe('AI Monitoring Service', () => {
  let testConfig;
  let testUser;

  beforeEach(async () => {
    testUser = new mongoose.Types.ObjectId();

    testConfig = new AIConfig({
      name: 'Test AI Configuration',
      description: 'Test configuration for monitoring',
      version: '1.0.0',
      isActive: true,
      createdBy: testUser,
      analysisSettings: {
        interval: 300,
        timeRange: '1h',
        enabledLogTypes: ['warn', 'error', 'fatal'],
        autoScheduling: {
          enabled: true
        }
      }
    });

    await testConfig.save();
  });

  afterEach(async () => {
    await AIConfig.deleteMany({});
    await AIAnalysisResult.deleteMany({});
    
    if (aiMonitoringService.initialized) {
      await aiMonitoringService.shutdown();
    }
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      await aiMonitoringService.initialize();
      
      expect(aiMonitoringService.initialized).toBe(true);
      const metrics = aiMonitoringService.getMetrics();
      expect(metrics.initialized).toBe(true);
    });

    it('should load initial metrics', async () => {
      // Create some test analysis results
      const results = [
        {
          analysisId: 'test-1',
          configId: testConfig._id,
          type: 'periodic',
          status: 'completed',
          execution: {
            startedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            duration: 5000
          },
          summary: { totalLogs: 100, anomalies: 2, threats: 1, patterns: 3 }
        },
        {
          analysisId: 'test-2',
          configId: testConfig._id,
          type: 'periodic',
          status: 'failed',
          execution: {
            startedAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
            duration: 3000
          },
          summary: { totalLogs: 0, anomalies: 0, threats: 0, patterns: 0 }
        }
      ];

      await AIAnalysisResult.insertMany(results);
      await aiMonitoringService.initialize();

      const metrics = aiMonitoringService.getMetrics();
      expect(metrics.totalAnalyses).toBe(2);
      expect(metrics.successfulAnalyses).toBe(1);
      expect(metrics.failedAnalyses).toBe(1);
    });
  });

  describe('Health Checks', () => {
    beforeEach(async () => {
      await aiMonitoringService.initialize();
    });

    it('should perform health check successfully', async () => {
      await aiMonitoringService.performHealthCheck();
      
      const metrics = aiMonitoringService.getMetrics();
      expect(metrics.lastHealthCheck).toBeDefined();
      expect(metrics.systemHealth).toBeDefined();
    });

    it('should check scheduler health', async () => {
      const schedulerHealth = await aiMonitoringService.checkSchedulerHealth();
      
      expect(schedulerHealth).toHaveProperty('score');
      expect(schedulerHealth).toHaveProperty('status');
      expect(schedulerHealth.score).toBeGreaterThan(0);
      expect(schedulerHealth.status).toBe('healthy');
    });

    it('should check data retention health', async () => {
      const retentionHealth = await aiMonitoringService.checkDataRetentionHealth();
      
      expect(retentionHealth).toHaveProperty('score');
      expect(retentionHealth).toHaveProperty('status');
      expect(retentionHealth.score).toBeGreaterThan(0);
    });

    it('should check recent analyses health', async () => {
      // Create a recent successful analysis
      const recentAnalysis = new AIAnalysisResult({
        analysisId: 'recent-success',
        configId: testConfig._id,
        type: 'periodic',
        status: 'completed',
        execution: {
          startedAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
          completedAt: new Date(Date.now() - 9 * 60 * 1000),
          duration: 60000
        },
        summary: { totalLogs: 100, anomalies: 1, threats: 0, patterns: 2 }
      });
      await recentAnalysis.save();

      const analysesHealth = await aiMonitoringService.checkRecentAnalysesHealth();
      
      expect(analysesHealth).toHaveProperty('score');
      expect(analysesHealth).toHaveProperty('status');
      expect(analysesHealth.details.recentCount).toBe(1);
    });

    it('should detect high failure rate', async () => {
      // Create multiple failed analyses
      const failedAnalyses = Array.from({ length: 5 }, (_, i) => ({
        analysisId: `failed-${i}`,
        configId: testConfig._id,
        type: 'periodic',
        status: 'failed',
        execution: {
          startedAt: new Date(Date.now() - (i + 1) * 5 * 60 * 1000), // Spread over last 25 minutes
          duration: 1000
        },
        summary: { totalLogs: 0, anomalies: 0, threats: 0, patterns: 0 }
      }));

      await AIAnalysisResult.insertMany(failedAnalyses);

      const analysesHealth = await aiMonitoringService.checkRecentAnalysesHealth();
      
      expect(analysesHealth.score).toBeLessThan(0.8);
      expect(analysesHealth.issues.some(issue => issue.includes('failure rate'))).toBe(true);
    });

    it('should detect stuck analyses', async () => {
      // Create a stuck analysis
      const stuckAnalysis = new AIAnalysisResult({
        analysisId: 'stuck-analysis',
        configId: testConfig._id,
        type: 'periodic',
        status: 'running',
        execution: {
          startedAt: new Date(Date.now() - 10 * 60 * 1000) // Started 10 minutes ago
        },
        summary: { totalLogs: 0, anomalies: 0, threats: 0, patterns: 0 }
      });
      await stuckAnalysis.save();

      const analysesHealth = await aiMonitoringService.checkRecentAnalysesHealth();
      
      expect(analysesHealth.issues.some(issue => issue.includes('stuck'))).toBe(true);
    });

    it('should check system resources health', async () => {
      const resourcesHealth = await aiMonitoringService.checkSystemResourcesHealth();
      
      expect(resourcesHealth).toHaveProperty('score');
      expect(resourcesHealth).toHaveProperty('status');
      expect(resourcesHealth.details).toHaveProperty('memoryUsageMB');
      expect(resourcesHealth.details).toHaveProperty('uptime');
    });
  });

  describe('Alert Generation', () => {
    beforeEach(async () => {
      await aiMonitoringService.initialize();
    });

    it('should generate alerts for critical issues', async () => {
      // Mock a critical health status
      const criticalHealthStatus = {
        timestamp: new Date(),
        scheduler: {
          score: 0.3,
          status: 'critical',
          issues: ['Scheduler not responding']
        },
        recentAnalyses: {
          score: 0.8,
          status: 'healthy',
          issues: []
        }
      };

      await aiMonitoringService.checkForAlerts(criticalHealthStatus);
      
      const metrics = aiMonitoringService.getMetrics();
      expect(metrics.alerts).toBeDefined();
      expect(metrics.alerts.length).toBeGreaterThan(0);
      expect(metrics.alerts[0].type).toBe('critical');
    });

    it('should detect consecutive failures', async () => {
      // Create consecutive failed analyses
      const consecutiveFailures = Array.from({ length: 4 }, (_, i) => ({
        analysisId: `consecutive-fail-${i}`,
        configId: testConfig._id,
        type: 'periodic',
        status: 'failed',
        execution: {
          startedAt: new Date(Date.now() - (i + 1) * 60 * 1000), // 1 minute apart
          duration: 1000
        },
        summary: { totalLogs: 0, anomalies: 0, threats: 0, patterns: 0 }
      }));

      await AIAnalysisResult.insertMany(consecutiveFailures);

      const consecutiveCount = await aiMonitoringService.getConsecutiveFailures();
      expect(consecutiveCount).toBe(4);
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await aiMonitoringService.initialize();
    });

    it('should check performance trends', async () => {
      // Create analyses with varying performance
      const performanceData = [
        {
          analysisId: 'perf-1',
          configId: testConfig._id,
          type: 'periodic',
          status: 'completed',
          execution: {
            startedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
            duration: 30000 // 30 seconds
          },
          summary: { totalLogs: 1000, anomalies: 5, threats: 2, patterns: 8 }
        },
        {
          analysisId: 'perf-2',
          configId: testConfig._id,
          type: 'periodic',
          status: 'completed',
          execution: {
            startedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
            duration: 45000 // 45 seconds
          },
          summary: { totalLogs: 1500, anomalies: 3, threats: 1, patterns: 6 }
        }
      ];

      await AIAnalysisResult.insertMany(performanceData);
      await aiMonitoringService.checkPerformanceTrends();

      // Should complete without errors
      expect(true).toBe(true);
    });
  });

  describe('Metrics Retrieval', () => {
    beforeEach(async () => {
      await aiMonitoringService.initialize();
    });

    it('should return comprehensive metrics', () => {
      const metrics = aiMonitoringService.getMetrics();
      
      expect(metrics).toHaveProperty('initialized');
      expect(metrics).toHaveProperty('totalAnalyses');
      expect(metrics).toHaveProperty('successfulAnalyses');
      expect(metrics).toHaveProperty('failedAnalyses');
      expect(metrics).toHaveProperty('averageAnalysisTime');
      expect(metrics).toHaveProperty('systemHealth');
      expect(metrics).toHaveProperty('alerts');
    });
  });

  describe('Shutdown', () => {
    it('should shutdown gracefully', async () => {
      await aiMonitoringService.initialize();
      expect(aiMonitoringService.initialized).toBe(true);
      
      await aiMonitoringService.shutdown();
      expect(aiMonitoringService.initialized).toBe(false);
    });
  });
});

const { describe, it, beforeEach, afterEach, expect, jest } = require('@jest/globals');
const mongoose = require('mongoose');
const AIConfig = require('../models/AIConfig');
const AIAnalysisResult = require('../models/AIAnalysisResult');

// Mock the scheduler service to avoid actual cron jobs during testing
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    stop: jest.fn(),
    running: true,
    nextDate: jest.fn(() => new Date())
  }))
}));

// Mock logger to avoid console output during tests
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

// Import after mocking
const aiSchedulerService = require('../services/aiSchedulerService');

describe('AI Scheduler Service', () => {
  let testConfig;
  let testUser;

  beforeEach(async () => {
    // Create test user
    testUser = new mongoose.Types.ObjectId();

    // Create test configuration
    testConfig = new AIConfig({
      name: 'Test AI Configuration',
      description: 'Test configuration for automated analysis',
      version: '1.0.0',
      isActive: true,
      createdBy: testUser,
      analysisSettings: {
        interval: 300, // 5 minutes
        timeRange: '1h',
        enabledLogTypes: ['warn', 'error', 'fatal'],
        maxLogsPerAnalysis: 10000,
        autoScheduling: {
          enabled: true,
          timezone: 'UTC',
          maxConcurrentRuns: 1,
          skipIfRunning: true
        }
      },
      dataRetention: {
        analysisResultsRetentionDays: 180,
        autoCleanup: {
          enabled: true,
          schedule: '0 2 * * *'
        }
      }
    });

    await testConfig.save();
  });

  afterEach(async () => {
    // Clean up test data
    await AIConfig.deleteMany({});
    await AIAnalysisResult.deleteMany({});

    // Reset scheduler state
    if (aiSchedulerService.initialized) {
      await aiSchedulerService.shutdown();
    }
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      await aiSchedulerService.initialize();

      expect(aiSchedulerService.initialized).toBe(true);
      const status = aiSchedulerService.getScheduleStatus();
      expect(status.initialized).toBe(true);
    });

    it('should perform startup recovery', async () => {
      // Create a stuck analysis
      const stuckAnalysis = new AIAnalysisResult({
        analysisId: 'stuck-analysis-123',
        configId: testConfig._id,
        type: 'periodic',
        status: 'running',
        analysisParameters: {
          timeRange: '1h',
          startTime: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
          endTime: new Date(),
          logTypes: ['warn', 'error']
        },
        execution: {
          startedAt: new Date(Date.now() - 45 * 60 * 1000) // Started 45 minutes ago
        }
      });
      await stuckAnalysis.save();

      await aiSchedulerService.initialize();

      // Check that stuck analysis was marked as failed
      const updatedAnalysis = await AIAnalysisResult.findOne({ analysisId: 'stuck-analysis-123' });
      expect(updatedAnalysis.status).toBe('failed');
      expect(updatedAnalysis.execution.errorMessage).toContain('System restart');
    });
  });

  describe('Configuration Management', () => {
    beforeEach(async () => {
      await aiSchedulerService.initialize();
    });

    it('should schedule analysis for active configuration', async () => {
      await aiSchedulerService.scheduleAnalysis(testConfig);

      const status = aiSchedulerService.getScheduleStatus();
      expect(status.scheduledJobs).toBeGreaterThan(0);
    });

    it('should update schedule when configuration changes', async () => {
      await aiSchedulerService.scheduleAnalysis(testConfig);

      // Update configuration
      testConfig.analysisSettings.interval = 600; // 10 minutes
      await testConfig.save();

      await aiSchedulerService.updateSchedule(testConfig._id.toString());

      // Verify schedule was updated
      const status = aiSchedulerService.getScheduleStatus();
      expect(status.scheduledJobs).toBeGreaterThan(0);
    });

    it('should remove schedule when configuration is disabled', async () => {
      await aiSchedulerService.scheduleAnalysis(testConfig);

      const configId = testConfig._id.toString();
      await aiSchedulerService.removeSchedule(configId);

      const status = aiSchedulerService.getScheduleStatus();
      const configSchedule = status.schedules.find(s => s.configId === configId);
      expect(configSchedule).toBeUndefined();
    });
  });

  describe('Duplicate Run Prevention', () => {
    beforeEach(async () => {
      await aiSchedulerService.initialize();
    });

    it('should prevent duplicate runs when analysis is already running', async () => {
      // Create a running analysis
      const runningAnalysis = new AIAnalysisResult({
        analysisId: 'running-analysis-123',
        configId: testConfig._id,
        type: 'periodic',
        status: 'running',
        analysisParameters: {
          timeRange: '1h',
          startTime: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
          endTime: new Date(),
          logTypes: ['warn', 'error']
        },
        execution: {
          startedAt: new Date(Date.now() - 2 * 60 * 1000)
        }
      });
      await runningAnalysis.save();

      const duplicateCheck = await aiSchedulerService.checkForDuplicateRuns(testConfig, 'new-analysis-456');

      expect(duplicateCheck.canProceed).toBe(false);
      expect(duplicateCheck.reason).toContain('already running');
    });

    it('should prevent rapid-fire scheduling', async () => {
      // Create a recently completed analysis
      const recentAnalysis = new AIAnalysisResult({
        analysisId: 'recent-analysis-123',
        configId: testConfig._id,
        type: 'periodic',
        status: 'completed',
        analysisParameters: {
          timeRange: '1h',
          startTime: new Date(Date.now() - 5 * 60 * 1000),
          endTime: new Date(Date.now() - 4 * 60 * 1000),
          logTypes: ['warn', 'error']
        },
        execution: {
          startedAt: new Date(Date.now() - 5 * 60 * 1000),
          completedAt: new Date(Date.now() - 30 * 1000) // 30 seconds ago
        }
      });
      await recentAnalysis.save();

      const duplicateCheck = await aiSchedulerService.checkForDuplicateRuns(testConfig, 'new-analysis-456');

      expect(duplicateCheck.canProceed).toBe(false);
      expect(duplicateCheck.reason).toContain('completed too recently');
    });

    it('should allow analysis when no conflicts exist', async () => {
      const duplicateCheck = await aiSchedulerService.checkForDuplicateRuns(testConfig, 'new-analysis-456');

      expect(duplicateCheck.canProceed).toBe(true);
    });
  });

  describe('Graceful Shutdown', () => {
    beforeEach(async () => {
      await aiSchedulerService.initialize();
    });

    it('should stop all scheduled jobs during shutdown', async () => {
      await aiSchedulerService.scheduleAnalysis(testConfig);

      const statusBefore = aiSchedulerService.getScheduleStatus();
      expect(statusBefore.scheduledJobs).toBeGreaterThan(0);

      await aiSchedulerService.shutdown();

      const statusAfter = aiSchedulerService.getScheduleStatus();
      expect(statusAfter.initialized).toBe(false);
      expect(statusAfter.scheduledJobs).toBe(0);
    });

    it('should wait for running analyses during shutdown', async () => {
      // Mock a running analysis
      const mockAnalysisResult = {
        markFailed: jest.fn().mockResolvedValue()
      };

      aiSchedulerService.runningAnalyses.set('test-analysis', {
        configId: testConfig._id.toString(),
        startTime: new Date(),
        analysisResult: mockAnalysisResult
      });

      await aiSchedulerService.shutdown();

      // Should have marked the running analysis as cancelled
      expect(mockAnalysisResult.markFailed).toHaveBeenCalledWith(
        expect.stringContaining('System shutdown')
      );
    });
  });

  describe('Health Status', () => {
    it('should return health status', async () => {
      await aiSchedulerService.initialize();

      const health = await aiSchedulerService.getHealthStatus();

      expect(health).toHaveProperty('initialized', true);
      expect(health).toHaveProperty('shutdownInProgress', false);
      expect(health).toHaveProperty('scheduledJobs');
      expect(health).toHaveProperty('runningAnalyses');
      expect(health).toHaveProperty('uptime');
    });
  });

  describe('Configuration Validation', () => {
    it('should generate correct cron expression for intervals', () => {
      // Test minute intervals
      testConfig.analysisSettings.interval = 300; // 5 minutes
      const cronExpr5min = testConfig.generateCronExpression();
      expect(cronExpr5min).toBe('*/5 * * * *');

      // Test hour intervals
      testConfig.analysisSettings.interval = 7200; // 2 hours
      const cronExpr2hour = testConfig.generateCronExpression();
      expect(cronExpr2hour).toBe('0 */2 * * *');

      // Test daily interval
      testConfig.analysisSettings.interval = 86400; // 24 hours
      const cronExprDaily = testConfig.generateCronExpression();
      expect(cronExprDaily).toBe('0 0 * * *');
    });

    it('should validate configuration settings', () => {
      const validation = testConfig.validateConfig();
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject invalid configuration settings', () => {
      testConfig.analysisSettings.interval = 30; // Too short
      testConfig.analysisSettings.autoScheduling.maxConcurrentRuns = 10; // Too many

      const validation = testConfig.validateConfig();
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });
});

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const config = require('../config');
const logger = require('../utils/logger');
const User = require('../models/User');

class WebSocketServer {
  constructor() {
    this.wss = null;
    this.clients = new Map();
  }

  start() {
    const port = config.websocket.port || 5001;

    this.wss = new WebSocket.Server({
      port,
      host: '0.0.0.0', // Bind to all interfaces for network access
      perMessageDeflate: false,
    });

    this.wss.on('connection', (ws, req) => {
      const clientId = this.generateClientId();
      this.clients.set(clientId, {
        ws,
        userId: null,
        subscriptions: new Set(),
        lastPing: Date.now(),
      });

      logger.info(`WebSocket client connected: ${clientId}`);

      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          this.handleMessage(clientId, data);
        } catch (error) {
          logger.error('Invalid WebSocket message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format',
          }));
        }
      });

      ws.on('close', () => {
        logger.info(`WebSocket client disconnected: ${clientId}`);
        this.clients.delete(clientId);
      });

      ws.on('error', (error) => {
        logger.error(`WebSocket error for client ${clientId}:`, error);
        this.clients.delete(clientId);
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        clientId,
        timestamp: new Date().toISOString(),
      }));
    });

    // Setup ping/pong for connection health
    this.setupHeartbeat();

    logger.info(`WebSocket server started on port ${port}`);
  }

  async handleMessage(clientId, data) {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (data.type) {
      case 'ping':
        client.lastPing = Date.now();
        client.ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date().toISOString(),
        }));
        break;

      case 'subscribe':
        if (data.channel && client.userId) {
          client.subscriptions.add(data.channel);
          client.ws.send(JSON.stringify({
            type: 'subscribed',
            channel: data.channel,
          }));
        } else {
          client.ws.send(JSON.stringify({
            type: 'error',
            message: 'Authentication required to subscribe to channels',
          }));
        }
        break;

      case 'unsubscribe':
        if (data.channel) {
          client.subscriptions.delete(data.channel);
          client.ws.send(JSON.stringify({
            type: 'unsubscribed',
            channel: data.channel,
          }));
        }
        break;

      case 'auth':
        await this.authenticateClient(clientId, data.token);
        break;

      case 'mark_notification_read':
        if (client.userId && data.notificationId) {
          // Handle marking notification as read
          this.handleNotificationRead(client.userId, data.notificationId);
        }
        break;

      default:
        client.ws.send(JSON.stringify({
          type: 'error',
          message: 'Unknown message type',
        }));
    }
  }

  broadcast(channel, data) {
    const message = JSON.stringify({
      type: 'broadcast',
      channel,
      data,
      timestamp: new Date().toISOString(),
    });

    this.clients.forEach((client) => {
      if (client.subscriptions.has(channel) && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(message);
      }
    });
  }

  sendToUser(userId, data) {
    const message = JSON.stringify({
      type: 'message',
      data,
      timestamp: new Date().toISOString(),
    });

    this.clients.forEach((client) => {
      if (client.userId === userId && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(message);
      }
    });
  }

  // AI-specific broadcasting methods
  broadcastAIThreatLevelChanged(threatLevel, details) {
    this.broadcast('ai:threat-level-changed', {
      threatLevel,
      details,
      timestamp: new Date().toISOString()
    });
  }

  broadcastAIAnomalyDetected(anomaly) {
    this.broadcast('ai:anomaly-detected', {
      anomaly,
      timestamp: new Date().toISOString()
    });
  }

  broadcastAIAnalysisComplete(analysisResults) {
    this.broadcast('ai:analysis-complete', {
      results: analysisResults,
      timestamp: new Date().toISOString()
    });
  }

  broadcastAIModelUpdated(modelInfo) {
    this.broadcast('ai:model-updated', {
      modelInfo,
      timestamp: new Date().toISOString()
    });
  }

  broadcastAIThreatDetected(threat) {
    this.broadcast('ai:threat-detected', {
      threat,
      timestamp: new Date().toISOString()
    });
  }

  broadcastAIInsightsUpdated(insights) {
    this.broadcast('ai:insights-updated', {
      insights,
      timestamp: new Date().toISOString()
    });
  }

  async authenticateClient(clientId, token) {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      if (!token) {
        throw new Error('No token provided');
      }

      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret);
      const user = await User.findById(decoded.id).select('_id username firstName lastName role status');

      if (!user || user.status !== 'active') {
        throw new Error('Invalid user or user not active');
      }

      client.userId = user._id.toString();
      client.user = user;
      client.authenticated = true;

      // Subscribe to user-specific channel
      client.subscriptions.add(`user:${client.userId}`);

      client.ws.send(JSON.stringify({
        type: 'authenticated',
        userId: client.userId,
        user: {
          id: user._id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        },
      }));

      logger.info(`WebSocket client authenticated: ${clientId} for user ${user.username}`);
    } catch (error) {
      logger.error(`WebSocket authentication failed for client ${clientId}:`, error);
      client.ws.send(JSON.stringify({
        type: 'auth_error',
        message: 'Authentication failed',
      }));
    }
  }

  async handleNotificationRead(userId, notificationId) {
    try {
      const Notification = require('../models/Notification');
      await Notification.findOneAndUpdate(
        { _id: notificationId, userId },
        { read: true, readAt: new Date() }
      );

      logger.debug(`Notification ${notificationId} marked as read for user ${userId}`);
    } catch (error) {
      logger.error('Error marking notification as read:', error);
    }
  }

  sendNotificationToUser(userId, notification) {
    const message = JSON.stringify({
      type: 'notification',
      data: notification,
      timestamp: new Date().toISOString(),
    });

    this.clients.forEach((client) => {
      if (client.userId === userId && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(message);
      }
    });
  }

  broadcastToChannel(channel, data) {
    const message = JSON.stringify({
      type: 'broadcast',
      channel,
      data,
      timestamp: new Date().toISOString(),
    });

    this.clients.forEach((client) => {
      if (client.subscriptions.has(channel) && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(message);
      }
    });
  }

  getConnectedUsers() {
    const users = new Set();
    this.clients.forEach((client) => {
      if (client.userId) {
        users.add(client.userId);
      }
    });
    return Array.from(users);
  }

  getClientStats() {
    const stats = {
      totalClients: this.clients.size,
      authenticatedClients: 0,
      uniqueUsers: new Set(),
    };

    this.clients.forEach((client) => {
      if (client.userId) {
        stats.authenticatedClients++;
        stats.uniqueUsers.add(client.userId);
      }
    });

    stats.uniqueUsers = stats.uniqueUsers.size;
    return stats;
  }

  setupHeartbeat() {
    const interval = setInterval(() => {
      const now = Date.now();

      this.clients.forEach((client, clientId) => {
        if (now - client.lastPing > 60000) { // 60 seconds timeout
          logger.warn(`Removing inactive WebSocket client: ${clientId}`);
          client.ws.terminate();
          this.clients.delete(clientId);
        }
      });
    }, 30000); // Check every 30 seconds

    this.wss.on('close', () => {
      clearInterval(interval);
    });
  }

  generateClientId() {
    return Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
  }

  stop() {
    if (this.wss) {
      this.wss.close();
      logger.info('WebSocket server stopped');
    }
  }
}

// Start the WebSocket server if this file is run directly
if (require.main === module) {
  const wsServer = new WebSocketServer();
  wsServer.start();

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('Received SIGTERM, shutting down WebSocket server...');
    wsServer.stop();
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('Received SIGINT, shutting down WebSocket server...');
    wsServer.stop();
    process.exit(0);
  });
}

module.exports = WebSocketServer;

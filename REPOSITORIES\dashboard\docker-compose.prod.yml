services:
  # Frontend Service
  frontend:
    image: exlog/exlog-frontend:${EXLOG_VERSION:-latest}
    environment:
      - VITE_API_URL=/api/v1
      - REACT_APP_API_URL=/api/v1
      - REACT_APP_WS_URL=/ws
    depends_on:
      - backend
    networks:
      - exlog-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Service
  backend:
    image: exlog/exlog-backend:${EXLOG_VERSION:-latest}
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/exlog
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-24h}
      # Email configuration
      - RESEND_API_KEY=${RESEND_API_KEY:-}
      - EMAIL_HOST=${EMAIL_HOST:-smtp.gmail.com}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_SECURE=${EMAIL_SECURE:-true}
      - EMAIL_USER=${EMAIL_USER:-}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD:-}
      # Log retention and storage configuration
      - LOG_RETENTION_SECONDS=${LOG_RETENTION_SECONDS:-7776000}
      - ALERT_RETENTION_SECONDS=${ALERT_RETENTION_SECONDS:-31536000}
      - ENABLE_AUTO_DELETE=${ENABLE_AUTO_DELETE:-true}
      - ARCHIVE_BEFORE_DELETE=${ARCHIVE_BEFORE_DELETE:-true}
      - ARCHIVE_RETENTION_DAYS=${ARCHIVE_RETENTION_DAYS:-2555}
      - LOG_STORAGE_PATH=/app/logs/storage
      - LOG_ARCHIVE_PATH=/app/logs/archive
      - EXTERNAL_STORAGE_TYPE=${EXTERNAL_STORAGE_TYPE:-local}
      - LOG_COMPRESSION_ENABLED=${LOG_COMPRESSION_ENABLED:-true}
      - LOG_COMPRESSION_ALGORITHM=${LOG_COMPRESSION_ALGORITHM:-gzip}
      # Cloud storage configuration
      - S3_BUCKET=${S3_BUCKET:-}
      - S3_REGION=${S3_REGION:-us-east-1}
      - S3_ACCESS_KEY_ID=${S3_ACCESS_KEY_ID:-}
      - S3_SECRET_ACCESS_KEY=${S3_SECRET_ACCESS_KEY:-}
      - AZURE_STORAGE_CONNECTION_STRING=${AZURE_STORAGE_CONNECTION_STRING:-}
      - AZURE_CONTAINER_NAME=${AZURE_CONTAINER_NAME:-logs}
      - GCP_PROJECT_ID=${GCP_PROJECT_ID:-}
      - GCP_BUCKET_NAME=${GCP_BUCKET_NAME:-}
    depends_on:
      - mongodb
    volumes:
      # Persistent log storage volumes
      - exlog_logs:/app/logs
      - exlog_log_storage:/app/logs/storage
      - exlog_log_archive:/app/logs/archive
      # Optional: Mount cloud credentials
      - ${GCP_KEY_FILE_PATH:-/dev/null}:/app/gcp-key.json:ro
    networks:
      - exlog-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # WebSocket Service
  websocket:
    image: exlog/exlog-websocket:${EXLOG_VERSION:-latest}
    environment:
      - NODE_ENV=production
      - PORT=5001
    networks:
      - exlog-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://127.0.0.1:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    environment:
      - MONGO_INITDB_DATABASE=exlog
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
      # MongoDB logs
      - exlog_logs:/var/log/mongodb
    networks:
      - exlog-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      # Nginx logs
      - exlog_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
      - websocket
    networks:
      - exlog-network
    restart: unless-stopped

volumes:
  mongodb_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${MONGODB_DATA_PATH:-./data/mongodb}
  mongodb_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${MONGODB_CONFIG_PATH:-./data/mongodb-config}
  # Persistent log storage volumes
  exlog_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_HOST_PATH:-./data/logs}
  exlog_log_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_STORAGE_HOST_PATH:-./data/logs/storage}
  exlog_log_archive:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_ARCHIVE_HOST_PATH:-./data/logs/archive}

networks:
  exlog-network:
    driver: bridge

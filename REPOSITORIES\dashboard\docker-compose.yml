services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: /api/v1
        REACT_APP_API_URL: /api/v1
        REACT_APP_WS_URL: /ws
    ports:
      - "3000:3000"
    environment:
      # Use dynamic API URLs that work with both localhost and IP access
      - VITE_API_URL=/api/v1
      - REACT_APP_API_URL=/api/v1
      - REACT_APP_WS_URL=/ws
      # Fallback URLs for direct access (when not using nginx proxy)
      - REACT_APP_API_URL_FALLBACK=http://localhost:5000/api/v1
      - REACT_APP_WS_URL_FALLBACK=ws://localhost:5001
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - exlog-network

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/exlog
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_EXPIRES_IN=24h
      # Email configuration
      - RESEND_API_KEY=${RESEND_API_KEY:-}
      - EMAIL_HOST=${EMAIL_HOST:-smtp.gmail.com}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_SECURE=${EMAIL_SECURE:-false}
      - EMAIL_USER=${EMAIL_USER:-}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD:-}
      # CORS configuration to allow network access
      - CORS_ORIGIN=*
      # Alternative: specify multiple origins
      # - CORS_ORIGIN=http://localhost:3000,http://localhost,http://127.0.0.1:3000,http://127.0.0.1
      # Log retention and storage configuration
      - LOG_RETENTION_SECONDS=${LOG_RETENTION_SECONDS:-7776000}
      - ALERT_RETENTION_SECONDS=${ALERT_RETENTION_SECONDS:-31536000}
      - ENABLE_AUTO_DELETE=${ENABLE_AUTO_DELETE:-false}
      - ARCHIVE_BEFORE_DELETE=${ARCHIVE_BEFORE_DELETE:-true}
      - ARCHIVE_RETENTION_DAYS=${ARCHIVE_RETENTION_DAYS:-2555}
      - LOG_STORAGE_PATH=/app/logs/storage
      - LOG_ARCHIVE_PATH=/app/logs/archive
      - EXTERNAL_STORAGE_TYPE=${EXTERNAL_STORAGE_TYPE:-local}
      - LOG_COMPRESSION_ENABLED=${LOG_COMPRESSION_ENABLED:-true}
      - LOG_COMPRESSION_ALGORITHM=${LOG_COMPRESSION_ALGORITHM:-gzip}
      # Cloud storage configuration (if needed)
      - S3_BUCKET=${S3_BUCKET:-}
      - S3_REGION=${S3_REGION:-us-east-1}
      - S3_ACCESS_KEY_ID=${S3_ACCESS_KEY_ID:-}
      - S3_SECRET_ACCESS_KEY=${S3_SECRET_ACCESS_KEY:-}
      - AZURE_STORAGE_CONNECTION_STRING=${AZURE_STORAGE_CONNECTION_STRING:-}
      - AZURE_CONTAINER_NAME=${AZURE_CONTAINER_NAME:-logs}
      - GCP_PROJECT_ID=${GCP_PROJECT_ID:-}
      - GCP_BUCKET_NAME=${GCP_BUCKET_NAME:-}
      - AI_SERVICE_KEY=ai-service-internal-key
    depends_on:
      - mongodb
    volumes:
      - ./backend:/app
      - /app/node_modules
      # Persistent log storage volumes
      - exlog_logs:/app/logs
      - exlog_log_storage:/app/logs/storage
      - exlog_log_archive:/app/logs/archive
    networks:
      - exlog-network

  # WebSocket Service
  websocket:
    build:
      context: ./backend
      dockerfile: Dockerfile.websocket
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=development
      - PORT=5001
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - exlog-network

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=exlog
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - exlog-network

  # AI Insights Service
  ai-insights:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    container_name: exlog-ai-insights
    ports:
      - "5002:5002"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=5002
      - MONGODB_URI=mongodb://mongodb:27017/exlog
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - ANALYSIS_INTERVAL=${ANALYSIS_INTERVAL:-300}
      - AI_ANOMALY_THRESHOLD=${AI_ANOMALY_THRESHOLD:-0.1}
      - AI_SERVICE_KEY=ai-service-internal-key
    volumes:
      - ./ai-service:/app
      - /app/node_modules
      - ai_models:/app/models
      - ai_cache:/app/cache
      - ai_logs:/app/logs
    depends_on:
      - mongodb
      - backend
    networks:
      - exlog-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:5002/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80" # Use port 8080 to avoid conflict with existing web server
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
      - websocket
      - ai-insights
    networks:
      - exlog-network

volumes:
  mongodb_data:

  exlog_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_HOST_PATH:-./data/logs}
  exlog_log_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_STORAGE_HOST_PATH:-./data/logs/storage}
  exlog_log_archive:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_ARCHIVE_HOST_PATH:-./data/logs/archive}

  # AI Service volumes
  ai_models:
    driver: local
  ai_cache:
    driver: local
  ai_logs:
    driver: local

networks:
  exlog-network:
    driver: bridge

# ExLog Dashboard - SSL/HTTPS Setup Guide

## Overview

ExLog Dashboard includes automated SSL/HTTPS setup using Let's Encrypt certificates. This guide covers both automated and manual SSL configuration.

## Automated SSL Setup (Recommended)

The automated SSL setup script handles the entire process of obtaining and configuring SSL certificates.

### Prerequisites

1. **Domain Name**: You need a domain name pointing to your server's public IP
2. **Public Access**: Ports 80 and 443 must be accessible from the internet
3. **ExLog Running**: ExLog Dashboard containers must be running
4. **Root Access**: Script requires sudo privileges for certificate management

### Quick Setup

```bash
# Navigate to ExLog Dashboard directory
cd /path/to/exlog-dashboard

# Run automated SSL setup
sudo ./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL>
```

### Advanced Usage

```bash
# Test with Let's Encrypt staging (recommended for testing)
sudo ./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL> --staging

# Force certificate renewal
sudo ./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL> --force

# Use custom docker-compose file
sudo ./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL> -c custom-compose.yml
```

### What the Script Does

1. **Validates Prerequisites**: Checks Docker, domain, email format
2. **Installs Certbot**: Automatically installs Let's Encrypt client
3. **Generates Certificates**: Obtains SSL certificates from Let's Encrypt
4. **Updates Nginx Config**: Configures nginx with SSL settings and HTTPS redirect
5. **Updates Docker Compose**: Adds SSL certificate volume mounts
6. **Sets Up Auto-Renewal**: Configures automatic certificate renewal via cron
7. **Tests Configuration**: Verifies SSL setup is working

### Script Options

| Option | Description | Required |
|--------|-------------|----------|
| `-d, --domain` | Domain name for SSL certificate | Yes |
| `-e, --email` | Email for Let's Encrypt registration | Yes |
| `-s, --staging` | Use staging environment (for testing) | No |
| `-f, --force` | Force certificate renewal | No |
| `-c, --compose` | Custom docker-compose file path | No |
| `-h, --help` | Show help message | No |

## Manual SSL Setup

If you prefer manual configuration or need custom SSL certificates:

### 1. Obtain SSL Certificates

#### Option A: Let's Encrypt (Free)

```bash
# Install Certbot
sudo apt-get install certbot

# Stop nginx temporarily
docker-compose stop nginx

# Generate certificate
sudo certbot certonly --standalone -d your-domain.com

# Start nginx
docker-compose start nginx
```

#### Option B: Custom Certificates

Place your certificates in `/etc/ssl/certs/`:
- Certificate: `/etc/ssl/certs/your-domain.crt`
- Private Key: `/etc/ssl/private/your-domain.key`
- Certificate Chain: `/etc/ssl/certs/your-domain-chain.crt`

### 2. Update Nginx Configuration

Create SSL-enabled nginx configuration:

```nginx
# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL security settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:MozTLS:10m;
    ssl_session_tickets off;

    # Security headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    
    # Include your existing location blocks here
    # (API routes, WebSocket, frontend, etc.)
}
```

### 3. Update Docker Compose

Add SSL certificate volumes to your nginx service:

```yaml
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"  # Add HTTPS port
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro  # Add SSL certificates
      - /var/www/certbot:/var/www/certbot:ro  # Add for renewals
    depends_on:
      - frontend
      - backend
```

### 4. Restart Services

```bash
docker-compose up -d nginx
```

## Certificate Renewal

### Automatic Renewal (Recommended)

The automated setup script configures automatic renewal via cron. Certificates are checked twice daily and renewed when needed.

### Manual Renewal

```bash
# Test renewal (dry run)
sudo certbot renew --dry-run

# Renew certificates
sudo certbot renew

# Restart nginx after renewal
docker-compose restart nginx
```

## Security Best Practices

### SSL Configuration

1. **Use Strong Ciphers**: Configure modern cipher suites
2. **Enable HSTS**: Force HTTPS for all future requests
3. **Disable Weak Protocols**: Only allow TLS 1.2 and 1.3
4. **Perfect Forward Secrecy**: Use ECDHE key exchange

### Firewall Configuration

```bash
# Allow HTTPS traffic
sudo ufw allow 443/tcp

# Optional: Block HTTP after SSL setup
# sudo ufw deny 80/tcp
```

### Certificate Monitoring

Monitor certificate expiration:

```bash
# Check certificate expiration
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -text -noout | grep "Not After"

# Set up monitoring alert (example with cron)
echo "0 0 * * 0 root /usr/bin/openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -checkend 604800 -noout || echo 'Certificate expires soon' | mail -s 'SSL Certificate Warning' <EMAIL>" | sudo tee -a /etc/crontab
```

## Troubleshooting

### Common Issues

#### 1. Certificate Generation Fails

```bash
# Check if port 80 is accessible
sudo netstat -tlnp | grep :80

# Verify domain DNS
nslookup your-domain.com

# Check Let's Encrypt rate limits
curl -s "https://crt.sh/?q=your-domain.com&output=json" | jq length
```

#### 2. Nginx SSL Errors

```bash
# Test nginx configuration
docker-compose exec nginx nginx -t

# Check SSL certificate
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -text -noout

# Verify certificate chain
openssl verify -CAfile /etc/letsencrypt/live/your-domain.com/chain.pem /etc/letsencrypt/live/your-domain.com/cert.pem
```

#### 3. Browser SSL Warnings

- **Self-signed certificate**: Use Let's Encrypt or proper CA-signed certificate
- **Mixed content**: Ensure all resources load over HTTPS
- **Certificate mismatch**: Verify certificate domain matches accessed domain

### Debug Commands

```bash
# Check SSL setup
curl -I https://your-domain.com

# Test SSL configuration
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate details
echo | openssl s_client -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates

# View nginx logs
docker-compose logs nginx

# Check certbot logs
sudo tail -f /var/log/letsencrypt/letsencrypt.log
```

## Integration with ExLog Dashboard

### Environment Variables

Update your `.env` file for HTTPS:

```bash
# Update frontend URLs to use HTTPS
REACT_APP_API_URL=https://your-domain.com/api/v1
REACT_APP_WS_URL=wss://your-domain.com/ws

# Update CORS for HTTPS
CORS_ORIGIN=https://your-domain.com
```

### Quick-Start Integration

The SSL setup integrates seamlessly with the quick-start installation:

```bash
# Standard quick-start
curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml -o docker-compose.yml
docker-compose up -d

# Add SSL after quick-start
sudo ./scripts/setup-ssl.sh -d your-domain.com -e <EMAIL>
```

## Production Considerations

### Load Balancing

For production deployments with load balancing:

```nginx
upstream exlog_backend {
    server backend-1:5000;
    server backend-2:5000;
}

server {
    listen 443 ssl http2;
    
    location /api/ {
        proxy_pass http://exlog_backend;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### CDN Integration

When using a CDN (CloudFlare, AWS CloudFront):

1. Configure origin SSL certificates
2. Set up proper cache headers
3. Configure WebSocket proxying
4. Update CORS origins for CDN domains

### Backup Certificates

```bash
# Backup SSL certificates
sudo tar -czf ssl-backup-$(date +%Y%m%d).tar.gz /etc/letsencrypt/

# Store backup securely
aws s3 cp ssl-backup-$(date +%Y%m%d).tar.gz s3://your-backup-bucket/ssl/
```

## Support

For SSL-related issues:

1. Check the troubleshooting section above
2. Review nginx and certbot logs
3. Verify domain DNS configuration
4. Test with SSL testing tools (SSL Labs, etc.)
5. Create an issue at: https://gitlab.com/spr888/dashboard/-/issues

## References

- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [Nginx SSL Configuration](https://nginx.org/en/docs/http/configuring_https_servers.html)
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
- [SSL Labs Server Test](https://www.ssllabs.com/ssltest/)

# ExLog Dashboard Troubleshooting Guide

This guide helps you diagnose and resolve common issues with ExLog Dashboard.

## Quick Diagnostics

### Health Check Script
Run the built-in health check to identify issues:
```bash
./scripts/health-check.sh
```

### Check Service Status
```bash
# View all container status
docker compose ps

# Check specific service logs
docker compose logs [service_name]

# Follow logs in real-time
docker compose logs -f
```

## Common Issues

### 1. Services Won't Start

#### Symptoms
- `docker compose up` fails
- Containers exit immediately
- Services show as "unhealthy"

#### Diagnosis
```bash
# Check Docker daemon
docker info

# Check container logs
docker compose logs

# Check system resources
docker stats
free -h
df -h
```

#### Solutions

**Port Conflicts:**
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8080

# Change ports in docker-compose.yml
ports:
  - "8081:80"  # Change 8080 to 8081
```

**Insufficient Memory:**
```bash
# Check memory usage
free -h

# Increase Docker memory limit (Docker Desktop)
# Or add swap space on Linux
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

**Permission Issues:**
```bash
# Fix Docker permissions (Linux)
sudo usermod -aG docker $USER
newgrp docker

# Fix file permissions
sudo chown -R $USER:$USER ./data
```

### 2. Database Connection Issues

#### Symptoms
- "Cannot connect to MongoDB" errors
- Backend service fails to start
- Database-related errors in logs

#### Diagnosis
```bash
# Check MongoDB container
docker compose logs mongodb

# Test MongoDB connection
docker compose exec mongodb mongosh --eval "db.adminCommand('ping')"

# Check MongoDB status
docker compose exec mongodb mongosh --eval "rs.status()"
```

#### Solutions

**MongoDB Not Starting:**
```bash
# Check MongoDB logs
docker compose logs mongodb

# Remove corrupted data (⚠️ Data loss!)
docker compose down
docker volume rm dashboard_mongodb_data
docker compose up -d
```

**Connection String Issues:**
```bash
# Verify environment variables
docker compose exec backend env | grep MONGODB

# Update connection string in .env
MONGODB_URI=*************************************************************
```

**Authentication Problems:**
```bash
# Reset MongoDB authentication
docker compose exec mongodb mongosh admin --eval "
  db.createUser({
    user: 'admin',
    pwd: 'password',
    roles: ['root']
  })
"
```

### 3. Frontend Not Loading

#### Symptoms
- Blank page or loading spinner
- "Cannot connect to server" errors
- 404 errors for API calls

#### Diagnosis
```bash
# Check frontend logs
docker compose logs frontend

# Check Nginx logs
docker compose logs nginx

# Test API connectivity
curl http://localhost:8080/api/v1/health
```

#### Solutions

**API Connection Issues:**
```bash
# Check backend service
curl http://localhost:5000/health

# Verify Nginx configuration
docker compose exec nginx nginx -t

# Restart Nginx
docker compose restart nginx
```

**Build Issues:**
```bash
# Rebuild frontend
docker compose build frontend
docker compose up -d frontend
```

**CORS Issues:**
```bash
# Update CORS settings in .env
CORS_ORIGIN=http://localhost:8080,http://localhost:3000

# Restart backend
docker compose restart backend
```

### 4. Performance Issues

#### Symptoms
- Slow page loading
- High CPU/memory usage
- Timeouts

#### Diagnosis
```bash
# Check resource usage
docker stats

# Check system load
htop
iotop

# Check database performance
docker compose exec mongodb mongosh exlog --eval "db.stats()"
```

#### Solutions

**High Memory Usage:**
```bash
# Limit container memory
# Add to docker-compose.yml:
deploy:
  resources:
    limits:
      memory: 1G
```

**Database Performance:**
```bash
# Check slow queries
docker compose exec mongodb mongosh exlog --eval "
  db.setProfilingLevel(1, {slowms: 100});
  db.system.profile.find().sort({ts:-1}).limit(5);
"

# Add database indexes
docker compose exec mongodb mongosh exlog --eval "
  db.logs.createIndex({timestamp: -1});
  db.logs.createIndex({level: 1, timestamp: -1});
"
```

**Disk Space Issues:**
```bash
# Clean up Docker
docker system prune -a

# Clean up logs
docker compose exec backend find /app/logs -name "*.log" -mtime +7 -delete

# Rotate logs
docker compose restart
```

### 5. Authentication Issues

#### Symptoms
- Cannot log in
- "Invalid credentials" errors
- Session expires immediately

#### Diagnosis
```bash
# Check backend logs for auth errors
docker compose logs backend | grep -i auth

# Verify JWT configuration
docker compose exec backend env | grep JWT

# Test authentication endpoint
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

#### Solutions

**Default Credentials Not Working:**
```bash
# Reset admin user
docker compose exec backend node -e "
  const bcrypt = require('bcrypt');
  console.log(bcrypt.hashSync('admin123', 10));
"

# Update user in database
docker compose exec mongodb mongosh exlog --eval "
  db.users.updateOne(
    {username: 'admin'},
    {\$set: {password: 'hashed_password_here'}}
  )
"
```

**JWT Issues:**
```bash
# Generate new JWT secret
openssl rand -base64 32

# Update .env file
JWT_SECRET=your_new_secret_here

# Restart backend
docker compose restart backend
```

### 6. Log Ingestion Issues

#### Symptoms
- Logs not appearing in dashboard
- Agent connection failures
- Data not persisting

#### Diagnosis
```bash
# Check log ingestion endpoint
curl -X POST http://localhost:8080/api/v1/logs \
  -H "Content-Type: application/json" \
  -d '{"message":"test","level":"info","timestamp":"2024-01-01T00:00:00Z"}'

# Check agent logs
docker compose logs | grep -i agent

# Verify log storage
docker compose exec backend ls -la /app/logs/
```

#### Solutions

**Storage Issues:**
```bash
# Check volume mounts
docker volume ls
docker volume inspect dashboard_exlog_logs

# Fix permissions
docker compose exec backend chown -R node:node /app/logs
```

**Agent Configuration:**
```bash
# Verify agent configuration
cat agent/config.json

# Test agent connectivity
curl http://localhost:5000/api/v1/agents/heartbeat
```

### 7. SSL/TLS Issues

#### Symptoms
- Certificate errors
- "Not secure" warnings
- HTTPS not working

#### Solutions

**Self-Signed Certificates:**
```bash
# Generate self-signed certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/exlog.key \
  -out nginx/ssl/exlog.crt

# Update Nginx configuration
# Add SSL configuration to nginx.conf
```

**Let's Encrypt:**
```bash
# Install Certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d your-domain.com

# Update docker-compose.yml to mount certificates
volumes:
  - /etc/letsencrypt:/etc/letsencrypt:ro
```

## Advanced Troubleshooting

### Debug Mode

Enable debug logging:
```bash
# Update .env
LOG_LEVEL=debug
NODE_ENV=development

# Restart services
docker compose restart
```

### Container Shell Access

Access container for debugging:
```bash
# Backend container
docker compose exec backend bash

# MongoDB container
docker compose exec mongodb bash

# Frontend container
docker compose exec frontend sh
```

### Network Issues

Debug network connectivity:
```bash
# Check Docker networks
docker network ls
docker network inspect dashboard_exlog-network

# Test inter-container connectivity
docker compose exec frontend ping backend
docker compose exec backend ping mongodb
```

### Data Recovery

Backup and restore data:
```bash
# Backup MongoDB
docker compose exec mongodb mongodump --out /tmp/backup

# Restore MongoDB
docker compose exec mongodb mongorestore /tmp/backup

# Backup volumes
docker run --rm -v dashboard_mongodb_data:/data -v $(pwd):/backup alpine tar czf /backup/mongodb-backup.tar.gz -C /data .
```

## Getting Help

### Log Collection

Collect logs for support:
```bash
# Create support bundle
mkdir exlog-support-$(date +%Y%m%d)
cd exlog-support-$(date +%Y%m%d)

# Collect system info
docker info > docker-info.txt
docker compose ps > container-status.txt
docker compose logs > application-logs.txt

# Collect configuration
cp ../.env env-config.txt
cp ../docker-compose.yml docker-config.txt

# Create archive
cd ..
tar czf exlog-support-$(date +%Y%m%d).tar.gz exlog-support-$(date +%Y%m%d)/
```

### Support Channels

- **GitHub Issues**: https://gitlab.com/spr888/dashboard/-/issues
- **Documentation**: https://gitlab.com/spr888/dashboard/-/blob/main/README.md
- **Community Forum**: [Link to community forum]

### Before Contacting Support

1. Run the health check script: `./scripts/health-check.sh`
2. Collect relevant logs: `docker compose logs > logs.txt`
3. Note your environment details:
   - Operating system and version
   - Docker and Docker Compose versions
   - ExLog Dashboard version
   - Any custom configuration changes

### Emergency Procedures

**Complete Reset (⚠️ Data Loss!):**
```bash
# Stop all services
docker compose down

# Remove all data
docker volume prune -f
docker system prune -a -f

# Start fresh
docker compose up -d
```

**Rollback to Previous Version:**
```bash
# Stop current version
docker compose down

# Pull previous version
docker compose pull

# Start with previous images
docker compose up -d
```

## Prevention

### Regular Maintenance

```bash
# Weekly health checks
./scripts/health-check.sh

# Monthly cleanup
docker system prune
./scripts/backup-logs.sh --cleanup-only

# Quarterly updates
docker compose pull
docker compose up -d
```

### Monitoring Setup

Set up monitoring to prevent issues:
```bash
# Add to crontab
0 */6 * * * /path/to/exlog/scripts/health-check.sh --quiet || echo "ExLog health check failed" | mail -s "ExLog Alert" <EMAIL>
```

### Backup Strategy

Implement regular backups:
```bash
# Daily backups
0 2 * * * /path/to/exlog/scripts/backup-logs.sh

# Weekly full backup
0 3 * * 0 /path/to/exlog/scripts/backup-logs.sh --full
```

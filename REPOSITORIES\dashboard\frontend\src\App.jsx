import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, CircularProgress, Typography } from '@mui/material'
import { autoLogin } from './store/slices/authSlice'
import { fetchRoles, fetchPermissions } from './store/slices/usersSlice'
import Layout from './components/Layout/Layout'
import ProtectedRoute from './components/Auth/ProtectedRoute'
import Login from './pages/Auth/Login'
import Dashboard from './pages/Dashboard/Dashboard'
import Logs from './pages/Logs/Logs'
import Alerts from './pages/Alerts/Alerts'
import Agents from './pages/Agents/Agents'
import Users from './pages/Users/<USER>'
import Reports from './pages/Reports/Reports'
import Settings from './pages/Settings/Settings'
import AIInsights from './pages/AIInsights/AIInsights'

// Component to handle default view redirection
const DefaultViewRedirect = () => {
  const { user } = useSelector((state) => state.auth)
  const defaultView = user?.preferences?.dashboard?.defaultView || 'overview'

  // Map default view to route
  const getDefaultRoute = () => {
    switch (defaultView) {
      case 'logs':
        return '/logs'
      case 'alerts':
        return '/alerts'
      case 'agents':
        return '/agents'
      case 'overview':
      default:
        return '/dashboard'
    }
  }

  return <Navigate to={getDefaultRoute()} replace />
}

function App() {
  const dispatch = useDispatch()
  const { isAuthenticated, isCheckingAuth } = useSelector((state) => state.auth)

  useEffect(() => {
    // Try to auto-login on app startup
    dispatch(autoLogin())
  }, [dispatch])

  // Load roles and permissions when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchRoles())
      dispatch(fetchPermissions())
    }
  }, [dispatch, isAuthenticated])

  // Show loading spinner while checking authentication
  if (isCheckingAuth) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Checking authentication...
        </Typography>
      </Box>
    )
  }

  return (
    <Routes>
      <Route
        path="/login"
        element={
          isAuthenticated ? <Navigate to="/" replace /> : <Login />
        }
      />
      <Route
        path="/*"
        element={
          isAuthenticated ? (
            <Layout>
              <Routes>
                <Route path="/" element={
                  <ProtectedRoute>
                    <DefaultViewRedirect />
                  </ProtectedRoute>
                } />
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                <Route path="/logs" element={
                  <ProtectedRoute>
                    <Logs />
                  </ProtectedRoute>
                } />
                <Route path="/alerts" element={
                  <ProtectedRoute>
                    <Alerts />
                  </ProtectedRoute>
                } />
                <Route path="/agents" element={
                  <ProtectedRoute>
                    <Agents />
                  </ProtectedRoute>
                } />
                <Route path="/users" element={
                  <ProtectedRoute>
                    <Users />
                  </ProtectedRoute>
                } />
                <Route path="/reports" element={
                  <ProtectedRoute>
                    <Reports />
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                } />
                <Route path="/ai-insights/*" element={
                  <ProtectedRoute>
                    <AIInsights />
                  </ProtectedRoute>
                } />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Layout>
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />
    </Routes>
  )
}

export default App

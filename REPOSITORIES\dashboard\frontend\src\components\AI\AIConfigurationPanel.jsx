import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Alert,
  Chip,
  <PERSON>lider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Snackbar
} from '@mui/material';
import {
  ExpandMore,
  Save,
  Refresh,
  Schedule,
  Storage,
  Security,
  TrendingUp,
  Settings
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { aiService } from '../../services/api';

const AIConfigurationPanel = () => {
  const theme = useTheme();
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [schedulerStatus, setSchedulerStatus] = useState(null);

  useEffect(() => {
    loadConfiguration();
    loadSchedulerStatus();
  }, []);

  const loadConfiguration = async () => {
    try {
      setLoading(true);
      const response = await aiService.getConfig();
      setConfig(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to load AI configuration');
      console.error('Error loading AI config:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadSchedulerStatus = async () => {
    try {
      const response = await aiService.getSchedulerStatus();
      setSchedulerStatus(response.data);
    } catch (err) {
      console.error('Error loading scheduler status:', err);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await aiService.updateConfig(config);
      setSuccess(true);
      setError(null);
      // Reload to get updated scheduling info
      await loadConfiguration();
      await loadSchedulerStatus();
    } catch (err) {
      setError('Failed to save configuration');
      console.error('Error saving AI config:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleConfigChange = (section, field, value) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedConfigChange = (section, subsection, field, value) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
  };

  const getIntervalLabel = (seconds) => {
    if (seconds < 3600) {
      return `${Math.floor(seconds / 60)} minutes`;
    } else if (seconds < 86400) {
      return `${Math.floor(seconds / 3600)} hours`;
    } else {
      return `${Math.floor(seconds / 86400)} days`;
    }
  };

  const getRiskLevelColor = (level) => {
    switch (level) {
      case 'low': return theme.palette.success.main;
      case 'medium': return theme.palette.warning.main;
      case 'high': return theme.palette.error.main;
      case 'critical': return theme.palette.error.dark;
      default: return theme.palette.grey[500];
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error && !config) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={loadConfiguration}>
          Retry
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Card>
        <CardHeader
          title="AI Analysis Configuration"
          subheader="Configure automated AI analysis settings and scheduling"
          action={
            <Box>
              <Button
                startIcon={<Refresh />}
                onClick={loadConfiguration}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Save />}
                onClick={handleSave}
                disabled={saving || !config}
              >
                {saving ? 'Saving...' : 'Save Configuration'}
              </Button>
            </Box>
          }
        />
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Scheduler Status */}
          {schedulerStatus && (
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <Schedule sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Scheduler Status
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="textSecondary">
                      Status
                    </Typography>
                    <Chip
                      label={schedulerStatus.initialized ? 'Active' : 'Inactive'}
                      color={schedulerStatus.initialized ? 'success' : 'error'}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="textSecondary">
                      Scheduled Jobs
                    </Typography>
                    <Typography variant="body1">
                      {schedulerStatus.scheduledJobs}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="textSecondary">
                      Running Analyses
                    </Typography>
                    <Typography variant="body1">
                      {schedulerStatus.runningAnalyses}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="textSecondary">
                      Next Run
                    </Typography>
                    <Typography variant="body1">
                      {config?.scheduling?.nextRun 
                        ? (() => {
                            try {
                              const date = new Date(config.scheduling.nextRun)
                              return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleString()
                            } catch (error) {
                              return 'Invalid Date'
                            }
                          })()
                        : 'Not scheduled'
                      }
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}

          {config && (
            <Box>
              {/* Analysis Settings */}
              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography variant="h6">
                    <Settings sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Analysis Settings
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={config.analysisSettings?.autoScheduling?.enabled || false}
                            onChange={(e) => handleNestedConfigChange('analysisSettings', 'autoScheduling', 'enabled', e.target.checked)}
                          />
                        }
                        label="Enable Automated Analysis"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography gutterBottom>
                        Analysis Interval: {getIntervalLabel(config.analysisSettings?.interval || 300)}
                      </Typography>
                      <Slider
                        value={config.analysisSettings?.interval || 300}
                        onChange={(e, value) => handleConfigChange('analysisSettings', 'interval', value)}
                        min={60}
                        max={86400}
                        step={60}
                        marks={[
                          { value: 300, label: '5m' },
                          { value: 1800, label: '30m' },
                          { value: 3600, label: '1h' },
                          { value: 21600, label: '6h' },
                          { value: 86400, label: '24h' }
                        ]}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Time Range for Analysis</InputLabel>
                        <Select
                          value={config.analysisSettings?.timeRange || '1h'}
                          onChange={(e) => handleConfigChange('analysisSettings', 'timeRange', e.target.value)}
                        >
                          <MenuItem value="15m">Last 15 minutes</MenuItem>
                          <MenuItem value="30m">Last 30 minutes</MenuItem>
                          <MenuItem value="1h">Last 1 hour</MenuItem>
                          <MenuItem value="2h">Last 2 hours</MenuItem>
                          <MenuItem value="6h">Last 6 hours</MenuItem>
                          <MenuItem value="12h">Last 12 hours</MenuItem>
                          <MenuItem value="24h">Last 24 hours</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Max Logs Per Analysis"
                        type="number"
                        value={config.analysisSettings?.maxLogsPerAnalysis || 10000}
                        onChange={(e) => handleConfigChange('analysisSettings', 'maxLogsPerAnalysis', parseInt(e.target.value))}
                        inputProps={{ min: 100, max: 100000 }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Max Concurrent Runs"
                        type="number"
                        value={config.analysisSettings?.autoScheduling?.maxConcurrentRuns || 1}
                        onChange={(e) => handleNestedConfigChange('analysisSettings', 'autoScheduling', 'maxConcurrentRuns', parseInt(e.target.value))}
                        inputProps={{ min: 1, max: 5 }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={config.analysisSettings?.autoScheduling?.skipIfRunning || true}
                            onChange={(e) => handleNestedConfigChange('analysisSettings', 'autoScheduling', 'skipIfRunning', e.target.checked)}
                          />
                        }
                        label="Skip if Analysis Already Running"
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>

              {/* Data Retention Settings */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography variant="h6">
                    <Storage sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Data Retention
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Analysis Results Retention (days)"
                        type="number"
                        value={config.dataRetention?.analysisResultsRetentionDays || 180}
                        onChange={(e) => handleConfigChange('dataRetention', 'analysisResultsRetentionDays', parseInt(e.target.value))}
                        inputProps={{ min: 30, max: 730 }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Insights Retention (days)"
                        type="number"
                        value={config.dataRetention?.insightsRetentionDays || 90}
                        onChange={(e) => handleConfigChange('dataRetention', 'insightsRetentionDays', parseInt(e.target.value))}
                        inputProps={{ min: 7, max: 365 }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Feedback Retention (days)"
                        type="number"
                        value={config.dataRetention?.feedbackRetentionDays || 365}
                        onChange={(e) => handleConfigChange('dataRetention', 'feedbackRetentionDays', parseInt(e.target.value))}
                        inputProps={{ min: 30, max: 1095 }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={config.dataRetention?.autoCleanup?.enabled || true}
                            onChange={(e) => handleNestedConfigChange('dataRetention', 'autoCleanup', 'enabled', e.target.checked)}
                          />
                        }
                        label="Enable Automatic Cleanup"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={config.dataRetention?.compressionSettings?.enabled || true}
                            onChange={(e) => handleNestedConfigChange('dataRetention', 'compressionSettings', 'enabled', e.target.checked)}
                          />
                        }
                        label="Enable Data Compression"
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Box>
          )}
        </CardContent>
      </Card>

      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
      >
        <Alert onClose={() => setSuccess(false)} severity="success">
          Configuration saved successfully!
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AIConfigurationPanel;

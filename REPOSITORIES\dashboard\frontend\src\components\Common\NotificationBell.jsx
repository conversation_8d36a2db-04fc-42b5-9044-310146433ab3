import React, { useState, useEffect, useCallback } from 'react'
import {
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  CircularProgress,
  Tooltip,
  ListItemSecondaryAction,
  Fade,
  Slide,
  Checkbox,
  FormControlLabel,
  Select,
  FormControl,
  InputLabel,
} from '@mui/material'
import {
  Notifications as NotificationsIcon,
  NotificationsNone as NotificationsNoneIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Delete as DeleteIcon,
  MarkEmailRead as MarkReadIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  SelectAll as SelectAllIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'
import { useSelector } from 'react-redux'
import { useNotifications } from '../../contexts/NotificationContext'
import notificationApi from '../../services/notificationApi'
import websocketService from '../../services/websocketService'

const NotificationBell = () => {
  const [anchorEl, setAnchorEl] = useState(null)
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const [selectedNotifications, setSelectedNotifications] = useState(new Set())
  const [filterType, setFilterType] = useState('all')
  const [filterCategory, setFilterCategory] = useState('all')
  const [showFilters, setShowFilters] = useState(false)
  const { user } = useSelector((state) => state.auth)
  const { inAppNotifications, showNotification, shouldShowNotification, notificationPreferences } = useNotifications()

  // Load notifications from API
  const loadNotifications = useCallback(async () => {
    if (!inAppNotifications || !user) return;

    try {
      setLoading(true);

      // Build filter parameters
      const filterParams = { limit: 20, page: 1 };
      if (filterType !== 'all') {
        if (filterType === 'unread') {
          filterParams.read = false;
        } else {
          filterParams.type = filterType;
        }
      }
      if (filterCategory !== 'all') {
        filterParams.category = filterCategory;
      }

      const [notificationsResult, unreadCountResult] = await Promise.all([
        notificationApi.getNotifications(filterParams),
        notificationApi.getUnreadCount(),
      ]);

      const formattedNotifications = notificationsResult.data.notifications.map(notification =>
        notificationApi.formatNotification(notification)
      );

      setNotifications(formattedNotifications);
      setUnreadCount(unreadCountResult);
      setSelectedNotifications(new Set()); // Clear selections when reloading
    } catch (error) {
      console.error('Error loading notifications:', error);
      showNotification('Failed to load notifications', 'error');
    } finally {
      setLoading(false);
    }
  }, [inAppNotifications, user, showNotification, filterType, filterCategory]);

  // Initialize notifications and WebSocket
  useEffect(() => {
    if (user && inAppNotifications) {
      loadNotifications();

      // Connect to WebSocket if not already connected
      const token = localStorage.getItem('token');
      if (token && !websocketService.getStatus().isConnected) {
        websocketService.connect(token).catch(error => {
          console.error('Failed to connect to WebSocket:', error);
        });
      }

      // Listen for new notifications
      const handleNewNotification = (notification) => {
        // Check if notification should be shown based on user preferences
        const notificationOptions = {
          category: notification.category,
          severity: notification.severity,
        };

        if (!shouldShowNotification(notificationOptions)) {
          return;
        }

        const formattedNotification = notificationApi.formatNotification(notification);
        setNotifications(prev => [formattedNotification, ...prev.slice(0, 19)]);
        setUnreadCount(prev => prev + 1);

        // Play notification sound for important notifications
        if (notification.severity === 'critical' || notification.severity === 'high') {
          try {
            // Create a simple notification sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
          } catch (error) {
            // Ignore audio errors
            console.debug('Could not play notification sound:', error);
          }
        }

        // Show snackbar notification
        showNotification(notification.message, notification.type, {
          duration: 5000,
          category: notification.category,
          severity: notification.severity,
        });
      };

      websocketService.on('notification', handleNewNotification);

      return () => {
        websocketService.off('notification', handleNewNotification);
      };
    }
  }, [user, inAppNotifications, loadNotifications, showNotification, shouldShowNotification]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleNotificationClick = async (notification) => {
    if (!notification.read) {
      try {
        await notificationApi.markNotificationAsRead(notification._id);
        websocketService.markNotificationRead(notification._id);

        // Update local state
        setNotifications(prev =>
          prev.map(n =>
            n._id === notification._id
              ? { ...n, read: true, readAt: new Date() }
              : n
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      } catch (error) {
        console.error('Error marking notification as read:', error);
        showNotification('Failed to mark notification as read', 'error');
      }
    }

    // Navigate to notification source if action exists
    if (notification.actions && notification.actions.length > 0) {
      const action = notification.actions[0];
      if (action.url) {
        window.location.href = action.url;
      }
    }
  }

  const handleDeleteNotification = async (notificationId, event) => {
    event.stopPropagation();

    try {
      await notificationApi.deleteNotification(notificationId);

      // Update local state
      const deletedNotification = notifications.find(n => n._id === notificationId);
      setNotifications(prev => prev.filter(n => n._id !== notificationId));

      if (deletedNotification && !deletedNotification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

      showNotification('Notification deleted', 'success');
    } catch (error) {
      console.error('Error deleting notification:', error);
      showNotification('Failed to delete notification', 'error');
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      await notificationApi.markAllAsRead();

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({
          ...notification,
          read: true,
          readAt: new Date(),
        }))
      );
      setUnreadCount(0);

      showNotification('All notifications marked as read', 'success');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      showNotification('Failed to mark all notifications as read', 'error');
    }
  }

  const handleRefresh = () => {
    loadNotifications();
  }

  const handleSelectNotification = (notificationId, checked) => {
    setSelectedNotifications(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(notificationId);
      } else {
        newSet.delete(notificationId);
      }
      return newSet;
    });
  }

  const handleSelectAll = () => {
    if (selectedNotifications.size === notifications.length) {
      setSelectedNotifications(new Set());
    } else {
      setSelectedNotifications(new Set(notifications.map(n => n._id)));
    }
  }

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.size === 0) return;

    try {
      const notificationIds = Array.from(selectedNotifications);
      await notificationApi.markAsRead(notificationIds);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          selectedNotifications.has(notification._id)
            ? { ...notification, read: true, readAt: new Date() }
            : notification
        )
      );

      // Update unread count
      const unreadSelected = notifications.filter(n =>
        selectedNotifications.has(n._id) && !n.read
      ).length;
      setUnreadCount(prev => Math.max(0, prev - unreadSelected));

      setSelectedNotifications(new Set());
      showNotification(`${notificationIds.length} notifications marked as read`, 'success');
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      showNotification('Failed to mark notifications as read', 'error');
    }
  }

  const handleBulkDelete = async () => {
    if (selectedNotifications.size === 0) return;

    try {
      const notificationIds = Array.from(selectedNotifications);
      await notificationApi.deleteNotifications(notificationIds);

      // Update local state
      const deletedUnreadCount = notifications.filter(n =>
        selectedNotifications.has(n._id) && !n.read
      ).length;

      setNotifications(prev =>
        prev.filter(notification => !selectedNotifications.has(notification._id))
      );
      setUnreadCount(prev => Math.max(0, prev - deletedUnreadCount));
      setSelectedNotifications(new Set());

      showNotification(`${notificationIds.length} notifications deleted`, 'success');
    } catch (error) {
      console.error('Error deleting notifications:', error);
      showNotification('Failed to delete notifications', 'error');
    }
  }

  const handleFilterChange = (type, value) => {
    if (type === 'type') {
      setFilterType(value);
    } else if (type === 'category') {
      setFilterCategory(value);
    }
  }

  const getIcon = (type) => {
    switch (type) {
      case 'error':
        return <ErrorIcon color="error" />
      case 'warning':
        return <WarningIcon color="warning" />
      case 'success':
        return <SuccessIcon color="success" />
      case 'info':
      default:
        return <InfoIcon color="info" />
    }
  }

  const getSeverityColor = (type) => {
    switch (type) {
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'success':
        return 'success'
      case 'info':
      default:
        return 'info'
    }
  }

  const formatTime = (timestamp) => {
    return notificationApi.getTimeAgo(timestamp);
  }

  if (!inAppNotifications) {
    return null
  }

  return (
    <>
      <Tooltip title={`${unreadCount} unread notifications`}>
        <IconButton
          color="inherit"
          onClick={handleClick}
          sx={{
            mr: 1,
            animation: unreadCount > 0 ? 'pulse 2s infinite' : 'none',
            '@keyframes pulse': {
              '0%': {
                transform: 'scale(1)',
              },
              '50%': {
                transform: 'scale(1.05)',
              },
              '100%': {
                transform: 'scale(1)',
              },
            },
          }}
        >
          <Badge
            badgeContent={unreadCount}
            color="error"
            sx={{
              '& .MuiBadge-badge': {
                animation: unreadCount > 0 ? 'bounce 1s ease-in-out' : 'none',
                '@keyframes bounce': {
                  '0%, 20%, 53%, 80%, 100%': {
                    transform: 'translate3d(0,0,0)',
                  },
                  '40%, 43%': {
                    transform: 'translate3d(0, -8px, 0)',
                  },
                  '70%': {
                    transform: 'translate3d(0, -4px, 0)',
                  },
                  '90%': {
                    transform: 'translate3d(0, -2px, 0)',
                  },
                },
              },
            }}
          >
            {unreadCount > 0 ? <NotificationsIcon /> : <NotificationsNoneIcon />}
          </Badge>
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 360,
            maxHeight: 400,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              Notifications
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Filters">
                <IconButton
                  size="small"
                  onClick={() => setShowFilters(!showFilters)}
                  color={showFilters ? 'primary' : 'default'}
                >
                  <FilterIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton size="small" onClick={handleRefresh} disabled={loading}>
                  {loading ? <CircularProgress size={16} /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
              {unreadCount > 0 && (
                <Tooltip title="Mark all as read">
                  <IconButton size="small" onClick={handleMarkAllAsRead}>
                    <MarkReadIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>

          {/* Filters */}
          <Slide direction="down" in={showFilters} mountOnEnter unmountOnExit>
            <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
              <FormControl size="small" sx={{ minWidth: 100 }}>
                <InputLabel>Type</InputLabel>
                <Select
                  value={filterType}
                  label="Type"
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="unread">Unread</MenuItem>
                  <MenuItem value="info">Info</MenuItem>
                  <MenuItem value="success">Success</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="error">Error</MenuItem>
                </Select>
              </FormControl>
              <FormControl size="small" sx={{ minWidth: 100 }}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filterCategory}
                  label="Category"
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="alert">Alerts</MenuItem>
                  <MenuItem value="agent">Agents</MenuItem>
                  <MenuItem value="system">System</MenuItem>
                  <MenuItem value="report">Reports</MenuItem>
                  <MenuItem value="security">Security</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Slide>

          {/* Bulk Actions */}
          {selectedNotifications.size > 0 && (
            <Fade in={true}>
              <Box sx={{ mt: 2, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">
                    {selectedNotifications.size} selected
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Mark selected as read">
                      <IconButton size="small" onClick={handleBulkMarkAsRead}>
                        <MarkReadIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete selected">
                      <IconButton size="small" onClick={handleBulkDelete} color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Clear selection">
                      <IconButton size="small" onClick={() => setSelectedNotifications(new Set())}>
                        <ClearIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Box>
            </Fade>
          )}

          {unreadCount > 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {unreadCount} unread
            </Typography>
          )}
        </Box>
        <Divider />

        {loading && notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <CircularProgress size={24} />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Loading notifications...
            </Typography>
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No notifications
            </Typography>
          </Box>
        ) : (
          <>
            {notifications.length > 0 && (
              <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      size="small"
                      checked={selectedNotifications.size === notifications.length && notifications.length > 0}
                      indeterminate={selectedNotifications.size > 0 && selectedNotifications.size < notifications.length}
                      onChange={handleSelectAll}
                    />
                  }
                  label={
                    <Typography variant="body2">
                      Select all ({notifications.length})
                    </Typography>
                  }
                />
              </Box>
            )}
            <List sx={{ p: 0, maxHeight: 300, overflow: 'auto' }}>
              {notifications.map((notification) => (
                <ListItem
                  key={notification._id}
                  button
                  onClick={(e) => {
                    // Don't trigger if clicking on checkbox or delete button
                    if (e.target.type === 'checkbox' || e.target.closest('button')) {
                      return;
                    }
                    handleNotificationClick(notification);
                  }}
                  sx={{
                    backgroundColor: notification.read ? 'transparent' : 'action.hover',
                    '&:hover': {
                      backgroundColor: 'action.selected',
                    },
                    position: 'relative',
                    pl: 1,
                  }}
                >
                  <Checkbox
                    size="small"
                    checked={selectedNotifications.has(notification._id)}
                    onChange={(e) => handleSelectNotification(notification._id, e.target.checked)}
                    onClick={(e) => e.stopPropagation()}
                    sx={{ mr: 1 }}
                  />
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    {getIcon(notification.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          variant="subtitle2"
                          sx={{
                            flexGrow: 1,
                            fontWeight: notification.read ? 'normal' : 'bold',
                          }}
                        >
                          {notification.title}
                        </Typography>
                        {notification.category && (
                          <Chip
                            label={notification.category}
                            size="small"
                            color={getSeverityColor(notification.type)}
                            variant="outlined"
                            sx={{
                              textTransform: 'capitalize',
                              fontSize: '0.7rem',
                            }}
                          />
                        )}
                        {notification.isRecent && (
                          <Chip
                            label="New"
                            size="small"
                            color="primary"
                            sx={{
                              fontSize: '0.6rem',
                              height: 16,
                              animation: 'glow 1.5s ease-in-out infinite alternate',
                              '@keyframes glow': {
                                from: { opacity: 0.7 },
                                to: { opacity: 1 },
                              },
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            fontWeight: notification.read ? 'normal' : 'medium',
                          }}
                        >
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTime(notification.createdAt || notification.timestamp)}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Delete notification">
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => handleDeleteNotification(notification._id, e)}
                        sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </>
        )}

        <Divider />
        <Box sx={{ p: 1 }}>
          <MenuItem onClick={handleClose} sx={{ justifyContent: 'center' }}>
            <Typography variant="body2" color="primary">
              View All Notifications
            </Typography>
          </MenuItem>
        </Box>
      </Menu>
    </>
  )
}

export default NotificationBell

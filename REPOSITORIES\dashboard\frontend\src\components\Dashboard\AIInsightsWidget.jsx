import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  IconButton,
  Tooltip,
  Divider,
  Button,
  CircularProgress,
  LinearProgress,
  Alert
} from '@mui/material'
import {
  Psychology,
  Security,
  Warning,
  Error,
  TrendingUp,
  TrendingDown,
  Refresh,
  OpenInNew,
  Shield,
  BugReport,
  Visibility,
  Settings,
  Schedule,
  CheckCircle,
  Info
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { formatDistanceToNow } from 'date-fns'
import { useNavigate } from 'react-router-dom'
import useCompactMode from '../../hooks/useCompactMode'
import websocketService from '../../services/websocketService'

const AIInsightsWidget = ({ aiData, isLoading, onRefresh }) => {
  const theme = useTheme()
  const navigate = useNavigate()
  const { getCompactStyles, typography, sizes } = useCompactMode()
  const [threatLevel, setThreatLevel] = useState('low')
  const [recentAnomalies, setRecentAnomalies] = useState([])
  const [threatTrend, setThreatTrend] = useState('stable')
  const [realtimeUpdates, setRealtimeUpdates] = useState([])
  const [analysisRunning, setAnalysisRunning] = useState(false)
  const [lastAutomatedAnalysis, setLastAutomatedAnalysis] = useState(null)

  useEffect(() => {
    if (aiData) {
      setThreatLevel(aiData.summary?.riskLevel || 'low')
      setRecentAnomalies(aiData.anomalies?.slice(0, 3) || [])

      // Determine threat trend based on recent data
      const currentThreats = aiData.summary?.threats || 0
      const previousThreats = aiData.previousSummary?.threats || 0
      if (currentThreats > previousThreats) {
        setThreatTrend('increasing')
      } else if (currentThreats < previousThreats) {
        setThreatTrend('decreasing')
      } else {
        setThreatTrend('stable')
      }
    }
  }, [aiData])

  // WebSocket listeners for real-time updates
  useEffect(() => {
    // Subscribe to AI updates when component mounts
    websocketService.subscribeToAIUpdates()

    // Analysis started handler
    const handleAnalysisStarted = (data) => {
      if (data.automated) {
        setAnalysisRunning(true)
        setRealtimeUpdates(prev => [...prev, {
          id: Date.now(),
          type: 'info',
          message: `Automated analysis started (${data.type})`,
          timestamp: new Date(),
          analysisId: data.analysisId
        }])
      }
    }

    // Analysis completed handler
    const handleAnalysisComplete = (data) => {
      if (data.automated) {
        setAnalysisRunning(false)
        setLastAutomatedAnalysis(data)

        // Update threat level if this is a new automated analysis
        if (data.summary?.riskLevel) {
          setThreatLevel(data.summary.riskLevel)
        }

        setRealtimeUpdates(prev => [...prev, {
          id: Date.now(),
          type: 'success',
          message: `Automated analysis completed - ${data.summary?.anomalies || 0} anomalies, ${data.summary?.threats || 0} threats detected`,
          timestamp: new Date(),
          analysisId: data.analysisId,
          summary: data.summary
        }])

        // Trigger refresh to get latest data
        if (onRefresh) {
          setTimeout(onRefresh, 1000) // Small delay to ensure data is saved
        }
      }
    }

    // Analysis failed handler
    const handleAnalysisFailed = (data) => {
      if (data.automated) {
        setAnalysisRunning(false)
        setRealtimeUpdates(prev => [...prev, {
          id: Date.now(),
          type: 'error',
          message: `Automated analysis failed: ${data.error}`,
          timestamp: new Date(),
          analysisId: data.analysisId
        }])
      }
    }

    // High risk alert handler
    const handleHighRiskAlert = (data) => {
      if (data.automated) {
        setRealtimeUpdates(prev => [...prev, {
          id: Date.now(),
          type: 'warning',
          message: `High risk detected! ${data.summary.threats} threats, ${data.summary.anomalies} anomalies`,
          timestamp: new Date(),
          analysisId: data.analysisId,
          riskLevel: data.riskLevel
        }])
      }
    }

    // Register event listeners
    websocketService.on('ai:analysis-started', handleAnalysisStarted)
    websocketService.on('ai:analysis-complete', handleAnalysisComplete)
    websocketService.on('ai:analysis-failed', handleAnalysisFailed)
    websocketService.on('ai:high-risk-alert', handleHighRiskAlert)

    // Cleanup on unmount
    return () => {
      websocketService.off('ai:analysis-started', handleAnalysisStarted)
      websocketService.off('ai:analysis-complete', handleAnalysisComplete)
      websocketService.off('ai:analysis-failed', handleAnalysisFailed)
      websocketService.off('ai:high-risk-alert', handleHighRiskAlert)
      websocketService.unsubscribeFromAIUpdates()
    }
  }, [onRefresh])

  // Clean up old realtime updates (keep only last 5)
  useEffect(() => {
    if (realtimeUpdates.length > 5) {
      setRealtimeUpdates(prev => prev.slice(-5))
    }
  }, [realtimeUpdates])

  const getThreatLevelColor = (level) => {
    const colors = {
      low: theme.palette.success.main,
      medium: theme.palette.warning.main,
      high: theme.palette.error.main,
      critical: theme.palette.error.dark
    }
    return colors[level] || colors.low
  }

  const getThreatLevelIcon = (level) => {
    switch (level) {
      case 'critical':
        return <Error sx={{ color: getThreatLevelColor(level) }} />
      case 'high':
        return <Warning sx={{ color: getThreatLevelColor(level) }} />
      case 'medium':
        return <Security sx={{ color: getThreatLevelColor(level) }} />
      default:
        return <Shield sx={{ color: getThreatLevelColor(level) }} />
    }
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp sx={{ color: theme.palette.error.main }} />
      case 'decreasing':
        return <TrendingDown sx={{ color: theme.palette.success.main }} />
      default:
        return null
    }
  }

  const handleViewDetails = () => {
    navigate('/ai-insights')
  }

  const handleConfigure = () => {
    navigate('/ai-insights/config')
  }

  if (isLoading) {
    return (
      <Card sx={getCompactStyles('card')}>
        <CardContent>
          <Typography variant={typography.sectionTitle} gutterBottom>
            🤖 AI Security Insights
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 200 }}>
            <CircularProgress size={40} />
          </Box>
        </CardContent>
      </Card>
    )
  }

  if (!aiData) {
    return (
      <Card sx={getCompactStyles('card')}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant={typography.sectionTitle}>
              🤖 AI Security Insights
            </Typography>
            <Tooltip title="Refresh AI insights">
              <IconButton onClick={onRefresh} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
          <Alert severity="info" sx={{ mt: 2 }}>
            AI insights are currently unavailable. The AI service may be starting up.
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card sx={getCompactStyles('card')}>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant={typography.sectionTitle}>
              🤖 AI Security Insights
            </Typography>
            {analysisRunning && (
              <Tooltip title="Automated analysis running">
                <Chip
                  icon={<CircularProgress size={12} />}
                  label="Auto"
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ height: 20, fontSize: '0.7rem' }}
                />
              </Tooltip>
            )}
            {lastAutomatedAnalysis && !analysisRunning && (
              <Tooltip title={`Last automated analysis: ${formatDistanceToNow(new Date(lastAutomatedAnalysis.timestamp || Date.now()), { addSuffix: true })}`}>
                <Chip
                  icon={<Schedule sx={{ fontSize: 12 }} />}
                  label="Auto"
                  size="small"
                  color="success"
                  variant="outlined"
                  sx={{ height: 20, fontSize: '0.7rem' }}
                />
              </Tooltip>
            )}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh AI insights">
              <IconButton onClick={onRefresh} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Tooltip title="Configure AI settings">
              <IconButton onClick={handleConfigure} size="small">
                <Settings />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Threat Level Indicator */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
            <Avatar sx={{
              bgcolor: getThreatLevelColor(threatLevel),
              width: sizes.avatar,
              height: sizes.avatar
            }}>
              {getThreatLevelIcon(threatLevel)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant={typography.cardTitle} sx={{ fontWeight: 'bold' }}>
                Threat Level: {threatLevel.toUpperCase()}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant={typography.caption} color="text.secondary">
                  {aiData.summary?.totalLogs || 0} logs analyzed
                </Typography>
                {getTrendIcon(threatTrend)}
              </Box>
            </Box>
          </Box>

          {/* Risk Level Progress Bar */}
          <LinearProgress
            variant="determinate"
            value={threatLevel === 'critical' ? 100 : threatLevel === 'high' ? 75 : threatLevel === 'medium' ? 50 : 25}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                backgroundColor: getThreatLevelColor(threatLevel),
                borderRadius: 3
              }
            }}
          />
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Recent Anomalies */}
        <Box sx={{ mb: 2 }}>
          <Typography variant={typography.cardTitle} gutterBottom>
            Recent Anomalies
          </Typography>

          {recentAnomalies.length === 0 ? (
            <Typography variant={typography.caption} color="text.secondary" sx={{ fontStyle: 'italic' }}>
              No recent anomalies detected
            </Typography>
          ) : (
            <List dense sx={{ py: 0 }}>
              {recentAnomalies.map((anomaly, index) => (
                <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <BugReport
                      sx={{
                        fontSize: sizes.icon,
                        color: getThreatLevelColor(anomaly.riskLevel)
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant={typography.caption} sx={{ fontWeight: 500 }}>
                        {anomaly.description || 'Anomalous behavior detected'}
                      </Typography>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <Chip
                          label={anomaly.riskLevel}
                          size="small"
                          sx={{
                            height: 16,
                            fontSize: '0.65rem',
                            backgroundColor: getThreatLevelColor(anomaly.riskLevel),
                            color: 'white'
                          }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {anomaly.timestamp ? formatDistanceToNow(new Date(anomaly.timestamp), { addSuffix: true }) : 'Recently'}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Summary Stats */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant={typography.cardTitle} sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
              {aiData.summary?.anomalies || 0}
            </Typography>
            <Typography variant={typography.caption} color="text.secondary">
              Anomalies
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant={typography.cardTitle} sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
              {aiData.summary?.threats || 0}
            </Typography>
            <Typography variant={typography.caption} color="text.secondary">
              Threats
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant={typography.cardTitle} sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
              {aiData.summary?.patterns || 0}
            </Typography>
            <Typography variant={typography.caption} color="text.secondary">
              Patterns
            </Typography>
          </Box>
        </Box>

        {/* Real-time Updates */}
        {(analysisRunning || realtimeUpdates.length > 0) && (
          <Box sx={{ mt: 2, mb: 2 }}>
            <Typography variant={typography.sectionTitle} sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Psychology sx={{ fontSize: 16 }} />
              Live Analysis Updates
              {analysisRunning && (
                <CircularProgress size={12} sx={{ ml: 1 }} />
              )}
            </Typography>

            {analysisRunning && (
              <Alert severity="info" sx={{ mb: 1, py: 0.5 }}>
                <Typography variant="caption">
                  Automated analysis in progress...
                </Typography>
              </Alert>
            )}

            {realtimeUpdates.length > 0 && (
              <List dense sx={{ maxHeight: 120, overflow: 'auto' }}>
                {realtimeUpdates.slice(-3).reverse().map((update) => (
                  <ListItem key={update.id} sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 24 }}>
                      {update.type === 'success' && <CheckCircle sx={{ fontSize: 16, color: theme.palette.success.main }} />}
                      {update.type === 'error' && <Error sx={{ fontSize: 16, color: theme.palette.error.main }} />}
                      {update.type === 'warning' && <Warning sx={{ fontSize: 16, color: theme.palette.warning.main }} />}
                      {update.type === 'info' && <Info sx={{ fontSize: 16, color: theme.palette.info.main }} />}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                          {update.message}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                          {formatDistanceToNow(update.timestamp, { addSuffix: true })}
                        </Typography>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<Visibility />}
            onClick={handleViewDetails}
            sx={{ flex: 1 }}
          >
            View Details
          </Button>
          <Button
            variant="text"
            size="small"
            endIcon={<OpenInNew />}
            onClick={handleViewDetails}
            sx={{ minWidth: 'auto' }}
          >
            Full Report
          </Button>
        </Box>

        {/* Last Updated */}
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mt: 1 }}>
          Last updated: {aiData.timestamp ? formatDistanceToNow(new Date(aiData.timestamp), { addSuffix: true }) : 'Unknown'}
        </Typography>
      </CardContent>
    </Card>
  )
}

export default AIInsightsWidget

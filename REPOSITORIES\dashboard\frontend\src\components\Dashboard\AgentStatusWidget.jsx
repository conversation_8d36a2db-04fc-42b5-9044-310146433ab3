import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material'
import {
  Computer,
  CheckCircle,
  Error,
  Warning,
  OfflinePin,
  Refresh,
  MoreVert
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import useCompactMode from '../../hooks/useCompactMode'

const AgentStatusWidget = ({ agentData, isLoading, onRefresh }) => {
  const theme = useTheme()
  const { getCompactStyles, typography, sizes } = useCompactMode()

  if (isLoading) {
    return (
      <Card sx={getCompactStyles('card')}>
        <CardContent>
          <Typography variant={typography.sectionTitle} gutterBottom>
            Agent Status
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 200 }}>
            <Typography color="text.secondary">Loading agent data...</Typography>
          </Box>
        </CardContent>
      </Card>
    )
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online':
        return <CheckCircle color="success" fontSize={sizes.iconSize} />
      case 'offline':
        return <OfflinePin color="disabled" fontSize={sizes.iconSize} />
      case 'warning':
        return <Warning color="warning" fontSize={sizes.iconSize} />
      case 'error':
        return <Error color="error" fontSize={sizes.iconSize} />
      default:
        return <Computer color="disabled" fontSize={sizes.iconSize} />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'online':
        return 'success'
      case 'offline':
        return 'default'
      case 'warning':
        return 'warning'
      case 'error':
        return 'error'
      default:
        return 'default'
    }
  }

  const totalAgents = agentData?.summary?.total || 0
  const onlineAgents = agentData?.summary?.online || 0
  const offlineAgents = agentData?.summary?.offline || 0
  const warningAgents = agentData?.summary?.warning || 0
  const errorAgents = agentData?.summary?.error || 0

  const healthPercentage = totalAgents > 0 ? Math.round((onlineAgents / totalAgents) * 100) : 0

  return (
    <Card sx={getCompactStyles('card')}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant={typography.sectionTitle}>
            Agent Status
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Refresh agent data">
              <IconButton size="small" onClick={onRefresh}>
                <Refresh fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Summary Stats */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant={typography.body} color="text.secondary">
              Overall Health
            </Typography>
            <Typography variant={typography.body} fontWeight="bold">
              {healthPercentage}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={healthPercentage}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                backgroundColor: healthPercentage > 80 ? theme.palette.success.main :
                                healthPercentage > 60 ? theme.palette.warning.main :
                                theme.palette.error.main
              }
            }}
          />
        </Box>

        {/* Status Breakdown */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
          <Chip
            icon={<CheckCircle />}
            label={`${onlineAgents} Online`}
            color="success"
            size="small"
            variant="outlined"
          />
          <Chip
            icon={<OfflinePin />}
            label={`${offlineAgents} Offline`}
            color="default"
            size="small"
            variant="outlined"
          />
          {warningAgents > 0 && (
            <Chip
              icon={<Warning />}
              label={`${warningAgents} Warning`}
              color="warning"
              size="small"
              variant="outlined"
            />
          )}
          {errorAgents > 0 && (
            <Chip
              icon={<Error />}
              label={`${errorAgents} Error`}
              color="error"
              size="small"
              variant="outlined"
            />
          )}
        </Box>

        {/* Recent Agents */}
        {agentData?.recentAgents && agentData.recentAgents.length > 0 && (
          <>
            <Typography variant={typography.body} color="text.secondary" gutterBottom>
              Recent Activity
            </Typography>
            <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
              {agentData.recentAgents.slice(0, 5).map((agent, index) => (
                <React.Fragment key={agent.agentId}>
                  <ListItem
                    sx={{ px: 0 }}
                    secondaryAction={
                      <Tooltip title="View details">
                        <IconButton size="small">
                          <MoreVert fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                        {getStatusIcon(agent.status)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography variant={typography.body} noWrap>
                          {agent.hostname}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={agent.status}
                            color={getStatusColor(agent.status)}
                            size="small"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {agent.platform}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < agentData.recentAgents.slice(0, 5).length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </>
        )}

        {/* No agents message */}
        {totalAgents === 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
            <Computer sx={{ fontSize: 48, color: theme.palette.grey[400], mb: 1 }} />
            <Typography variant={typography.body} color="text.secondary" align="center">
              No agents registered
            </Typography>
            <Typography variant="caption" color="text.secondary" align="center">
              Deploy agents to start monitoring
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default AgentStatusWidget

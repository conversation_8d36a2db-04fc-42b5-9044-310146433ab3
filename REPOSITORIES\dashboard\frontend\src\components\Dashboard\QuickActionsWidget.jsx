import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  IconButton,
  Tooltip,
  Grid,
  Divider,
  Chip
} from '@mui/material'
import {
  ViewList,
  Computer,
  Assessment,
  Settings,
  Add,
  Search,
  Download,
  Refresh,
  Security,
  Notifications,
  People,
  Storage
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import useCompactMode from '../../hooks/useCompactMode'

// Utility function to export dashboard data
const exportDashboardData = (dashboardData) => {
  const dataToExport = {
    exportedAt: new Date().toISOString(),
    overview: dashboardData.overview,
    systemHealth: dashboardData.systemHealth,
    agentStatus: dashboardData.agentStatus,
    recentAlerts: dashboardData.recentAlerts
  }

  const dataStr = JSON.stringify(dataToExport, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const QuickActionsWidget = ({ onRefreshDashboard }) => {
  const navigate = useNavigate()
  const { user } = useSelector((state) => state.auth)
  const dashboardData = useSelector((state) => state.dashboard)
  const { getCompactStyles, typography, sizes } = useCompactMode()

  const quickActions = [
    {
      title: 'View Logs',
      description: 'Browse and search log entries',
      icon: <ViewList color="primary" />,
      action: () => navigate('/logs'),
      color: 'primary',
      permission: 'nav_logs'
    },
    {
      title: 'Manage Agents',
      description: 'Monitor and configure agents',
      icon: <Computer color="success" />,
      action: () => navigate('/agents'),
      color: 'success',
      permission: 'nav_agents'
    },
    {
      title: 'View Reports',
      description: 'Access analytics reports',
      icon: <Assessment color="info" />,
      action: () => navigate('/reports'),
      color: 'info',
      permission: 'nav_reports'
    },
    {
      title: 'Settings',
      description: 'Configure preferences',
      icon: <Settings color="warning" />,
      action: () => navigate('/settings'),
      color: 'warning',
      permission: 'nav_settings'
    }
  ]

  const utilityActions = [
    {
      title: 'Search Logs',
      icon: <Search />,
      action: () => navigate('/logs'),
      tooltip: 'Advanced log search'
    },
    {
      title: 'View Alerts',
      icon: <Notifications />,
      action: () => navigate('/alerts'),
      tooltip: 'View active alerts'
    },
    {
      title: 'Export Data',
      icon: <Download />,
      action: () => exportDashboardData(dashboardData),
      tooltip: 'Export dashboard data'
    },
    {
      title: 'Refresh',
      icon: <Refresh />,
      action: onRefreshDashboard,
      tooltip: 'Refresh dashboard'
    }
  ]

  // Generate system status based on real data
  const getSystemStatus = () => {
    const status = []

    // Database status
    if (dashboardData.systemHealth?.health?.database) {
      const dbHealth = dashboardData.systemHealth.health.database
      status.push({
        label: 'Database',
        icon: <Storage fontSize="small" />,
        status: dbHealth.status === 'healthy' ? 'healthy' : 'warning',
        color: dbHealth.status === 'healthy' ? 'success' : 'warning'
      })
    }

    // Alerts status
    if (dashboardData.recentAlerts?.summary) {
      const alertCount = dashboardData.recentAlerts.summary.total || 0
      status.push({
        label: `${alertCount} Alerts`,
        icon: <Notifications fontSize="small" />,
        status: alertCount > 0 ? 'active' : 'clear',
        color: alertCount > 5 ? 'error' : alertCount > 0 ? 'warning' : 'success'
      })
    }

    // Agents status
    if (dashboardData.agentStatus?.summary) {
      const agentSummary = dashboardData.agentStatus.summary
      const healthyAgents = agentSummary.online || 0
      const totalAgents = agentSummary.total || 0
      status.push({
        label: `${healthyAgents}/${totalAgents} Agents`,
        icon: <Computer fontSize="small" />,
        status: healthyAgents === totalAgents ? 'healthy' : 'warning',
        color: healthyAgents === totalAgents ? 'success' : 'warning'
      })
    }

    // API status
    if (dashboardData.systemHealth?.health?.api) {
      const apiHealth = dashboardData.systemHealth.health.api
      status.push({
        label: 'API',
        icon: <Security fontSize="small" />,
        status: apiHealth.status === 'healthy' ? 'healthy' : 'warning',
        color: apiHealth.status === 'healthy' ? 'success' : 'warning'
      })
    }

    return status
  }

  const systemStatus = getSystemStatus()

  // Filter actions based on user permissions
  const availableActions = quickActions.filter(action => {
    if (!action.permission) return true
    return user?.permissions?.includes(action.permission) || user?.role === 'admin'
  })

  const availableUtilityActions = utilityActions.filter(action => {
    if (!action.permission) return true
    return user?.permissions?.includes(action.permission) || user?.role === 'admin'
  })

  return (
    <Card sx={getCompactStyles('card')}>
      <CardContent>
        <Typography variant={typography.sectionTitle} gutterBottom>
          Quick Actions
        </Typography>

        {/* Main Actions */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={1}>
            {availableActions.map((action, index) => (
              <Grid item xs={6} key={index}>
                <Button
                  fullWidth
                  variant="outlined"
                  color={action.color}
                  startIcon={action.icon}
                  onClick={action.action}
                  sx={{
                    height: 64,
                    flexDirection: 'column',
                    gap: 0.5,
                    textTransform: 'none',
                    '& .MuiButton-startIcon': {
                      margin: 0
                    }
                  }}
                >
                  <Typography variant="caption" fontWeight="bold">
                    {action.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                    {action.description}
                  </Typography>
                </Button>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Utility Actions */}
        <Box sx={{ mb: 3 }}>
          <Typography variant={typography.body} color="text.secondary" gutterBottom>
            Quick Tools
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'space-around', gap: 1 }}>
            {availableUtilityActions.map((action, index) => (
              <Tooltip key={index} title={action.tooltip}>
                <IconButton
                  onClick={action.action}
                  sx={{
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 2,
                    width: 48,
                    height: 48
                  }}
                >
                  {action.icon}
                </IconButton>
              </Tooltip>
            ))}
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* System Status Overview */}
        <Box>
          <Typography variant={typography.body} color="text.secondary" gutterBottom>
            System Status
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {systemStatus.map((status, index) => (
              <Chip
                key={index}
                icon={status.icon}
                label={status.label}
                color={status.color}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.7rem' }}
              />
            ))}
          </Box>
        </Box>

        {/* Recent Activity Summary */}
        <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
            Last Updated: {new Date().toLocaleTimeString()}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Welcome back, {user?.firstName || user?.username || 'User'}!
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}

export default QuickActionsWidget

import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  IconButton,
  Tooltip,
  Divider,
  But<PERSON>
} from '@mui/material'
import {
  Warning,
  Error,
  Info,
  CheckCircle,
  Schedule,
  Refresh,
  OpenInNew,
  TrendingUp,
  TrendingDown
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { formatDistanceToNow } from 'date-fns'
import { useNavigate } from 'react-router-dom'
import useCompactMode from '../../hooks/useCompactMode'

const RecentAlertsWidget = ({ alertData, isLoading, onRefresh }) => {
  const theme = useTheme()
  const navigate = useNavigate()
  const { getCompactStyles, typography, sizes } = useCompactMode()

  const handleViewAll = () => {
    navigate('/alerts')
  }

  if (isLoading) {
    return (
      <Card sx={getCompactStyles('card')}>
        <CardContent>
          <Typography variant={typography.sectionTitle} gutterBottom>
            Recent Alerts
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 200 }}>
            <Typography color="text.secondary">Loading alerts...</Typography>
          </Box>
        </CardContent>
      </Card>
    )
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <Error color="error" fontSize={sizes.iconSize} />
      case 'high':
        return <Warning color="warning" fontSize={sizes.iconSize} />
      case 'medium':
        return <Info color="info" fontSize={sizes.iconSize} />
      case 'low':
        return <CheckCircle color="success" fontSize={sizes.iconSize} />
      default:
        return <Info color="disabled" fontSize={sizes.iconSize} />
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'error'
      case 'high':
        return 'warning'
      case 'medium':
        return 'info'
      case 'low':
        return 'success'
      default:
        return 'default'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'resolved':
        return <CheckCircle color="success" fontSize="small" />
      case 'acknowledged':
        return <Schedule color="warning" fontSize="small" />
      case 'open':
        return <Error color="error" fontSize="small" />
      default:
        return <Info color="disabled" fontSize="small" />
    }
  }

  const summary = alertData?.summary || { critical: 0, high: 0, medium: 0, low: 0, total: 0 }
  const recentAlerts = alertData?.recentAlerts || []
  const trend = alertData?.trend || { direction: 'stable', percentage: 0 }

  return (
    <Card sx={getCompactStyles('card')}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant={typography.sectionTitle}>
            Recent Alerts
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Refresh alerts">
              <IconButton size="small" onClick={onRefresh}>
                <Refresh fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="View all alerts">
              <IconButton size="small" onClick={handleViewAll}>
                <OpenInNew fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Alert Summary */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant={typography.body} color="text.secondary">
              Total Active Alerts
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant={typography.body} fontWeight="bold">
                {summary.total}
              </Typography>
              {trend.direction !== 'stable' && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {trend.direction === 'up' ? (
                    <TrendingUp fontSize="small" color="error" />
                  ) : (
                    <TrendingDown fontSize="small" color="success" />
                  )}
                  <Typography variant="caption" color={trend.direction === 'up' ? 'error.main' : 'success.main'}>
                    {trend.percentage}%
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>

          {/* Severity Breakdown */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {summary.critical > 0 && (
              <Chip
                icon={<Error />}
                label={`${summary.critical} Critical`}
                color="error"
                size="small"
                variant="outlined"
              />
            )}
            {summary.high > 0 && (
              <Chip
                icon={<Warning />}
                label={`${summary.high} High`}
                color="warning"
                size="small"
                variant="outlined"
              />
            )}
            {summary.medium > 0 && (
              <Chip
                icon={<Info />}
                label={`${summary.medium} Medium`}
                color="info"
                size="small"
                variant="outlined"
              />
            )}
            {summary.low > 0 && (
              <Chip
                icon={<CheckCircle />}
                label={`${summary.low} Low`}
                color="success"
                size="small"
                variant="outlined"
              />
            )}
          </Box>
        </Box>

        {/* Recent Alerts List */}
        {recentAlerts.length > 0 ? (
          <>
            <Typography variant={typography.body} color="text.secondary" gutterBottom>
              Latest Alerts
            </Typography>
            <List dense sx={{ maxHeight: 250, overflow: 'auto' }}>
              {recentAlerts.slice(0, 5).map((alert, index) => (
                <React.Fragment key={alert._id || index}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                        {getSeverityIcon(alert.severity)}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant={typography.body} noWrap sx={{ flex: 1 }}>
                            {alert.title || alert.message || 'Alert'}
                          </Typography>
                          {getStatusIcon(alert.status)}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                          <Chip
                            label={alert.severity}
                            color={getSeverityColor(alert.severity)}
                            size="small"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {alert.createdAt ? formatDistanceToNow(new Date(alert.createdAt), { addSuffix: true }) : 'Unknown time'}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < recentAlerts.slice(0, 5).length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
            {recentAlerts.length > 5 && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button size="small" onClick={handleViewAll}>
                  View All {recentAlerts.length} Alerts
                </Button>
              </Box>
            )}
          </>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
            <CheckCircle sx={{ fontSize: 48, color: theme.palette.success.main, mb: 1 }} />
            <Typography variant={typography.body} color="text.secondary" align="center">
              No active alerts
            </Typography>
            <Typography variant="caption" color="text.secondary" align="center">
              System is running smoothly
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default RecentAlertsWidget

import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  LinearProgress
} from '@mui/material'
import {
  Person,
  Login,
  Logout,
  Schedule,
  TrendingUp,
  TrendingDown,
  Refresh,
  MoreVert
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { formatDistanceToNow } from 'date-fns'
import useCompactMode from '../../hooks/useCompactMode'

const UserActivityWidget = ({ userActivityData, isLoading, onRefresh }) => {
  const theme = useTheme()
  const { getCompactStyles, typography, sizes } = useCompactMode()

  if (isLoading) {
    return (
      <Card sx={getCompactStyles('card')}>
        <CardContent>
          <Typography variant={typography.sectionTitle} gutterBottom>
            User Activity
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 200 }}>
            <Typography color="text.secondary">Loading user activity...</Typography>
          </Box>
        </CardContent>
      </Card>
    )
  }

  const getActivityIcon = (action) => {
    switch (action) {
      case 'login':
        return <Login color="success" fontSize={sizes.iconSize} />
      case 'logout':
        return <Logout color="info" fontSize={sizes.iconSize} />
      case 'view_logs':
        return <Person color="primary" fontSize={sizes.iconSize} />
      default:
        return <Schedule color="disabled" fontSize={sizes.iconSize} />
    }
  }

  const getActivityColor = (action) => {
    switch (action) {
      case 'login':
        return 'success'
      case 'logout':
        return 'info'
      case 'view_logs':
        return 'primary'
      default:
        return 'default'
    }
  }

  const summary = userActivityData?.summary || { 
    activeUsers: 0, 
    totalSessions: 0, 
    avgSessionTime: 0,
    trend: { direction: 'stable', percentage: 0 }
  }
  
  const recentActivity = userActivityData?.recentActivity || []
  const activeSessions = userActivityData?.activeSessions || []

  return (
    <Card sx={getCompactStyles('card')}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant={typography.sectionTitle}>
            User Activity
          </Typography>
          <Tooltip title="Refresh user activity">
            <IconButton size="small" onClick={onRefresh}>
              <Refresh fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Activity Summary */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant={typography.body} color="text.secondary">
              Active Users
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant={typography.body} fontWeight="bold">
                {summary.activeUsers}
              </Typography>
              {summary.trend.direction !== 'stable' && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {summary.trend.direction === 'up' ? (
                    <TrendingUp fontSize="small" color="success" />
                  ) : (
                    <TrendingDown fontSize="small" color="error" />
                  )}
                  <Typography variant="caption" color={summary.trend.direction === 'up' ? 'success.main' : 'error.main'}>
                    {summary.trend.percentage}%
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>

          {/* Session Activity Bar */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Session Activity
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {summary.totalSessions} sessions
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={Math.min((summary.activeUsers / Math.max(summary.totalSessions, 1)) * 100, 100)}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: theme.palette.grey[200],
                '& .MuiLinearProgress-bar': {
                  backgroundColor: theme.palette.primary.main
                }
              }}
            />
          </Box>

          {/* Quick Stats */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              icon={<Person />}
              label={`${summary.activeUsers} Active`}
              color="primary"
              size="small"
              variant="outlined"
            />
            <Chip
              icon={<Schedule />}
              label={`${Math.round(summary.avgSessionTime)}m avg`}
              color="info"
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>

        {/* Recent Activity */}
        {recentActivity.length > 0 ? (
          <>
            <Typography variant={typography.body} color="text.secondary" gutterBottom>
              Recent Activity
            </Typography>
            <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
              {recentActivity.slice(0, 5).map((activity, index) => (
                <React.Fragment key={activity.id || index}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                        {getActivityIcon(activity.action)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography variant={typography.body} noWrap>
                          {activity.user?.firstName || activity.user?.username || 'Unknown User'}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                          <Chip
                            label={activity.action}
                            color={getActivityColor(activity.action)}
                            size="small"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {activity.timestamp ? formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true }) : 'Unknown time'}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < recentActivity.slice(0, 5).length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
            <Person sx={{ fontSize: 48, color: theme.palette.grey[400], mb: 1 }} />
            <Typography variant={typography.body} color="text.secondary" align="center">
              No recent activity
            </Typography>
            <Typography variant="caption" color="text.secondary" align="center">
              User activity will appear here
            </Typography>
          </Box>
        )}

        {/* Active Sessions Summary */}
        {activeSessions.length > 0 && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
              Active Sessions: {activeSessions.length}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {activeSessions.slice(0, 3).map((session, index) => (
                <Chip
                  key={index}
                  label={session.user?.username || 'User'}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem' }}
                />
              ))}
              {activeSessions.length > 3 && (
                <Chip
                  label={`+${activeSessions.length - 3} more`}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem' }}
                />
              )}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default UserActivityWidget

import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material'
import {
  Psychology,
  Refresh,
  Settings,
  TrendingUp,
  Security,
  BugReport,
  Shield,
  Timeline,
  Assessment,
  FilterList
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { formatDistanceToNow } from 'date-fns'

// Import AI Insights components
import ThreatOverviewDashboard from './components/ThreatOverviewDashboard'
import AnomalyDetectionResults from './components/AnomalyDetectionResults'
import SecurityPatternsAnalysis from './components/SecurityPatternsAnalysis'
import PredictiveAnalytics from './components/PredictiveAnalytics'
import AIModelPerformance from './components/AIModelPerformance'
import AIConfiguration from './components/AIConfiguration'

// Import API service
import { aiService } from '../../services/api'

const AIInsights = () => {
  const theme = useTheme()
  const [currentTab, setCurrentTab] = useState(0)
  const [aiData, setAiData] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [timeRange, setTimeRange] = useState('24h') // Default to 24h, will be updated from config
  const [refreshInterval, setRefreshInterval] = useState(null)
  const [config, setConfig] = useState(null)

  // Tab configuration
  const tabs = [
    { label: 'Threat Overview', icon: <Security />, component: ThreatOverviewDashboard },
    { label: 'Anomaly Detection', icon: <BugReport />, component: AnomalyDetectionResults },
    { label: 'Security Patterns', icon: <Shield />, component: SecurityPatternsAnalysis },
    { label: 'Predictive Analytics', icon: <Timeline />, component: PredictiveAnalytics },
    { label: 'Model Performance', icon: <Assessment />, component: AIModelPerformance },
    { label: 'Configuration', icon: <Settings />, component: AIConfiguration }
  ]

  // Fetch AI insights data
  const fetchAIInsights = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const [insights, threats, anomalies, performance] = await Promise.all([
        aiService.getInsights(),
        aiService.getThreats(),
        aiService.getAnomalies({ limit: 20 }),
        aiService.getPerformance()
      ])

      setAiData({
        insights: insights.data,
        threats: threats.data,
        anomalies: anomalies.data,
        performance: performance.data,
        lastUpdated: new Date().toISOString()
      })
    } catch (err) {
      console.error('Failed to fetch AI insights:', err)
      setError(err.message || 'Failed to load AI insights')
    } finally {
      setIsLoading(false)
    }
  }

  // Trigger manual analysis
  const triggerAnalysis = async () => {
    try {
      setIsLoading(true)
      await aiService.triggerAnalysis({ timeRange, logTypes: [] })
      await fetchAIInsights()
    } catch (err) {
      console.error('Failed to trigger analysis:', err)
      setError(err.message || 'Failed to trigger analysis')
      setIsLoading(false)
    }
  }

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue)
  }

  // Handle auto-refresh toggle
  const handleAutoRefreshToggle = (event) => {
    const enabled = event.target.checked
    setAutoRefresh(enabled)

    if (enabled) {
      const interval = setInterval(fetchAIInsights, 30000) // Refresh every 30 seconds
      setRefreshInterval(interval)
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval)
        setRefreshInterval(null)
      }
    }
  }

  // Load AI configuration
  const loadConfiguration = async () => {
    try {
      const response = await aiService.getConfig()
      const configData = response.data?.data || response.data || {}
      setConfig(configData)

      // Update time range to default from config
      if (configData.defaultTimeRange) {
        setTimeRange(configData.defaultTimeRange)
      }
    } catch (error) {
      console.error('Failed to load AI configuration:', error)
      // Keep default time range if config fails to load
    }
  }

  // Handle time range change
  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value)
  }

  // Initialize data on component mount
  useEffect(() => {
    // Load configuration first, then fetch AI insights
    loadConfiguration().then(() => {
      fetchAIInsights()
    })

    // Cleanup interval on unmount
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [])

  // Re-fetch data when time range changes
  useEffect(() => {
    if (!isLoading) {
      fetchAIInsights()
    }
  }, [timeRange])

  const CurrentTabComponent = tabs[currentTab]?.component

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Psychology sx={{ fontSize: 32, color: theme.palette.primary.main }} />
            <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
              AI Security Insights
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* Time Range Selector */}
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={handleTimeRangeChange}
              >
                <MenuItem value="15m">15 minutes</MenuItem>
                <MenuItem value="30m">30 minutes</MenuItem>
                <MenuItem value="1h">1 hour</MenuItem>
                <MenuItem value="2h">2 hours</MenuItem>
                <MenuItem value="6h">6 hours</MenuItem>
                <MenuItem value="12h">12 hours</MenuItem>
                <MenuItem value="24h">24 hours</MenuItem>
                <MenuItem value="7d">7 days</MenuItem>
              </Select>
            </FormControl>

            {/* Auto-refresh Toggle */}
            <FormControlLabel
              control={
                <Switch
                  checked={autoRefresh}
                  onChange={handleAutoRefreshToggle}
                  size="small"
                />
              }
              label="Auto-refresh"
            />

            {/* Manual Refresh Button */}
            <Tooltip title="Refresh insights">
              <IconButton onClick={fetchAIInsights} disabled={isLoading}>
                <Refresh />
              </IconButton>
            </Tooltip>

            {/* Trigger Analysis Button */}
            <Button
              variant="contained"
              startIcon={<TrendingUp />}
              onClick={triggerAnalysis}
              disabled={isLoading}
            >
              Run Analysis
            </Button>
          </Box>
        </Box>

        {/* Status Information */}
        {aiData && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Chip
              label={`Threat Level: ${aiData.insights?.summary?.riskLevel?.toUpperCase() || 'UNKNOWN'}`}
              color={
                aiData.insights?.summary?.riskLevel === 'critical' ? 'error' :
                  aiData.insights?.summary?.riskLevel === 'high' ? 'warning' :
                    aiData.insights?.summary?.riskLevel === 'medium' ? 'info' : 'success'
              }
              sx={{ fontWeight: 'bold' }}
            />
            <Typography variant="body2" color="text.secondary">
              Last updated: {aiData.lastUpdated ? formatDistanceToNow(new Date(aiData.lastUpdated), { addSuffix: true }) : 'Unknown'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {aiData.insights?.summary?.totalLogs || 0} logs analyzed
            </Typography>
          </Box>
        )}

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
      </Box>

      {/* Loading State */}
      {isLoading && !aiData && (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <CircularProgress size={60} />
        </Box>
      )}

      {/* Main Content */}
      {!isLoading || aiData ? (
        <>
          {/* Navigation Tabs */}
          <Card sx={{ mb: 3 }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  label={tab.label}
                  icon={tab.icon}
                  iconPosition="start"
                  sx={{ minHeight: 64 }}
                />
              ))}
            </Tabs>
          </Card>

          {/* Tab Content */}
          <Box sx={{ mt: 3 }}>
            {CurrentTabComponent && (
              <CurrentTabComponent
                aiData={aiData}
                isLoading={isLoading}
                onRefresh={fetchAIInsights}
                timeRange={timeRange}
              />
            )}
          </Box>
        </>
      ) : null}
    </Box>
  )
}

export default AIInsights

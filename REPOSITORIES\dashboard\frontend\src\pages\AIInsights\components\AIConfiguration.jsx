import React, { useState, useEffect } from 'react'
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Alert,
  Divider,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import { Settings, Save } from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { aiService } from '../../../services/api'

const AIConfiguration = ({ aiData, isLoading, onRefresh }) => {
  const theme = useTheme()
  const [config, setConfig] = useState({
    analysisInterval: 300,
    anomalyThreshold: 0.1,
    defaultTimeRange: '24h',
    enabledFeatures: {
      anomalyDetection: true,
      threatPrediction: true,
      patternMatching: true,
      realTimeAnalysis: true
    }
  })
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState(null)

  useEffect(() => {
    loadConfiguration()
  }, [])

  const loadConfiguration = async () => {
    try {
      const response = await aiService.getConfig()
      console.log('AI Config API Response:', response)

      // The backend returns {success: true, data: config, timestamp: ...}
      // So we need to access response.data.data to get the actual config
      const configData = response.data?.data || response.data || {}
      console.log('Extracted config data:', configData)

      // Ensure enabledFeatures exists with defaults
      const safeConfig = {
        analysisInterval: configData.analysisInterval || 300,
        anomalyThreshold: configData.anomalyThreshold || 0.1,
        defaultTimeRange: configData.defaultTimeRange || '24h',
        enabledFeatures: {
          anomalyDetection: true,
          threatPrediction: true,
          patternMatching: true,
          realTimeAnalysis: true,
          ...(configData.enabledFeatures || {})
        }
      }

      console.log('Safe config to set:', safeConfig)
      setConfig(safeConfig)
    } catch (error) {
      console.error('Failed to load configuration:', error)
      setMessage({ type: 'error', text: 'Failed to load configuration' })
      // Don't change config on error - keep the default state
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      await aiService.updateConfig(config)
      setMessage({ type: 'success', text: 'Configuration saved successfully' })
      if (onRefresh) onRefresh()
    } catch (error) {
      console.error('Failed to save configuration:', error)
      setMessage({ type: 'error', text: 'Failed to save configuration' })
    } finally {
      setSaving(false)
    }
  }

  const handleFeatureToggle = (feature) => (event) => {
    setConfig(prev => ({
      ...prev,
      enabledFeatures: {
        ...prev.enabledFeatures || {},
        [feature]: event.target.checked
      }
    }))
  }

  const handleIntervalChange = (event) => {
    setConfig(prev => ({
      ...prev,
      analysisInterval: parseInt(event.target.value)
    }))
  }

  const handleThresholdChange = (event, newValue) => {
    setConfig(prev => ({
      ...prev,
      anomalyThreshold: newValue
    }))
  }

  const handleTimeRangeChange = (event) => {
    setConfig(prev => ({
      ...prev,
      defaultTimeRange: event.target.value
    }))
  }

  return (
    <Grid container spacing={3}>
      {/* Header */}
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Settings sx={{ fontSize: 32, color: theme.palette.primary.main }} />
          <Typography variant="h6">
            AI Configuration
          </Typography>
        </Box>

        {message && (
          <Alert
            severity={message.type}
            sx={{ mb: 2 }}
            onClose={() => setMessage(null)}
          >
            {message.text}
          </Alert>
        )}
      </Grid>

      {/* Analysis Settings */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Analysis Settings
            </Typography>

            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Analysis Interval</InputLabel>
                <Select
                  value={config.analysisInterval}
                  label="Analysis Interval"
                  onChange={handleIntervalChange}
                >
                  <MenuItem value={60}>1 minute</MenuItem>
                  <MenuItem value={300}>5 minutes</MenuItem>
                  <MenuItem value={600}>10 minutes</MenuItem>
                  <MenuItem value={1800}>30 minutes</MenuItem>
                  <MenuItem value={3600}>1 hour</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography gutterBottom>
                Anomaly Detection Threshold: {config.anomalyThreshold}
              </Typography>
              <Slider
                value={config.anomalyThreshold}
                onChange={handleThresholdChange}
                min={0.01}
                max={0.5}
                step={0.01}
                marks={[
                  { value: 0.01, label: 'Sensitive' },
                  { value: 0.1, label: 'Normal' },
                  { value: 0.5, label: 'Conservative' }
                ]}
                valueLabelDisplay="auto"
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Default Analysis Time Range</InputLabel>
                <Select
                  value={config.defaultTimeRange}
                  onChange={handleTimeRangeChange}
                  label="Default Analysis Time Range"
                >
                  <MenuItem value="1h">1 hour</MenuItem>
                  <MenuItem value="6h">6 hours</MenuItem>
                  <MenuItem value="12h">12 hours</MenuItem>
                  <MenuItem value="24h">24 hours (Recommended)</MenuItem>
                  <MenuItem value="48h">48 hours</MenuItem>
                  <MenuItem value="7d">7 days</MenuItem>
                </Select>
              </FormControl>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Default time range for AI analysis when the page loads. 24 hours provides optimal balance between performance and threat detection.
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Feature Toggles */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Enabled Features
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={config.enabledFeatures?.anomalyDetection || false}
                  onChange={handleFeatureToggle('anomalyDetection')}
                />
              }
              label="Anomaly Detection"
              sx={{ display: 'block', mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={config.enabledFeatures?.threatPrediction || false}
                  onChange={handleFeatureToggle('threatPrediction')}
                />
              }
              label="Threat Prediction"
              sx={{ display: 'block', mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={config.enabledFeatures?.patternMatching || false}
                  onChange={handleFeatureToggle('patternMatching')}
                />
              }
              label="Pattern Matching"
              sx={{ display: 'block', mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={config.enabledFeatures?.realTimeAnalysis || false}
                  onChange={handleFeatureToggle('realTimeAnalysis')}
                />
              }
              label="Real-time Analysis"
              sx={{ display: 'block', mb: 1 }}
            />
          </CardContent>
        </Card>
      </Grid>

      {/* Model Information */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Model Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    v1.0.0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Model Version
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    Isolation Forest
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Anomaly Detection
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                    MITRE ATT&CK
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pattern Matching
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                    Active
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Save Button */}
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSave}
            disabled={saving}
            size="large"
          >
            {saving ? 'Saving...' : 'Save Configuration'}
          </Button>
        </Box>
      </Grid>
    </Grid>
  )
}

export default AIConfiguration

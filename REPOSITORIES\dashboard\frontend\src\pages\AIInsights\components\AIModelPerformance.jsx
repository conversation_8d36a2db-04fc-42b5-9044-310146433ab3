import React from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Alert
} from '@mui/material'
import { Assessment, Memory, Speed } from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'

const AIModelPerformance = ({ aiData, isLoading }) => {
  const theme = useTheme()

  if (!aiData?.performance) {
    return (
      <Alert severity="info">
        No performance data available. The AI service may be starting up.
      </Alert>
    )
  }

  const { performance } = aiData

  return (
    <Grid container spacing={3}>
      {/* Performance Metrics */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
              <Assessment sx={{ fontSize: 32, color: theme.palette.primary.main }} />
              <Typography variant="h6">
                AI Model Performance Metrics
              </Typography>
            </Box>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Response Time
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(100, (performance.averageResponseTime || 0) / 50)} // Scale to 5000ms max
                      sx={{ flex: 1, height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body2">
                      {Math.round(performance.averageResponseTime || 0)}ms
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Memory Usage
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(100, ((performance.memoryUsage?.heapUsed || 0) / (performance.memoryUsage?.heapTotal || 1)) * 100)}
                      sx={{ flex: 1, height: 8, borderRadius: 4 }}
                      color="warning"
                    />
                    <Typography variant="body2">
                      {Math.round((performance.memoryUsage?.heapUsed || 0) / 1024 / 1024)}MB
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Cache Efficiency
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(100, (performance.cacheSize || 0) / 10)} // Scale to 1000 max
                      sx={{ flex: 1, height: 8, borderRadius: 4 }}
                      color="success"
                    />
                    <Typography variant="body2">
                      {performance.cacheSize || 0} items
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: theme.palette.background.default, borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                        {performance.totalAnalyses || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Analyses
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: theme.palette.background.default, borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                        {performance.anomaliesDetected || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Anomalies Found
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: theme.palette.background.default, borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                        {performance.threatsIdentified || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Threats Identified
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: theme.palette.background.default, borderRadius: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                        {Math.round((performance.uptime || 0) / 1000 / 60)}m
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Uptime
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* System Status */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              System Status
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                  <Box sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    bgcolor: performance.isInitialized ? theme.palette.success.main : theme.palette.error.main
                  }} />
                  <Typography variant="body2">
                    AI Service: {performance.isInitialized ? 'Online' : 'Offline'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                  <Memory sx={{ color: theme.palette.info.main }} />
                  <Typography variant="body2">
                    Memory: {Math.round((performance.memoryUsage?.heapUsed || 0) / 1024 / 1024)}MB / {Math.round((performance.memoryUsage?.heapTotal || 0) / 1024 / 1024)}MB
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                  <Speed sx={{ color: theme.palette.warning.main }} />
                  <Typography variant="body2">
                    Avg Response: {Math.round(performance.averageResponseTime || 0)}ms
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default AIModelPerformance

import React from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Divider,
  But<PERSON>
} from '@mui/material'
import {
  BugReport,
  Warning,
  Error,
  Info,
  ThumbUp,
  ThumbDown
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { formatDistanceToNow } from 'date-fns'
import { aiService } from '../../../services/api'

const AnomalyDetectionResults = ({ aiData, isLoading }) => {
  const theme = useTheme()

  const getRiskLevelColor = (level) => {
    const colors = {
      low: theme.palette.success.main,
      medium: theme.palette.warning.main,
      high: theme.palette.error.main,
      critical: theme.palette.error.dark
    }
    return colors[level] || colors.low
  }

  const getRiskLevelIcon = (level) => {
    switch (level) {
      case 'critical':
        return <Error sx={{ color: getRiskLevelColor(level) }} />
      case 'high':
        return <Warning sx={{ color: getRiskLevelColor(level) }} />
      case 'medium':
        return <BugReport sx={{ color: getRiskLevelColor(level) }} />
      default:
        return <Info sx={{ color: getRiskLevelColor(level) }} />
    }
  }

  const handleFeedback = async (anomalyId, isCorrect) => {
    try {
      await aiService.submitFeedback({
        anomalyId,
        isCorrect,
        feedback: isCorrect ? 'Correctly identified anomaly' : 'False positive'
      })
      // You could show a success message here
    } catch (error) {
      console.error('Failed to submit feedback:', error)
      // You could show an error message here
    }
  }

  if (!aiData?.anomalies) {
    return (
      <Alert severity="info">
        No anomaly detection data available. Run an analysis to detect anomalies.
      </Alert>
    )
  }

  const { anomalies } = aiData

  return (
    <Grid container spacing={3}>
      {/* Summary Statistics */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Anomaly Detection Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    {anomalies.anomalies?.length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Anomalies
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                    {anomalies.anomalies?.filter(a => a.riskLevel === 'critical').length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Critical
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                    {anomalies.anomalies?.filter(a => a.riskLevel === 'high').length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    High Risk
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    {anomalies.anomalies?.filter(a => ['medium', 'low'].includes(a.riskLevel)).length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Medium/Low Risk
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Detected Anomalies */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Detected Anomalies
            </Typography>
            {!anomalies.anomalies || anomalies.anomalies.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', py: 2 }}>
                No anomalies detected in the current time range
              </Typography>
            ) : (
              <List>
                {anomalies.anomalies.map((anomaly, index) => (
                  <React.Fragment key={anomaly.id || index}>
                    <ListItem sx={{ px: 0, alignItems: 'flex-start' }}>
                      <ListItemIcon sx={{ mt: 1 }}>
                        {getRiskLevelIcon(anomaly.riskLevel)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              {anomaly.description || 'Anomalous behavior detected'}
                            </Typography>
                            <Chip
                              label={anomaly.riskLevel}
                              size="small"
                              sx={{
                                backgroundColor: getRiskLevelColor(anomaly.riskLevel),
                                color: 'white'
                              }}
                            />
                            <Chip
                              label={`${anomaly.confidence || 0}% confidence`}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              <strong>Message:</strong> {anomaly.message || 'No message available'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              <strong>Log Level:</strong> {anomaly.logLevel || 'Unknown'} • 
                              <strong> Source:</strong> {anomaly.sourceType || 'Unknown'} • 
                              <strong> Score:</strong> {anomaly.anomalyScore?.toFixed(3) || 'N/A'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                              {anomaly.timestamp ? formatDistanceToNow(new Date(anomaly.timestamp), { addSuffix: true }) : 'Unknown time'}
                            </Typography>
                            
                            {/* Recommendations */}
                            {anomaly.recommendations && anomaly.recommendations.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                                  Recommendations:
                                </Typography>
                                <List dense sx={{ mt: 0.5 }}>
                                  {anomaly.recommendations.slice(0, 3).map((rec, recIndex) => (
                                    <ListItem key={recIndex} sx={{ px: 0, py: 0 }}>
                                      <Typography variant="caption" color="text.secondary">
                                        • {rec}
                                      </Typography>
                                    </ListItem>
                                  ))}
                                </List>
                              </Box>
                            )}

                            {/* Feedback Buttons */}
                            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                              <Button
                                size="small"
                                startIcon={<ThumbUp />}
                                onClick={() => handleFeedback(anomaly.id, true)}
                                variant="outlined"
                                color="success"
                              >
                                Correct
                              </Button>
                              <Button
                                size="small"
                                startIcon={<ThumbDown />}
                                onClick={() => handleFeedback(anomaly.id, false)}
                                variant="outlined"
                                color="error"
                              >
                                False Positive
                              </Button>
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < anomalies.anomalies.length - 1 && <Divider sx={{ my: 2 }} />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default AnomalyDetectionResults

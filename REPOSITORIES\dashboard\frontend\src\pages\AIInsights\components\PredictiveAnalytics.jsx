import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  CircularProgress,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material'
import { 
  Timeline, 
  TrendingUp, 
  TrendingDown, 
  Assessment, 
  Security,
  Warning,
  Speed,
  Storage
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { aiService } from '../../../services/api'

const PredictiveAnalytics = ({ aiData, isLoading, timeRange }) => {
  const theme = useTheme()
  const [predictions, setPredictions] = useState(null)
  const [historicalStats, setHistoricalStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchPredictiveData = async () => {
      try {
        setLoading(true)
        setError(null)

        const [threats, performance, insights] = await Promise.all([
          aiService.getThreats(),
          aiService.getPerformance(),
          aiService.getInsights()
        ])

        const predictiveInsights = processPredictiveData(threats.data, performance.data, insights.data)
        setPredictions(predictiveInsights)

        setHistoricalStats({
          threats: threats.data,
          performance: performance.data,
          insights: insights.data
        })

      } catch (err) {
        console.error('Failed to fetch predictive data:', err)
        setError(err.message || 'Failed to load predictive analysis')
      } finally {
        setLoading(false)
      }
    }

    fetchPredictiveData()
  }, [timeRange])

  const processPredictiveData = (threats, performance, insights) => {
    const predictions = {
      threatForecast: processThreatForecast(threats, insights),
      riskTrend: processRiskTrend(insights),
      capacityPrediction: processCapacityPrediction(performance, insights),
      performanceForecast: processPerformanceForecast(performance),
      recommendations: processAIRecommendations(threats, insights)
    }
    return predictions
  }

  const processThreatForecast = (threats, insights) => {
    const currentThreats = threats?.length || 0
    const riskLevel = insights?.summary?.riskLevel || 'low'
    
    const highConfidenceThreats = threats?.filter(threat => threat.confidence > 70) || []
    const avgRiskScore = threats?.reduce((sum, threat) => sum + (threat.overallRiskScore || threat.riskScore || 0), 0) / Math.max(threats?.length || 1, 1)
    
    const predictedThreats = Math.round(currentThreats + (avgRiskScore > 5 ? Math.ceil(avgRiskScore / 2) : 0))
    
    const confidence = threats?.length > 0 ? 
      Math.round(threats.reduce((sum, threat) => sum + (threat.confidence || 0), 0) / threats.length) : 50
    
    return {
      current: currentThreats,
      predicted: predictedThreats,
      trend: predictedThreats > currentThreats ? 'increasing' : 'stable',
      confidence: Math.min(95, Math.max(50, confidence)),
      timeframe: '24 hours',
      highConfidenceThreats: highConfidenceThreats.length,
      avgRiskScore: Math.round(avgRiskScore * 10) / 10
    }
  }

  const processRiskTrend = (insights) => {
    const currentRisk = insights?.summary?.riskLevel || 'low'
    const riskScores = { low: 25, medium: 50, high: 75, critical: 100 }
    const currentScore = riskScores[currentRisk]
    
    const anomalies = insights?.summary?.anomalies || 0
    const threats = insights?.summary?.threats || 0
    const patterns = insights?.summary?.patterns || 0
    
    let predictedScore = currentScore
    const riskFactors = anomalies * 2 + threats * 3 + patterns * 1.5
    
    if (riskFactors > 10) {
      predictedScore = Math.min(100, currentScore + 20)
    } else if (riskFactors > 5) {
      predictedScore = Math.min(100, currentScore + 10)
    } else if (riskFactors < 2) {
      predictedScore = Math.max(0, currentScore - 5)
    }
    
    return {
      current: currentScore,
      predicted: Math.round(predictedScore),
      trend: predictedScore > currentScore ? 'increasing' : predictedScore < currentScore ? 'decreasing' : 'stable',
      severity: predictedScore > 75 ? 'critical' : predictedScore > 50 ? 'high' : predictedScore > 25 ? 'medium' : 'low',
      riskFactors: Math.round(riskFactors * 10) / 10
    }
  }

  const processCapacityPrediction = (performance, insights) => {
    const totalLogs = insights?.summary?.totalLogs || 0
    const analysisTime = insights?.summary?.analysisTime || 0
    const memoryUsage = performance?.memoryUsage?.heapUsed || 0
    const cacheSize = performance?.cacheSize || 0
    
    const logsPerSecond = analysisTime > 0 ? totalLogs / (analysisTime / 1000) : 0
    const predictedDailyLogs = Math.round(logsPerSecond * 86400)
    const memoryUsageMB = Math.round(memoryUsage / 1024 / 1024)
    
    const memoryLimit = 512
    const capacityUtilization = Math.min(100, (memoryUsageMB / memoryLimit) * 100)
    
    return {
      currentLogVolume: totalLogs,
      predictedDailyVolume: predictedDailyLogs,
      memoryUsage: memoryUsageMB,
      capacityUtilization: Math.round(capacityUtilization),
      recommendation: capacityUtilization > 80 ? 'Scale up resources' : 
                     capacityUtilization > 60 ? 'Monitor resource usage' : 'Current capacity sufficient',
      cacheEfficiency: performance?.cacheHitRate || 0,
      analysisSpeed: Math.round(logsPerSecond)
    }
  }

  const processPerformanceForecast = (performance) => {
    const avgResponseTime = performance?.averageResponseTime || 0
    const uptime = performance?.uptime || 0
    const totalAnalyses = performance?.totalAnalyses || 0
    const isInitialized = performance?.isInitialized || false
    
    const responseTimeHistory = performance?.responseTimeHistory || [avgResponseTime]
    const trend = responseTimeHistory.length > 1 ? 
      responseTimeHistory[responseTimeHistory.length - 1] - responseTimeHistory[0] : 0
    
    const predictedResponseTime = Math.max(0, avgResponseTime + trend)
    const uptimeHours = Math.round(uptime / 1000 / 3600)
    
    let healthScore = 100
    if (!isInitialized) healthScore -= 30
    if (avgResponseTime > 5000) healthScore -= 20
    if (avgResponseTime > 2000) healthScore -= 10
    if (uptimeHours < 24) healthScore -= 15
    
    return {
      currentResponseTime: Math.round(avgResponseTime),
      predictedResponseTime: Math.round(predictedResponseTime),
      uptime: uptimeHours,
      totalAnalyses,
      performanceTrend: trend > 100 ? 'degrading' : trend < -100 ? 'improving' : 'stable',
      healthScore: Math.max(0, Math.round(healthScore)),
      isInitialized
    }
  }

  const processAIRecommendations = (threats, insights) => {
    const recommendations = []
    
    if (threats && threats.length > 0) {
      threats.forEach(threat => {
        if (threat.severity === 'critical' || threat.severity === 'high') {
          const threatRecommendations = threat.recommendations || []
          threatRecommendations.forEach(rec => {
            recommendations.push({
              type: 'security',
              priority: threat.severity === 'critical' ? 'high' : 'medium',
              title: `${threat.name} Mitigation`,
              description: `AI detected ${threat.name.toLowerCase()} with ${threat.confidence}% confidence`,
              action: rec
            })
          })
        }
      })
    }
    
    const riskLevel = insights?.summary?.riskLevel || 'low'
    const anomalies = insights?.summary?.anomalies || 0
    const patterns = insights?.summary?.patterns || 0
    
    if (riskLevel === 'high' || riskLevel === 'critical') {
      recommendations.push({
        type: 'security',
        priority: 'high',
        title: 'Elevated Risk Level Detected',
        description: `AI analysis indicates ${riskLevel} risk level with ${anomalies} anomalies and ${patterns} security patterns`,
        action: 'Review AI insights and implement recommended security measures'
      })
    }
    
    if (anomalies > 5) {
      recommendations.push({
        type: 'monitoring',
        priority: 'medium',
        title: 'High Anomaly Count',
        description: `AI detected ${anomalies} anomalies requiring investigation`,
        action: 'Review anomaly detection results and investigate root causes'
      })
    }
    
    return recommendations.slice(0, 5)
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'increasing':
      case 'degrading':
        return <TrendingUp sx={{ color: theme.palette.error.main }} />
      case 'decreasing':
      case 'improving':
        return <TrendingDown sx={{ color: theme.palette.success.main }} />
      default:
        return <Timeline sx={{ color: theme.palette.info.main }} />
    }
  }

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'increasing':
      case 'degrading':
        return theme.palette.error.main
      case 'decreasing':
      case 'improving':
        return theme.palette.success.main
      default:
        return theme.palette.info.main
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    )
  }

  if (!predictions) {
    return (
      <Alert severity="info">
        No predictive data available. Run an analysis to generate predictions.
      </Alert>
    )
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Security sx={{ fontSize: 32, color: theme.palette.primary.main }} />
              <Typography variant="h6">
                Threat Forecast
              </Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    {predictions.threatForecast.current}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current Threats
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: getTrendColor(predictions.threatForecast.trend) }}>
                    {predictions.threatForecast.predicted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Predicted (24h)
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getTrendIcon(predictions.threatForecast.trend)}
                <Typography variant="body2" sx={{ color: getTrendColor(predictions.threatForecast.trend) }}>
                  {predictions.threatForecast.trend}
                </Typography>
              </Box>
              <Chip 
                label={`${predictions.threatForecast.confidence}% confidence`}
                size="small"
                color="info"
              />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Assessment sx={{ fontSize: 32, color: theme.palette.warning.main }} />
              <Typography variant="h6">
                Risk Trend Analysis
              </Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Current Risk Score
              </Typography>
              <LinearProgress
                variant="determinate"
                value={predictions.riskTrend.current}
                sx={{ height: 8, borderRadius: 4, mb: 1 }}
                color={predictions.riskTrend.current > 75 ? 'error' : predictions.riskTrend.current > 50 ? 'warning' : 'success'}
              />
              <Typography variant="caption" color="text.secondary">
                {predictions.riskTrend.current}/100
              </Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Predicted Risk Score
              </Typography>
              <LinearProgress
                variant="determinate"
                value={predictions.riskTrend.predicted}
                sx={{ height: 8, borderRadius: 4, mb: 1 }}
                color={predictions.riskTrend.predicted > 75 ? 'error' : predictions.riskTrend.predicted > 50 ? 'warning' : 'success'}
              />
              <Typography variant="caption" color="text.secondary">
                {predictions.riskTrend.predicted}/100
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getTrendIcon(predictions.riskTrend.trend)}
                <Typography variant="body2" sx={{ color: getTrendColor(predictions.riskTrend.trend) }}>
                  {predictions.riskTrend.trend}
                </Typography>
              </Box>
              <Chip 
                label={predictions.riskTrend.severity}
                size="small"
                color={predictions.riskTrend.severity === 'critical' ? 'error' : predictions.riskTrend.severity === 'high' ? 'warning' : 'info'}
              />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Storage sx={{ fontSize: 32, color: theme.palette.success.main }} />
              <Typography variant="h6">
                Capacity Planning
              </Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    {predictions.capacityPrediction.currentLogVolume}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Current Logs
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    {predictions.capacityPrediction.predictedDailyVolume}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Predicted Daily
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                Memory Utilization
              </Typography>
              <LinearProgress
                variant="determinate"
                value={predictions.capacityPrediction.capacityUtilization}
                sx={{ height: 6, borderRadius: 3 }}
                color={predictions.capacityPrediction.capacityUtilization > 80 ? 'error' : 'success'}
              />
              <Typography variant="caption" color="text.secondary">
                {Math.round(predictions.capacityPrediction.capacityUtilization)}% ({predictions.capacityPrediction.memoryUsage}MB)
              </Typography>
            </Box>
            <Alert severity={predictions.capacityPrediction.capacityUtilization > 80 ? 'warning' : 'info'} sx={{ mt: 2 }}>
              {predictions.capacityPrediction.recommendation}
            </Alert>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Speed sx={{ fontSize: 32, color: theme.palette.info.main }} />
              <Typography variant="h6">
                Performance Forecast
              </Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    {predictions.performanceForecast.currentResponseTime}ms
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Current Response
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: getTrendColor(predictions.performanceForecast.performanceTrend) }}>
                    {predictions.performanceForecast.predictedResponseTime}ms
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Predicted Response
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getTrendIcon(predictions.performanceForecast.performanceTrend)}
                <Typography variant="body2" sx={{ color: getTrendColor(predictions.performanceForecast.performanceTrend) }}>
                  {predictions.performanceForecast.performanceTrend}
                </Typography>
              </Box>
              <Chip 
                label={`Health: ${predictions.performanceForecast.healthScore}%`}
                size="small"
                color={predictions.performanceForecast.healthScore > 90 ? 'success' : predictions.performanceForecast.healthScore > 70 ? 'warning' : 'error'}
              />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Uptime: {predictions.performanceForecast.uptime}h • Analyses: {predictions.performanceForecast.totalAnalyses}
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Predictive Recommendations
            </Typography>
            {predictions.recommendations.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                No specific recommendations based on current predictions
              </Typography>
            ) : (
              <List>
                {predictions.recommendations.map((rec, index) => (
                  <React.Fragment key={index}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              {rec.title}
                            </Typography>
                            <Chip
                              label={rec.priority}
                              size="small"
                              color={rec.priority === 'high' ? 'error' : rec.priority === 'medium' ? 'warning' : 'info'}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {rec.description}
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              Action: {rec.action}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < predictions.recommendations.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default PredictiveAnalytics

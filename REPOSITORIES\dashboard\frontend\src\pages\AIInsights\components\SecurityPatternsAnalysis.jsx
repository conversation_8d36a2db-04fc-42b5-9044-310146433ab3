import React from 'react'
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Divider,
  Link
} from '@mui/material'
import {
  Shield,
  Security,
  Warning,
  Error,
  OpenInNew
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'

const SecurityPatternsAnalysis = ({ aiData, isLoading }) => {
  const theme = useTheme()

  const getSeverityColor = (severity) => {
    const colors = {
      low: theme.palette.success.main,
      medium: theme.palette.warning.main,
      high: theme.palette.error.main,
      critical: theme.palette.error.dark
    }
    return colors[severity] || colors.low
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <Error sx={{ color: getSeverityColor(severity) }} />
      case 'high':
        return <Warning sx={{ color: getSeverityColor(severity) }} />
      case 'medium':
        return <Security sx={{ color: getSeverityColor(severity) }} />
      default:
        return <Shield sx={{ color: getSeverityColor(severity) }} />
    }
  }

  if (!aiData?.insights?.patterns) {
    return (
      <Alert severity="info">
        No security pattern analysis data available. Run an analysis to detect security patterns.
      </Alert>
    )
  }

  const { patterns } = aiData.insights

  return (
    <Grid container spacing={3}>
      {/* Summary Statistics */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Security Patterns Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    {patterns.length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Patterns
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    {patterns.filter(p => p.type === 'mitre_attack').length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    MITRE ATT&CK
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                    {patterns.filter(p => p.type === 'custom_rule').length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Custom Rules
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                    {patterns.filter(p => p.severity === 'critical').length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Critical Severity
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Detected Patterns */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Detected Security Patterns
            </Typography>
            {patterns.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', py: 2 }}>
                No security patterns detected in the current time range
              </Typography>
            ) : (
              <List>
                {patterns.map((pattern, index) => (
                  <React.Fragment key={pattern.id || index}>
                    <ListItem sx={{ px: 0, alignItems: 'flex-start' }}>
                      <ListItemIcon sx={{ mt: 1 }}>
                        {getSeverityIcon(pattern.severity)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              {pattern.name}
                            </Typography>
                            <Chip
                              label={pattern.type === 'mitre_attack' ? 'MITRE ATT&CK' : 'Custom Rule'}
                              size="small"
                              color={pattern.type === 'mitre_attack' ? 'primary' : 'secondary'}
                            />
                            <Chip
                              label={pattern.severity}
                              size="small"
                              sx={{
                                backgroundColor: getSeverityColor(pattern.severity),
                                color: 'white'
                              }}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {pattern.description}
                            </Typography>
                            
                            {pattern.techniqueId && (
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                <strong>Technique ID:</strong> {pattern.techniqueId}
                              </Typography>
                            )}
                            
                            {pattern.category && (
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                <strong>Category:</strong> {pattern.category}
                              </Typography>
                            )}
                            
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              <strong>Confidence:</strong> {pattern.confidence}% • 
                              <strong> Matches:</strong> {pattern.matchCount}
                            </Typography>

                            {/* References */}
                            {pattern.references && pattern.references.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                                  References:
                                </Typography>
                                {pattern.references.map((ref, refIndex) => (
                                  <Box key={refIndex} sx={{ mt: 0.5 }}>
                                    <Link
                                      href={ref}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                                    >
                                      <Typography variant="caption">
                                        {ref}
                                      </Typography>
                                      <OpenInNew sx={{ fontSize: 12 }} />
                                    </Link>
                                  </Box>
                                ))}
                              </Box>
                            )}

                            {/* Recommendations */}
                            {pattern.recommendations && pattern.recommendations.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                                  Recommendations:
                                </Typography>
                                <List dense sx={{ mt: 0.5 }}>
                                  {pattern.recommendations.slice(0, 3).map((rec, recIndex) => (
                                    <ListItem key={recIndex} sx={{ px: 0, py: 0 }}>
                                      <Typography variant="caption" color="text.secondary">
                                        • {rec}
                                      </Typography>
                                    </ListItem>
                                  ))}
                                </List>
                              </Box>
                            )}

                            {/* Indicators */}
                            {pattern.indicators && pattern.indicators.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                                  Sample Indicators ({pattern.indicators.length} total):
                                </Typography>
                                <List dense sx={{ mt: 0.5 }}>
                                  {pattern.indicators.slice(0, 2).map((indicator, indIndex) => (
                                    <ListItem key={indIndex} sx={{ px: 0, py: 0 }}>
                                      <Typography variant="caption" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
                                        {indicator.message?.substring(0, 100)}...
                                      </Typography>
                                    </ListItem>
                                  ))}
                                </List>
                              </Box>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < patterns.length - 1 && <Divider sx={{ my: 2 }} />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default SecurityPatternsAnalysis

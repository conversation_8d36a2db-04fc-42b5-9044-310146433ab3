import React from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  Alert,
  Divider
} from '@mui/material'
import {
  Security,
  Warning,
  Error,
  Shield,
  TrendingUp,
  TrendingDown,
  Timeline,
  Assessment
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { formatDistanceToNow } from 'date-fns'

const ThreatOverviewDashboard = ({ aiData, isLoading }) => {
  const theme = useTheme()

  const getThreatLevelColor = (level) => {
    const colors = {
      low: theme.palette.success.main,
      medium: theme.palette.warning.main,
      high: theme.palette.error.main,
      critical: theme.palette.error.dark
    }
    return colors[level] || colors.low
  }

  const getThreatLevelIcon = (level) => {
    switch (level) {
      case 'critical':
        return <Error sx={{ color: getThreatLevelColor(level) }} />
      case 'high':
        return <Warning sx={{ color: getThreatLevelColor(level) }} />
      case 'medium':
        return <Security sx={{ color: getThreatLevelColor(level) }} />
      default:
        return <Shield sx={{ color: getThreatLevelColor(level) }} />
    }
  }

  const getRiskScore = (level) => {
    const scores = { low: 25, medium: 50, high: 75, critical: 100 }
    return scores[level] || 0
  }

  if (!aiData?.insights) {
    return (
      <Alert severity="info">
        No threat overview data available. Run an analysis to see threat insights.
      </Alert>
    )
  }

  const { summary, threats = [], recommendations = [] } = aiData.insights

  return (
    <Grid container spacing={3}>
      {/* Current Threat Level */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Current Threat Level
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              {getThreatLevelIcon(summary?.riskLevel)}
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: getThreatLevelColor(summary?.riskLevel) }}>
                {summary?.riskLevel?.toUpperCase() || 'UNKNOWN'}
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={getRiskScore(summary?.riskLevel)}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: theme.palette.grey[200],
                '& .MuiLinearProgress-bar': {
                  backgroundColor: getThreatLevelColor(summary?.riskLevel),
                  borderRadius: 4
                }
              }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Risk Score: {getRiskScore(summary?.riskLevel)}/100
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Risk Score Trends */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Analysis Summary
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                  {summary?.totalLogs || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Logs Analyzed
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                  {summary?.anomalies || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Anomalies
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                  {summary?.threats || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Threats
                </Typography>
              </Box>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Analysis completed in {summary?.analysisTime || 0}ms
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Active Threats Timeline */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Threat Activity
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <Timeline sx={{ color: theme.palette.info.main }} />
              <Typography variant="body2">
                {threats.length} active threats detected
              </Typography>
            </Box>
            {threats.length > 0 ? (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Most recent threat:
                </Typography>
                <Chip
                  label={threats[0]?.name || 'Unknown threat'}
                  color={threats[0]?.severity === 'critical' ? 'error' : 'warning'}
                  size="small"
                />
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                No active threats detected
              </Typography>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Top Threats */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Top Security Threats
            </Typography>
            {threats.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', py: 2 }}>
                No threats detected in the current time range
              </Typography>
            ) : (
              <List>
                {threats.slice(0, 5).map((threat, index) => (
                  <React.Fragment key={threat.id || index}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon>
                        {threat.severity === 'critical' ? (
                          <Error sx={{ color: theme.palette.error.main }} />
                        ) : threat.severity === 'high' ? (
                          <Warning sx={{ color: theme.palette.warning.main }} />
                        ) : (
                          <Security sx={{ color: theme.palette.info.main }} />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              {threat.name}
                            </Typography>
                            <Chip
                              label={threat.severity}
                              size="small"
                              color={threat.severity === 'critical' ? 'error' : 'warning'}
                            />
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="body2" color="text.secondary">
                              {threat.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Confidence: {threat.confidence}% • Risk Score: {threat.riskScore}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < threats.slice(0, 5).length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Recommendations */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Security Recommendations
            </Typography>
            {recommendations.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                No specific recommendations at this time
              </Typography>
            ) : (
              <List dense>
                {recommendations.slice(0, 4).map((rec, index) => (
                  <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <Assessment 
                        sx={{ 
                          fontSize: 16,
                          color: rec.priority === 'critical' ? theme.palette.error.main :
                                 rec.priority === 'high' ? theme.palette.warning.main :
                                 theme.palette.info.main
                        }} 
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {rec.message}
                        </Typography>
                      }
                      secondary={
                        <Chip
                          label={rec.priority}
                          size="small"
                          sx={{
                            height: 16,
                            fontSize: '0.65rem',
                            mt: 0.5,
                            backgroundColor: rec.priority === 'critical' ? theme.palette.error.main :
                                           rec.priority === 'high' ? theme.palette.warning.main :
                                           theme.palette.info.main,
                            color: 'white'
                          }}
                        />
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* System Health */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              System Health Overview
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                    {aiData?.performance?.isInitialized ? '✓' : '✗'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    AI Service Status
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    {aiData?.performance?.totalAnalyses || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Analyses
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                    {Math.round(aiData?.performance?.averageResponseTime || 0)}ms
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Response Time
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    {Math.round((aiData?.performance?.uptime || 0) / 1000 / 60)}m
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Uptime
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default ThreatOverviewDashboard

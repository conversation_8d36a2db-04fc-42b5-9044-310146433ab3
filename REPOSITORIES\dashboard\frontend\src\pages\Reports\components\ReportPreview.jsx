import React from 'react'
import {
  Box,
  Typography,
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  Divider,
} from '@mui/material'
import ReportChart from '../../../components/Charts/ReportChart'

const ReportPreview = ({ reportData, reportName, reportType }) => {
  if (!reportData) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="text.secondary">No data to preview</Typography>
      </Paper>
    )
  }

  const renderAnalyticsPreview = (data) => (
    <Grid container spacing={3}>
      {/* Summary Cards */}
      {data.summary && (
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>Summary</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Critical Events
                  </Typography>
                  <Typography variant="h4" color="error">
                    {data.summary.criticalEvents}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Warning Events
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {data.summary.warningEvents}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Info Events
                  </Typography>
                  <Typography variant="h4" color="info.main">
                    {data.summary.infoEvents}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      )}

      {/* Charts */}
      {data.logsByLevel && (
        <Grid item xs={12} md={6}>
          <ReportChart
            type="pie"
            data={data.logsByLevel.map(item => ({ name: item._id, value: item.count }))}
            title="Events by Log Level"
            height={300}
          />
        </Grid>
      )}

      {data.logsByHour && (
        <Grid item xs={12} md={6}>
          <ReportChart
            type="line"
            data={data.logsByHour.map(item => ({ name: item._id, value: item.count }))}
            title="Events Over Time"
            height={300}
          />
        </Grid>
      )}

      {data.topHosts && (
        <Grid item xs={12}>
          <ReportChart
            type="bar"
            data={data.topHosts.slice(0, 10).map(item => ({ name: item._id, value: item.count }))}
            title="Top 10 Hosts by Event Count"
            height={300}
          />
        </Grid>
      )}

      {/* Recent Alerts Table */}
      {data.recentAlerts && data.recentAlerts.length > 0 && (
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>Recent Alerts</Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Severity</TableCell>
                  <TableCell>Rule</TableCell>
                  <TableCell>Description</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.recentAlerts.slice(0, 10).map((alert, index) => (
                  <TableRow key={index}>
                    <TableCell>{(() => {
                      try {
                        if (!alert.createdAt) return 'N/A'
                        const date = new Date(alert.createdAt)
                        return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleString()
                      } catch (error) {
                        return 'Invalid Date'
                      }
                    })()}</TableCell>
                    <TableCell>
                      <Chip 
                        label={alert.severity} 
                        color={alert.severity === 'high' ? 'error' : alert.severity === 'medium' ? 'warning' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{alert.rule?.name || 'N/A'}</TableCell>
                    <TableCell>{alert.description}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      )}
    </Grid>
  )

  const renderCompliancePreview = (data) => (
    <Grid container spacing={3}>
      {/* Compliance Score */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography color="text.secondary" gutterBottom>
              Compliance Score
            </Typography>
            <Typography variant="h2" color={data.complianceScore >= 80 ? 'success.main' : 'error.main'}>
              {data.complianceScore}%
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Rules Summary */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Rules Assessment</Typography>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Typography color="text.secondary">Total Rules</Typography>
                <Typography variant="h4">{data.totalRules}</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography color="text.secondary">Passed</Typography>
                <Typography variant="h4" color="success.main">{data.passedRules}</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography color="text.secondary">Failed</Typography>
                <Typography variant="h4" color="error.main">{data.failedRules}</Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Recommendations */}
      {data.recommendations && data.recommendations.length > 0 && (
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>Recommendations</Typography>
          <Paper sx={{ p: 2 }}>
            {data.recommendations.map((rec, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Typography variant="subtitle1" color="error">
                  {rec.recommendation}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Priority: {rec.priority} | Impact: {rec.impact}
                </Typography>
                {index < data.recommendations.length - 1 && <Divider sx={{ mt: 1 }} />}
              </Box>
            ))}
          </Paper>
        </Grid>
      )}
    </Grid>
  )

  const renderCustomPreview = (data) => (
    <Grid container spacing={3}>
      {Object.entries(data).map(([componentId, componentData]) => (
        <Grid item xs={12} md={6} key={componentId}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {componentId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Typography>
            {componentData.error ? (
              <Typography color="error">{componentData.error}</Typography>
            ) : (
              <pre style={{ fontSize: '12px', overflow: 'auto', maxHeight: '200px' }}>
                {JSON.stringify(componentData, null, 2)}
              </pre>
            )}
          </Paper>
        </Grid>
      ))}
    </Grid>
  )

  const renderPreviewContent = () => {
    switch (reportType || reportData.type) {
      case 'analytics':
        return renderAnalyticsPreview(reportData.data || reportData)
      case 'compliance':
        return renderCompliancePreview(reportData.data || reportData)
      case 'custom':
        return renderCustomPreview(reportData.data || reportData)
      default:
        return (
          <Paper sx={{ p: 3 }}>
            <pre style={{ fontSize: '12px', overflow: 'auto' }}>
              {JSON.stringify(reportData, null, 2)}
            </pre>
          </Paper>
        )
    }
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          {reportName || 'Report Preview'}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Generated: {new Date().toLocaleString()}
        </Typography>
        {reportData.metadata && (
          <Typography variant="body2" color="text.secondary">
            Time Range: {reportData.metadata.timeRange} | 
            Records: {reportData.metadata.recordCount?.toLocaleString() || 'N/A'}
          </Typography>
        )}
      </Box>

      {/* Content */}
      {renderPreviewContent()}
    </Box>
  )
}

export default ReportPreview

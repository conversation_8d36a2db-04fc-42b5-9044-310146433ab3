import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Grid,
  Avatar,
  Typography,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
} from '@mui/material'
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
} from '@mui/icons-material'
import { useSelector, useDispatch } from 'react-redux'
import { settingsService } from '../../../services/settingsService'

const ProfileTab = ({ onSuccess }) => {
  const { user } = useSelector((state) => state.auth)
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    username: '',
    role: '',
  })
  const [originalData, setOriginalData] = useState({})
  const [changePasswordDialog, setChangePasswordDialog] = useState(false)
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
  const [passwordErrors, setPasswordErrors] = useState({})

  useEffect(() => {
    if (user) {
      const data = {
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        username: user.username || '',
        role: user.role || '',
      }
      setProfileData(data)
      setOriginalData(data)
    }
  }, [user])

  const handleEditToggle = () => {
    if (isEditing) {
      // Cancel editing - restore original data
      setProfileData(originalData)
    }
    setIsEditing(!isEditing)
  }

  const handleInputChange = (field) => (event) => {
    setProfileData({
      ...profileData,
      [field]: event.target.value,
    })
  }

  const handleSaveProfile = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.updateProfile({
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        email: profileData.email,
      })

      if (response.status === 'success') {
        setOriginalData(profileData)
        setIsEditing(false)
        onSuccess('Profile updated successfully')
        
        // Update Redux store if needed
        // dispatch(updateUserProfile(response.data.profile))
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to update profile', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordChange = (field) => (event) => {
    setPasswordData({
      ...passwordData,
      [field]: event.target.value,
    })
    
    // Clear errors when user starts typing
    if (passwordErrors[field]) {
      setPasswordErrors({
        ...passwordErrors,
        [field]: '',
      })
    }
  }

  const validatePassword = () => {
    const errors = {}
    
    if (!passwordData.currentPassword) {
      errors.currentPassword = 'Current password is required'
    }
    
    if (!passwordData.newPassword) {
      errors.newPassword = 'New password is required'
    } else if (passwordData.newPassword.length < 8) {
      errors.newPassword = 'Password must be at least 8 characters long'
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(passwordData.newPassword)) {
      errors.newPassword = 'Password must contain uppercase, lowercase, number, and special character'
    }
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }
    
    setPasswordErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleChangePassword = async () => {
    if (!validatePassword()) return

    setIsLoading(true)
    try {
      const response = await settingsService.changePassword(passwordData)
      
      if (response.status === 'success') {
        setChangePasswordDialog(false)
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        })
        onSuccess('Password changed successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to change password', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const getRoleColor = (role) => {
    const colors = {
      admin: 'error',
      security_analyst: 'primary',
      compliance_officer: 'warning',
      executive: 'secondary',
    }
    return colors[role] || 'default'
  }

  const formatRoleName = (role) => {
    return role?.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ') || 'Unknown'
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Profile Information Card */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader
              title="Profile Information"
              action={
                <IconButton onClick={handleEditToggle} disabled={isLoading}>
                  {isEditing ? <CancelIcon /> : <EditIcon />}
                </IconButton>
              }
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={profileData.firstName}
                    onChange={handleInputChange('firstName')}
                    disabled={!isEditing || isLoading}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={profileData.lastName}
                    onChange={handleInputChange('lastName')}
                    disabled={!isEditing || isLoading}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={profileData.email}
                    onChange={handleInputChange('email')}
                    disabled={!isEditing || isLoading}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={profileData.username}
                    disabled
                    variant="outlined"
                    helperText="Username cannot be changed"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Role:
                    </Typography>
                    <Chip
                      label={formatRoleName(profileData.role)}
                      color={getRoleColor(profileData.role)}
                      size="small"
                    />
                  </Box>
                </Grid>
              </Grid>

              {isEditing && (
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveProfile}
                    disabled={isLoading}
                  >
                    Save Changes
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleEditToggle}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Profile Summary Card */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                }}
              >
                {user?.firstName?.[0]}{user?.lastName?.[0]}
              </Avatar>
              <Typography variant="h6" gutterBottom>
                {user?.firstName} {user?.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {user?.email}
              </Typography>
              <Chip
                label={formatRoleName(user?.role)}
                color={getRoleColor(user?.role)}
                size="small"
                sx={{ mb: 2 }}
              />
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Member since
              </Typography>
              <Typography variant="body2">
                {user?.createdAt ? (() => {
                  try {
                    const date = new Date(user.createdAt)
                    return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleDateString()
                  } catch (error) {
                    return 'Invalid Date'
                  }
                })() : 'Unknown'}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 1 }}>
                Last login
              </Typography>
              <Typography variant="body2">
                {user?.lastLogin ? (() => {
                  try {
                    const date = new Date(user.lastLogin)
                    return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleString()
                  } catch (error) {
                    return 'Invalid Date'
                  }
                })() : 'Never'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Actions Card */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Security Actions" />
            <CardContent>
              <Button
                variant="outlined"
                onClick={() => setChangePasswordDialog(true)}
                sx={{ mr: 2 }}
              >
                Change Password
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Change Password Dialog */}
      <Dialog
        open={changePasswordDialog}
        onClose={() => setChangePasswordDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Change Password</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Current Password"
              type="password"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange('currentPassword')}
              error={!!passwordErrors.currentPassword}
              helperText={passwordErrors.currentPassword}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="New Password"
              type="password"
              value={passwordData.newPassword}
              onChange={handlePasswordChange('newPassword')}
              error={!!passwordErrors.newPassword}
              helperText={passwordErrors.newPassword || 'Must contain uppercase, lowercase, number, and special character'}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Confirm New Password"
              type="password"
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange('confirmPassword')}
              error={!!passwordErrors.confirmPassword}
              helperText={passwordErrors.confirmPassword}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChangePasswordDialog(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleChangePassword}
            variant="contained"
            disabled={isLoading}
          >
            Change Password
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ProfileTab

import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Tabs,
  Tab,
  Grid,
  FormControlLabel,
  Checkbox,
  Chip,
  FormGroup,
  Divider,
  Alert,
} from '@mui/material'
import { updateUser } from '../../../store/slices/usersSlice'
import PermissionManager from './PermissionManager'

const EditUserDialog = ({ open, onClose, user, roles, permissions, showSnackbar }) => {
  const dispatch = useDispatch()
  const { isUpdating, groupedPermissions } = useSelector((state) => state.users)

  const [activeTab, setActiveTab] = useState(0)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    role: '',
    permissions: [],
    status: 'active',
  })
  const [errors, setErrors] = useState({})

  useEffect(() => {
    if (user && open) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || '',
        permissions: user.permissions || [],
        status: user.status || 'active',
      })
      setActiveTab(0)
      setErrors({})
    }
  }, [user, open])

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handlePermissionToggle = (permission) => {
    const newPermissions = formData.permissions.includes(permission)
      ? formData.permissions.filter(p => p !== permission)
      : [...formData.permissions, permission]
    
    setFormData(prev => ({ ...prev, permissions: newPermissions }))
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
    if (!formData.role) newErrors.role = 'Role is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      const userData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role,
        permissions: formData.permissions,
        status: formData.status,
      }

      await dispatch(updateUser({ id: user._id, userData })).unwrap()
      showSnackbar('User updated successfully')
      onClose()
    } catch (error) {
      showSnackbar(error, 'error')
    }
  }

  const getSelectedRole = () => {
    return roles.find(role => role.id === formData.role)
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const renderTabContent = (tab) => {
    switch (tab) {
      case 0:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                error={!!errors.firstName}
                helperText={errors.firstName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                error={!!errors.lastName}
                helperText={errors.lastName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={user?.username || ''}
                disabled
                helperText="Username cannot be changed"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                value={user?.email || ''}
                disabled
                helperText="Email cannot be changed"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="locked">Locked</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => handleInputChange('role', e.target.value)}
                  error={!!errors.role}
                >
                  {roles.map((role) => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        )

      case 1:
        return (
          <PermissionManager
            selectedRole={formData.role}
            userPermissions={formData.permissions}
            onPermissionsChange={(newPermissions) => handleInputChange('permissions', newPermissions)}
            roles={roles}
            groupedPermissions={groupedPermissions}
            disabled={isUpdating}
          />
        )

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Account Information
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">User ID</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?._id}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Created</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.createdAt ? (() => {
                    try {
                      const date = new Date(user.createdAt)
                      return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleDateString()
                    } catch (error) {
                      return 'Invalid Date'
                    }
                  })() : 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Last Updated</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.updatedAt ? (() => {
                    try {
                      const date = new Date(user.updatedAt)
                      return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleDateString()
                    } catch (error) {
                      return 'Invalid Date'
                    }
                  })() : 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Last Login</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.lastLogin ? (() => {
                    try {
                      const date = new Date(user.lastLogin)
                      return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleDateString()
                    } catch (error) {
                      return 'Invalid Date'
                    }
                  })() : 'Never'}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">MFA Status</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.mfaEnabled ? 'Enabled' : 'Disabled'}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">Login Attempts</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.loginAttempts || 0} failed attempts
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )

      default:
        return null
    }
  }

  if (!user) return null

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Edit User: {user.firstName} {user.lastName}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Basic Info" />
            <Tab label="Permissions" />
            <Tab label="Account Details" />
          </Tabs>
        </Box>

        {renderTabContent(activeTab)}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={isUpdating}>
          Cancel
        </Button>
        
        {activeTab < 2 && (
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={isUpdating}
          >
            {isUpdating ? 'Updating...' : 'Update User'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default EditUserDialog

import React from 'react'
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress,
} from '@mui/material'
import {
  People,
  PersonAdd,
  Security,
  Business,
  AdminPanelSettings,
  Refresh,
} from '@mui/icons-material'
import { format } from 'date-fns'

const UsersStatistics = ({ stats, isLoading, onRefresh }) => {
  if (isLoading && !stats) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  if (!stats) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            No statistics available
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={onRefresh}
          >
            Refresh
          </Button>
        </CardContent>
      </Card>
    )
  }

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin':
        return <AdminPanelSettings />
      case 'security_analyst':
        return <Security />
      case 'compliance_officer':
        return <Business />
      case 'executive':
        return <Business />
      default:
        return <People />
    }
  }

  const getRoleDisplayName = (role) => {
    const roleMap = {
      admin: 'System Administrator',
      security_analyst: 'Security Analyst',
      compliance_officer: 'Compliance Officer',
      executive: 'Executive',
    }
    return roleMap[role] || role
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'default'
      case 'locked':
        return 'error'
      default:
        return 'default'
    }
  }

  const calculatePercentage = (value, total) => {
    return total > 0 ? Math.round((value / total) * 100) : 0
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">User Statistics</Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={onRefresh}
          disabled={isLoading}
        >
          Refresh
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Total Users Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <People />
                </Avatar>
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.totalUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Users
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Users Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <PersonAdd />
                </Avatar>
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.statusDistribution.active}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Users
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Inactive Users Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'grey.500', mr: 2 }}>
                  <People />
                </Avatar>
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.statusDistribution.inactive}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Inactive Users
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Locked Users Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                  <People />
                </Avatar>
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.statusDistribution.locked}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Locked Users
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Status Distribution Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Status Distribution
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Active</Typography>
                  <Typography variant="body2">
                    {stats.statusDistribution.active} ({calculatePercentage(stats.statusDistribution.active, stats.totalUsers)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={calculatePercentage(stats.statusDistribution.active, stats.totalUsers)}
                  color="success"
                  sx={{ mb: 2 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Inactive</Typography>
                  <Typography variant="body2">
                    {stats.statusDistribution.inactive} ({calculatePercentage(stats.statusDistribution.inactive, stats.totalUsers)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={calculatePercentage(stats.statusDistribution.inactive, stats.totalUsers)}
                  color="inherit"
                  sx={{ mb: 2 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Locked</Typography>
                  <Typography variant="body2">
                    {stats.statusDistribution.locked} ({calculatePercentage(stats.statusDistribution.locked, stats.totalUsers)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={calculatePercentage(stats.statusDistribution.locked, stats.totalUsers)}
                  color="error"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Role Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Role Distribution
              </Typography>
              
              <List>
                {Object.entries(stats.roleDistribution).map(([role, count]) => (
                  <ListItem key={role} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.light' }}>
                        {getRoleIcon(role)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={getRoleDisplayName(role)}
                      secondary={`${count} users (${calculatePercentage(count, stats.totalUsers)}%)`}
                    />
                    <Chip
                      label={count}
                      size="small"
                      variant="outlined"
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Users */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recently Added Users
              </Typography>
              
              {stats.recentUsers && stats.recentUsers.length > 0 ? (
                <List>
                  {stats.recentUsers.map((user) => (
                    <ListItem key={user._id} sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar>
                          {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={`${user.firstName} ${user.lastName}`}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {user.email} • @{user.username}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Added {(() => {
                                try {
                                  if (!user.createdAt) return 'N/A'
                                  const date = new Date(user.createdAt)
                                  if (isNaN(date.getTime())) return 'Invalid Date'
                                  return format(date, 'MMM dd, yyyy')
                                } catch (error) {
                                  console.warn('Error formatting date in UsersStatistics:', user.createdAt, error)
                                  return 'Invalid Date'
                                }
                              })()}
                            </Typography>
                          </Box>
                        }
                      />
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Chip
                          label={getRoleDisplayName(user.role)}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          label={user.status}
                          size="small"
                          color={getStatusColor(user.status)}
                        />
                      </Box>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No recent users found
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default UsersStatistics

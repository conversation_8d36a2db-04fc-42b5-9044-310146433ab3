import api from './api';

/**
 * Notification API service
 */
class NotificationApiService {
  /**
   * Get user notifications with filtering and pagination
   */
  async getNotifications(params = {}) {
    const {
      page = 1,
      limit = 20,
      category,
      type,
      read,
      severity,
    } = params;

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (category) queryParams.append('category', category);
    if (type) queryParams.append('type', type);
    if (read !== undefined) queryParams.append('read', read.toString());
    if (severity) queryParams.append('severity', severity);

    const response = await api.get(`/notifications?${queryParams.toString()}`);
    return response.data;
  }

  /**
   * Get count of unread notifications
   */
  async getUnreadCount() {
    const response = await api.get('/notifications/unread-count');
    return response.data.data.count;
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats() {
    const response = await api.get('/notifications/stats');
    return response.data.data;
  }

  /**
   * Get a specific notification
   */
  async getNotification(id) {
    const response = await api.get(`/notifications/${id}`);
    return response.data.data.notification;
  }

  /**
   * Mark notifications as read
   */
  async markAsRead(notificationIds = null) {
    const response = await api.put('/notifications/mark-read', {
      notificationIds,
    });
    return response.data.data;
  }

  /**
   * Mark a specific notification as read
   */
  async markNotificationAsRead(id) {
    const response = await api.put(`/notifications/${id}/read`);
    return response.data.data.notification;
  }

  /**
   * Delete notifications
   */
  async deleteNotifications(notificationIds) {
    const response = await api.delete('/notifications', {
      data: { notificationIds },
    });
    return response.data.data;
  }

  /**
   * Delete a specific notification
   */
  async deleteNotification(id) {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  }

  /**
   * Create a notification (admin only)
   */
  async createNotification(notificationData) {
    const response = await api.post('/notifications', notificationData);
    return response.data.data.notification;
  }

  /**
   * Create bulk notifications (admin only)
   */
  async createBulkNotifications(notifications) {
    const response = await api.post('/notifications/bulk', { notifications });
    return response.data.data;
  }

  /**
   * Get notifications by category
   */
  async getNotificationsByCategory(category, params = {}) {
    return this.getNotifications({ ...params, category });
  }

  /**
   * Get unread notifications
   */
  async getUnreadNotifications(params = {}) {
    return this.getNotifications({ ...params, read: false });
  }

  /**
   * Get notifications by type
   */
  async getNotificationsByType(type, params = {}) {
    return this.getNotifications({ ...params, type });
  }

  /**
   * Get notifications by severity
   */
  async getNotificationsBySeverity(severity, params = {}) {
    return this.getNotifications({ ...params, severity });
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead() {
    return this.markAsRead();
  }

  /**
   * Get recent notifications (last 24 hours)
   */
  async getRecentNotifications(limit = 10) {
    return this.getNotifications({ limit, page: 1 });
  }

  /**
   * Search notifications by text
   */
  async searchNotifications(searchText, params = {}) {
    // Note: This would require backend implementation of search
    // For now, we'll get all notifications and filter client-side
    const result = await this.getNotifications(params);
    
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      result.data.notifications = result.data.notifications.filter(notification =>
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower)
      );
    }
    
    return result;
  }

  /**
   * Get notification counts by category
   */
  async getNotificationCountsByCategory() {
    const stats = await this.getNotificationStats();
    return stats.byCategory;
  }

  /**
   * Get notification counts by type
   */
  async getNotificationCountsByType() {
    const stats = await this.getNotificationStats();
    return stats.byType;
  }

  /**
   * Batch operations helper
   */
  async batchOperation(operation, notificationIds) {
    switch (operation) {
      case 'markRead':
        return this.markAsRead(notificationIds);
      case 'delete':
        return this.deleteNotifications(notificationIds);
      default:
        throw new Error(`Unknown batch operation: ${operation}`);
    }
  }

  /**
   * Get notification preferences from user settings
   */
  getNotificationPreferences(user) {
    return user?.preferences?.notifications || {
      email: true,
      inApp: true,
      alerts: {
        critical: true,
        high: true,
        medium: false,
        low: false,
      },
      digest: {
        enabled: false,
        frequency: 'weekly',
        time: '09:00',
      },
    };
  }

  /**
   * Check if notification should be shown based on user preferences
   */
  shouldShowNotification(notification, userPreferences) {
    // Check if in-app notifications are enabled
    if (userPreferences.inApp === false) {
      return false;
    }

    // Check category-specific preferences
    if (notification.category === 'alert' && userPreferences.alerts) {
      const severity = notification.severity || 'medium';
      return userPreferences.alerts[severity] !== false;
    }

    return true;
  }

  /**
   * Format notification for display
   */
  formatNotification(notification) {
    return {
      ...notification,
      timeAgo: this.getTimeAgo(notification.createdAt),
      isRecent: this.isRecent(notification.createdAt),
      displayType: this.getDisplayType(notification.type),
      displaySeverity: this.getDisplaySeverity(notification.severity),
    };
  }

  /**
   * Get time ago string
   */
  getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return time.toLocaleDateString();
  }

  /**
   * Check if notification is recent (within last hour)
   */
  isRecent(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = (now - time) / (1000 * 60 * 60);
    return diffInHours <= 1;
  }

  /**
   * Get display type for notification
   */
  getDisplayType(type) {
    const types = {
      info: 'Information',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
    };
    return types[type] || 'Information';
  }

  /**
   * Get display severity for notification
   */
  getDisplaySeverity(severity) {
    const severities = {
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      critical: 'Critical',
    };
    return severities[severity] || 'Medium';
  }
}

// Create singleton instance
const notificationApi = new NotificationApiService();

export default notificationApi;

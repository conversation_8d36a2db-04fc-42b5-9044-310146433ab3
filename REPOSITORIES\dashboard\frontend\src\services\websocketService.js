class WebSocketService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.listeners = new Map();
    this.messageQueue = [];
    this.heartbeatInterval = null;
    this.token = null;
    this.authenticated = false;
  }

  /**
   * Get WebSocket URL based on environment
   */
  getWebSocketUrl() {
    // Check if we're in development or production
    const isDev = import.meta.env.DEV;
    const hostname = window.location.hostname;
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const port = window.location.port;

    if (isDev) {
      // Development: connect through the proxy on the same port as the frontend
      if (port) {
        return `${protocol}//${hostname}:${port}/ws`;
      } else {
        return `${protocol}//${hostname}/ws`;
      }
    } else {
      // Production: use relative URL for nginx proxy
      if (port && port !== '80' && port !== '443') {
        return `${protocol}//${hostname}:${port}/ws`;
      } else {
        return `${protocol}//${hostname}/ws`;
      }
    }
  }

  /**
   * Connect to WebSocket server
   */
  connect(token) {
    if (this.isConnected || this.isConnecting) {
      return Promise.resolve();
    }

    this.token = token;
    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        const wsUrl = this.getWebSocketUrl();
        console.log('Connecting to WebSocket:', wsUrl);

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;

          // Authenticate immediately after connection
          if (this.token) {
            this.authenticate(this.token);
          }

          // Start heartbeat
          this.startHeartbeat();

          // Process queued messages
          this.processMessageQueue();

          // Notify listeners
          this.emit('connected');

          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnected = false;
          this.isConnecting = false;
          this.authenticated = false;

          this.stopHeartbeat();
          this.emit('disconnected', { code: event.code, reason: event.reason });

          // Attempt to reconnect if not a clean close
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.isConnected = false;
    this.authenticated = false;
    this.stopHeartbeat();
  }

  /**
   * Authenticate with the server
   */
  authenticate(token) {
    this.token = token;
    this.send({
      type: 'auth',
      token: token,
    });
  }

  /**
   * Send message to server
   */
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for later
      this.messageQueue.push(message);
    }
  }

  /**
   * Subscribe to a channel
   */
  subscribe(channel) {
    this.send({
      type: 'subscribe',
      channel: channel,
    });
  }

  /**
   * Unsubscribe from a channel
   */
  unsubscribe(channel) {
    this.send({
      type: 'unsubscribe',
      channel: channel,
    });
  }

  /**
   * Mark notification as read
   */
  markNotificationRead(notificationId) {
    this.send({
      type: 'mark_notification_read',
      notificationId: notificationId,
    });
  }

  /**
   * Handle incoming messages
   */
  handleMessage(data) {
    switch (data.type) {
      case 'welcome':
        console.log('WebSocket welcome message received');
        break;

      case 'authenticated':
        console.log('WebSocket authenticated for user:', data.user?.username);
        this.authenticated = true;
        this.emit('authenticated', data.user);
        break;

      case 'auth_error':
        console.error('WebSocket authentication failed:', data.message);
        this.authenticated = false;
        this.emit('auth_error', data.message);
        break;

      case 'pong':
        // Heartbeat response
        break;

      case 'notification':
        console.log('New notification received:', data.data);
        this.emit('notification', data.data);
        break;

      case 'message':
        this.emit('message', data.data);
        break;

      case 'broadcast':
        console.log(`Broadcast received on channel ${data.channel}:`, data.data);
        this.emit('broadcast', data.channel, data.data);
        this.emit(`broadcast:${data.channel}`, data.data);

        // Handle specific AI analysis broadcasts
        if (data.channel.startsWith('ai:')) {
          this.handleAIBroadcast(data.channel, data.data);
        }
        break;

      case 'subscribed':
        console.log('Subscribed to channel:', data.channel);
        this.emit('subscribed', data.channel);
        break;

      case 'unsubscribed':
        console.log('Unsubscribed from channel:', data.channel);
        this.emit('unsubscribed', data.channel);
        break;

      case 'error':
        console.error('WebSocket server error:', data.message);
        this.emit('error', data.message);
        break;

      default:
        console.log('Unknown WebSocket message type:', data.type);
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' });
      }
    }, 30000); // Send ping every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (!this.isConnected && this.token) {
        this.connect(this.token).catch(error => {
          console.error('Reconnect failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Process queued messages
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, ...args) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error('Error in WebSocket event listener:', error);
        }
      });
    }
  }

  /**
   * Handle AI-specific broadcasts
   */
  handleAIBroadcast(channel, data) {
    switch (channel) {
      case 'ai:analysis-update':
        this.emit('ai:analysis-update', data);
        break;

      case 'ai:analysis-complete':
        this.emit('ai:analysis-complete', data);
        // Also emit a general insights update
        this.emit('ai:insights-updated', data);
        break;

      case 'ai:analysis-started':
        this.emit('ai:analysis-started', data);
        break;

      case 'ai:analysis-failed':
        this.emit('ai:analysis-failed', data);
        break;

      case 'ai:high-risk-alert':
        this.emit('ai:high-risk-alert', data);
        // Also emit as a notification
        this.emit('notification', {
          type: 'warning',
          title: 'High Risk Analysis Alert',
          message: `Analysis detected ${data.riskLevel} risk level with ${data.summary.threats} threats and ${data.summary.anomalies} anomalies`,
          data: data
        });
        break;

      case 'ai:insights-updated':
        this.emit('ai:insights-updated', data);
        break;

      case 'ai:threat-detected':
        this.emit('ai:threat-detected', data);
        break;

      case 'ai:anomaly-detected':
        this.emit('ai:anomaly-detected', data);
        break;

      case 'ai:threat-level-changed':
        this.emit('ai:threat-level-changed', data);
        break;

      default:
        console.log(`Unhandled AI broadcast: ${channel}`, data);
    }
  }

  /**
   * Subscribe to AI analysis updates
   */
  subscribeToAIUpdates() {
    this.subscribe('ai:analysis-update');
    this.subscribe('ai:analysis-complete');
    this.subscribe('ai:analysis-started');
    this.subscribe('ai:analysis-failed');
    this.subscribe('ai:high-risk-alert');
    this.subscribe('ai:insights-updated');
    this.subscribe('ai:threat-detected');
    this.subscribe('ai:anomaly-detected');
    this.subscribe('ai:threat-level-changed');
  }

  /**
   * Unsubscribe from AI analysis updates
   */
  unsubscribeFromAIUpdates() {
    this.unsubscribe('ai:analysis-update');
    this.unsubscribe('ai:analysis-complete');
    this.unsubscribe('ai:analysis-started');
    this.unsubscribe('ai:analysis-failed');
    this.unsubscribe('ai:high-risk-alert');
    this.unsubscribe('ai:insights-updated');
    this.unsubscribe('ai:threat-detected');
    this.unsubscribe('ai:anomaly-detected');
    this.unsubscribe('ai:threat-level-changed');
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      authenticated: this.authenticated,
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks
export const fetchDashboardOverview = createAsyncThunk(
  'dashboard/fetchOverview',
  async (timeRange = '24h', { rejectWithValue }) => {
    try {
      const response = await api.get(`/dashboards/overview?timeRange=${timeRange}`)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard overview')
    }
  }
)

export const fetchSystemHealth = createAsyncThunk(
  'dashboard/fetchSystemHealth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboards/system-health')
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch system health')
    }
  }
)

export const fetchAgentStatus = createAsyncThunk(
  'dashboard/fetchAgentStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboards/agent-status')
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch agent status')
    }
  }
)

export const fetchRecentAlerts = createAsyncThunk(
  'dashboard/fetchRecentAlerts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboards/recent-alerts')
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch recent alerts')
    }
  }
)

export const fetchUserActivity = createAsyncThunk(
  'dashboard/fetchUserActivity',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboards/user-activity')
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user activity')
    }
  }
)

const initialState = {
  overview: null,
  systemHealth: null,
  agentStatus: null,
  recentAlerts: null,
  userActivity: null,
  isLoading: false,
  isLoadingHealth: false,
  isLoadingAgents: false,
  isLoadingAlerts: false,
  isLoadingUserActivity: false,
  error: null,
  lastUpdated: null,
}

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setLastUpdated: (state) => {
      state.lastUpdated = new Date().toISOString()
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch dashboard overview
      .addCase(fetchDashboardOverview.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchDashboardOverview.fulfilled, (state, action) => {
        state.isLoading = false
        state.overview = action.payload.data
        state.lastUpdated = new Date().toISOString()
      })
      .addCase(fetchDashboardOverview.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Fetch system health
      .addCase(fetchSystemHealth.pending, (state) => {
        state.isLoadingHealth = true
      })
      .addCase(fetchSystemHealth.fulfilled, (state, action) => {
        state.isLoadingHealth = false
        state.systemHealth = action.payload.data
      })
      .addCase(fetchSystemHealth.rejected, (state, action) => {
        state.isLoadingHealth = false
        state.error = action.payload
      })

      // Fetch agent status
      .addCase(fetchAgentStatus.pending, (state) => {
        state.isLoadingAgents = true
      })
      .addCase(fetchAgentStatus.fulfilled, (state, action) => {
        state.isLoadingAgents = false
        state.agentStatus = action.payload.data
      })
      .addCase(fetchAgentStatus.rejected, (state, action) => {
        state.isLoadingAgents = false
        state.error = action.payload
      })

      // Fetch recent alerts
      .addCase(fetchRecentAlerts.pending, (state) => {
        state.isLoadingAlerts = true
      })
      .addCase(fetchRecentAlerts.fulfilled, (state, action) => {
        state.isLoadingAlerts = false
        state.recentAlerts = action.payload.data
      })
      .addCase(fetchRecentAlerts.rejected, (state, action) => {
        state.isLoadingAlerts = false
        state.error = action.payload
      })

      // Fetch user activity
      .addCase(fetchUserActivity.pending, (state) => {
        state.isLoadingUserActivity = true
      })
      .addCase(fetchUserActivity.fulfilled, (state, action) => {
        state.isLoadingUserActivity = false
        state.userActivity = action.payload.data
      })
      .addCase(fetchUserActivity.rejected, (state, action) => {
        state.isLoadingUserActivity = false
        state.error = action.payload
      })
  },
})

export const { clearError, setLastUpdated } = dashboardSlice.actions
export default dashboardSlice.reducer

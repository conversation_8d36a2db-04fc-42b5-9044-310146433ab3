const mongoose = require('mongoose');
const { faker } = require('@faker-js/faker');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog';

// Log levels and their weights (higher weight = more likely)
const LOG_LEVELS = [
  { level: 'info', weight: 50 },
  { level: 'warn', weight: 25 },
  { level: 'error', weight: 15 },
  { level: 'debug', weight: 8 },
  { level: 'fatal', weight: 2 }
];

// Source types
const SOURCE_TYPES = [
  'system', 'application', 'security', 'network', 'database', 'web_server'
];

// Sample log messages for different scenarios
const LOG_MESSAGES = {
  normal: [
    'User login successful',
    'Database connection established',
    'File uploaded successfully',
    'Cache cleared',
    'Backup completed',
    'Service started',
    'Configuration updated',
    'Request processed',
    'Session created',
    'Data synchronized'
  ],
  security: [
    'Failed login attempt from IP {ip}',
    'Multiple failed login attempts detected',
    'Suspicious file access attempt',
    'Unauthorized access denied',
    'Privilege escalation attempt blocked',
    'Malware signature detected',
    'Port scan detected from {ip}',
    'Brute force attack in progress',
    'Data exfiltration attempt blocked',
    'Admin access granted to user {user}'
  ],
  errors: [
    'Database connection failed',
    'Service unavailable',
    'Memory allocation failed',
    'Disk space critical',
    'Network timeout occurred',
    'Authentication service down',
    'File not found error',
    'Permission denied',
    'Invalid configuration detected',
    'System overload detected'
  ],
  anomalies: [
    'Unusual network traffic pattern detected',
    'Process injection attempt detected',
    'DLL injection suspicious activity',
    'Command line injection detected',
    'Suspicious process creation: powershell.exe',
    'Large file transfer at unusual time',
    'Directory enumeration detected',
    'Tool transfer suspicious activity',
    'Thread execution hijacking detected',
    'Password spray attack detected'
  ]
};

function getWeightedRandom(items) {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of items) {
    random -= item.weight;
    if (random <= 0) {
      return item.level;
    }
  }
  return items[0].level;
}

function generateLogMessage(category = 'normal') {
  const messages = LOG_MESSAGES[category];
  let message = faker.helpers.arrayElement(messages);
  
  // Replace placeholders
  message = message.replace('{ip}', faker.internet.ip());
  message = message.replace('{user}', faker.internet.userName());
  
  return message;
}

function generateLog(timestamp, forceCategory = null) {
  const categories = ['normal', 'security', 'errors', 'anomalies'];
  const categoryWeights = [70, 15, 10, 5]; // Normal logs are most common
  
  let category;
  if (forceCategory) {
    category = forceCategory;
  } else {
    const random = Math.random() * 100;
    let cumulative = 0;
    for (let i = 0; i < categories.length; i++) {
      cumulative += categoryWeights[i];
      if (random <= cumulative) {
        category = categories[i];
        break;
      }
    }
  }
  
  const logLevel = getWeightedRandom(LOG_LEVELS);
  const sourceType = faker.helpers.arrayElement(SOURCE_TYPES);
  const message = generateLogMessage(category);
  
  return {
    timestamp: timestamp,
    logLevel: logLevel,
    message: message,
    sourceType: sourceType,
    source: `${sourceType}-${faker.number.int({ min: 1, max: 10 })}`,
    hostname: faker.internet.domainName(),
    severity: faker.number.int({ min: 1, max: 10 }),
    userId: faker.string.uuid(),
    sessionId: faker.string.uuid(),
    ipAddress: faker.internet.ip(),
    userAgent: faker.internet.userAgent(),
    metadata: {
      category: category,
      generated: true,
      version: '1.0'
    }
  };
}

async function generateTestLogs(count = 1000) {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const db = mongoose.connection.db;
    const logsCollection = db.collection('logs');

    console.log(`Generating ${count} test logs...`);
    
    const logs = [];
    const now = new Date();
    
    // Generate logs over the past 24 hours
    for (let i = 0; i < count; i++) {
      // Random timestamp within the last 24 hours
      const hoursAgo = Math.random() * 24;
      const timestamp = new Date(now.getTime() - (hoursAgo * 60 * 60 * 1000));
      
      // Force some specific categories for testing
      let forceCategory = null;
      if (i < 50) forceCategory = 'security'; // First 50 are security logs
      if (i >= 50 && i < 80) forceCategory = 'anomalies'; // Next 30 are anomalies
      if (i >= 80 && i < 120) forceCategory = 'errors'; // Next 40 are errors
      
      const log = generateLog(timestamp, forceCategory);
      logs.push(log);
    }

    // Insert logs in batches
    const batchSize = 100;
    for (let i = 0; i < logs.length; i += batchSize) {
      const batch = logs.slice(i, i + batchSize);
      await logsCollection.insertMany(batch);
      console.log(`Inserted batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(logs.length / batchSize)}`);
    }

    console.log(`Successfully generated and inserted ${count} test logs`);
    
    // Print some statistics
    const stats = await logsCollection.aggregate([
      {
        $group: {
          _id: '$logLevel',
          count: { $sum: 1 }
        }
      }
    ]).toArray();
    
    console.log('\nLog level distribution:');
    stats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count}`);
    });

  } catch (error) {
    console.error('Error generating test logs:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run if called directly
if (require.main === module) {
  const count = parseInt(process.argv[2]) || 1000;
  generateTestLogs(count);
}

module.exports = { generateTestLogs };

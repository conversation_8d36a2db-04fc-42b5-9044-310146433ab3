{"name": "exlog-dashboard", "version": "1.0.0", "description": "ExLog: Cybersecurity Log Management Dashboard", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:hub": "docker-compose -f docker-compose.hub.yml up -d", "setup": "npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "seed:dashboard": "cd backend && node scripts/seedDashboardData.js", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "release": "./scripts/release.sh", "release:patch": "./scripts/release.sh patch", "release:minor": "./scripts/release.sh minor", "release:major": "./scripts/release.sh major", "docker:publish": "./.dockerhub/build-and-push.sh"}, "keywords": ["cybersecurity", "log-management", "dashboard", "security-monitoring", "siem"], "author": "ExLog Development Team", "license": "MIT", "dependencies": {"@faker-js/faker": "^8.0.0", "mongoose": "^7.5.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend", "agent"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/exlog/dashboard.git"}, "bugs": {"url": "https://github.com/exlog/dashboard/issues"}, "homepage": "https://github.com/exlog/dashboard#readme"}
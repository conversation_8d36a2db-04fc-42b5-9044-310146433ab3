# ExLog Dashboard Quick Start Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Environment
NODE_ENV=production

# Security Configuration - CHANGE THESE IN PRODUCTION!
JWT_SECRET=your-super-secret-jwt-key-change-in-production-please
MONGODB_PASSWORD=password

# Default Admin Credentials (CHANGE AFTER FIRST LOGIN!)
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=Admin123!

# Network Configuration
# For development: Use * to allow all origins (enables external IP access)
# For production: Replace * with specific allowed origins
CORS_ORIGIN=*

# Database Configuration
MONGODB_URI=*************************************************************

# Logging Configuration
LOG_LEVEL=info
LOG_RETENTION_SECONDS=7776000  # 90 days
ALERT_RETENTION_SECONDS=31536000  # 365 days

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX=1000

# Auto-cleanup (recommended for production)
ENABLE_AUTO_DELETE=true

# Email Configuration (optional)
# RESEND_API_KEY=your-resend-api-key
# EMAIL_FROM=<EMAIL>

# External Storage (optional)
# EXTERNAL_STORAGE_TYPE=s3
# S3_BUCKET=your-logs-bucket
# S3_REGION=us-east-1
# S3_ACCESS_KEY_ID=your-access-key
# S3_SECRET_ACCESS_KEY=your-secret-key

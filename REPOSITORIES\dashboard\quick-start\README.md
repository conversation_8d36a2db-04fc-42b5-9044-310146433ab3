# ExLog Dashboard Quick Start

Get ExLog Dashboard running in 5 minutes with pre-built Docker images.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- 2GB+ RAM available
- 5GB+ disk space

## Quick Installation

1. **Download the configuration:**
   ```bash
   curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml -o docker-compose.yml
   curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/.env.template -o .env
   curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/nginx.conf -o nginx.conf
   mkdir -p scripts
   curl -fsSL https://gitlab.com/spr888/dashboard/-/raw/main/scripts/mongo-init.js -o scripts/mongo-init.js
   ```

2. **Start ExLog Dashboard:**
   ```bash
   docker compose up -d
   ```

3. **Access the dashboard:**
   - **HTTPS (Recommended)**: https://localhost:8443
   - **HTTP**: http://localhost:8080 (automatically redirects to HTTPS)
   - **Network Access**: Also accessible via your system's IP address (e.g., https://*************:8443)
   - **From Other Devices**: Access from other PCs on the same network using your computer's IP with HTTPS
   - Default login: `<EMAIL>` / `Admin123!`
   - **Note**: Browser will show security warning for self-signed certificate - click "Advanced" and "Proceed" to continue

## HTTPS/SSL Features

ExLog Dashboard automatically generates self-signed SSL certificates when containers start:

**Automatic SSL Features:**
- Self-signed certificates generated automatically on first startup
- HTTPS enabled by default on port 8443
- HTTP traffic automatically redirects to HTTPS
- Certificates include Subject Alternative Names (SANs) for localhost and private IP ranges
- No manual configuration required

**Accessing via HTTPS:**
- Primary: https://localhost:8443
- Network: https://your-ip-address:8443
- Browser will show security warning (expected for self-signed certificates)
- Click "Advanced" → "Proceed to localhost" to continue

**Certificate Details:**
- Valid for 365 days
- Automatically regenerated if expired
- Stored in persistent Docker volume
- Includes SANs for localhost, *.localhost, exlog.local, *.exlog.local, and common private IP ranges

## What's Included

- **Frontend**: React-based web interface
- **Backend**: Node.js API server
- **WebSocket**: Real-time communication
- **AI Service**: Security insights and analysis
- **MongoDB**: Database for logs and configuration
- **Nginx**: Reverse proxy and load balancer

## Configuration

Edit the `.env` file to customize your installation:

```bash
# Security (IMPORTANT: Change these!)
JWT_SECRET=your-secure-secret-here
MONGODB_PASSWORD=your-secure-password

# Network
CORS_ORIGIN=https://your-domain.com

# Data retention
LOG_RETENTION_SECONDS=7776000  # 90 days
```

## Useful Commands

```bash
# View logs
docker compose logs -f

# Stop services
docker compose down

# Restart services
docker compose restart

# Update to latest version
docker compose pull && docker compose up -d

# Check service status
docker compose ps
```

## Troubleshooting

### Services not starting
```bash
# Check Docker daemon
docker info

# Check service logs
docker compose logs [service_name]

# Restart all services
docker compose restart
```

### Port conflicts
Edit `docker-compose.yml` and change the port mappings:
```yaml
ports:
  - "8081:80"  # Change 8080 to 8081
```

### Memory issues
Ensure you have at least 2GB RAM available:
```bash
# Check memory usage
docker stats

# Free up memory
docker system prune
```

## Next Steps

1. **Change default credentials** after first login
2. **Configure log sources** in the dashboard
3. **Set up alerting rules** for your environment
4. **Review security settings** in the `.env` file

## Full Documentation

- [Complete Installation Guide](../INSTALLATION.md)
- [Production Deployment Guide](../DEPLOYMENT.md)
- [Troubleshooting Guide](../docs/TROUBLESHOOTING.md)

## Support

- Issues: https://gitlab.com/spr888/dashboard/-/issues
- Documentation: https://gitlab.com/spr888/dashboard

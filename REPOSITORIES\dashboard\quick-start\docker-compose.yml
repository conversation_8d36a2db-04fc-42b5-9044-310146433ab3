version: '3.8'

services:
  # Frontend service using Docker Hub image
  frontend:
    image: jmason11/exlog-frontend:latest
    container_name: exlog-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=/api/v1
      - REACT_APP_WS_URL=/ws
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend service using Docker Hub image
  backend:
    image: jmason11/exlog-backend:latest
    container_name: exlog-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=*************************************************************
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production-please}
      - JWT_EXPIRES_IN=24h
      - CORS_ORIGIN=*
      - LOG_LEVEL=info
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX=1000
    volumes:
      - exlog_logs:/app/logs
      - exlog_log_storage:/app/logs/storage
      - exlog_log_archive:/app/logs/archive
    depends_on:
      - mongodb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://127.0.0.1:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # WebSocket service using Docker Hub image
  websocket:
    image: jmason11/exlog-websocket:latest
    container_name: exlog-websocket
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - WEBSOCKET_PORT=5001
      - MONGODB_URI=*************************************************************
    depends_on:
      - mongodb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://127.0.0.1:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # AI Service using Docker Hub image
  ai-insights:
    image: jmason11/exlog-ai-service:latest
    container_name: exlog-ai-service
    ports:
      - "5002:5002"
    environment:
      - NODE_ENV=production
      - PORT=5002
      - MONGODB_URI=*************************************************************
    depends_on:
      - mongodb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://127.0.0.1:5002/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB database
  mongodb:
    image: mongo:7.0
    container_name: exlog-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=exlog
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx reverse proxy with SSL
  nginx:
    image: jmason11/exlog-nginx:latest
    container_name: exlog-nginx
    ports:
      - "8080:80"   # HTTP (redirects to HTTPS)
      - "8443:443"  # HTTPS
    volumes:
      - ssl_certs:/etc/ssl/exlog  # Persist certificates
    depends_on:
      - frontend
      - backend
      - websocket
      - ai-insights
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
  ssl_certs:
  exlog_logs:
    driver: local
  exlog_log_storage:
    driver: local
  exlog_log_archive:
    driver: local

networks:
  default:
    name: exlog-network

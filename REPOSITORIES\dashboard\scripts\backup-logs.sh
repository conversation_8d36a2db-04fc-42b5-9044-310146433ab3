#!/bin/bash

# ExLog Automated Backup Script
# This script creates automated backups of logs and can be run via cron

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_BASE_DIR="${BACKUP_DIR:-$PROJECT_DIR/backups}"
LOG_FILE="${LOG_FILE:-$BACKUP_BASE_DIR/backup.log}"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
API_URL="${API_URL:-http://localhost:5000/api/v1}"
ADMIN_TOKEN="${ADMIN_TOKEN:-}"

# Create backup directory
mkdir -p "$BACKUP_BASE_DIR"
mkdir -p "$(dirname "$LOG_FILE")"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        error_exit "Docker is not running or not accessible"
    fi
}

# Check if ExLog is running
check_exlog() {
    if ! curl -s "$API_URL/health" >/dev/null 2>&1; then
        error_exit "ExLog API is not accessible at $API_URL"
    fi
}

# Get admin token if not provided
get_admin_token() {
    if [ -z "$ADMIN_TOKEN" ]; then
        log "WARN" "No admin token provided. Attempting to extract from container..."
        
        # Try to get token from running container
        local container_name="dashboard-backend-1"
        if docker ps --format "table {{.Names}}" | grep -q "$container_name"; then
            # This is a placeholder - in real implementation, you'd need proper authentication
            log "WARN" "Please provide ADMIN_TOKEN environment variable for API access"
            return 1
        else
            error_exit "Backend container not found and no admin token provided"
        fi
    fi
}

# Create API backup
create_api_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_dir="$BACKUP_BASE_DIR/api_backup_$timestamp"
    
    log "INFO" "Creating API backup..."
    
    if [ -n "$ADMIN_TOKEN" ]; then
        # Use API to create backup
        local response=$(curl -s -X POST \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"includeArchived": true, "compress": true}' \
            "$API_URL/settings/system/create-backup")
        
        if echo "$response" | grep -q '"status":"success"'; then
            log "INFO" "API backup created successfully"
            echo "$response" > "$backup_dir/api_response.json"
        else
            log "ERROR" "API backup failed: $response"
        fi
    else
        log "WARN" "Skipping API backup - no admin token available"
    fi
}

# Create file system backup
create_filesystem_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_dir="$BACKUP_BASE_DIR/fs_backup_$timestamp"
    
    mkdir -p "$backup_dir"
    
    log "INFO" "Creating filesystem backup..."
    
    # Backup data directory
    if [ -d "$PROJECT_DIR/data" ]; then
        log "INFO" "Backing up data directory..."
        tar -czf "$backup_dir/data.tar.gz" -C "$PROJECT_DIR" data/ 2>/dev/null || {
            log "WARN" "Failed to backup data directory"
        }
    fi
    
    # Backup Docker volumes
    log "INFO" "Backing up Docker volumes..."
    
    # MongoDB data
    if docker volume ls | grep -q "dashboard_mongodb_data"; then
        docker run --rm \
            -v dashboard_mongodb_data:/data \
            -v "$backup_dir":/backup \
            alpine tar -czf /backup/mongodb_data.tar.gz -C /data . 2>/dev/null || {
            log "WARN" "Failed to backup MongoDB volume"
        }
    fi
    
    # Log volumes
    for volume in "dashboard_exlog_logs" "dashboard_exlog_log_storage" "dashboard_exlog_log_archive"; do
        if docker volume ls | grep -q "$volume"; then
            local volume_name=$(echo "$volume" | sed 's/dashboard_exlog_//')
            docker run --rm \
                -v "$volume":/data \
                -v "$backup_dir":/backup \
                alpine tar -czf "/backup/${volume_name}.tar.gz" -C /data . 2>/dev/null || {
                log "WARN" "Failed to backup volume: $volume"
            }
        fi
    done
    
    # Create backup manifest
    cat > "$backup_dir/manifest.json" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "type": "filesystem",
    "version": "1.0",
    "contents": [
        "data.tar.gz",
        "mongodb_data.tar.gz",
        "logs.tar.gz",
        "log_storage.tar.gz",
        "log_archive.tar.gz"
    ]
}
EOF
    
    log "INFO" "Filesystem backup created: $backup_dir"
}

# Clean up old backups
cleanup_old_backups() {
    log "INFO" "Cleaning up backups older than $RETENTION_DAYS days..."
    
    find "$BACKUP_BASE_DIR" -type d -name "*backup_*" -mtime +$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    # Clean up old log files
    find "$(dirname "$LOG_FILE")" -name "backup.log.*" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    log "INFO" "Cleanup completed"
}

# Rotate log file
rotate_log() {
    if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt 10485760 ]; then
        mv "$LOG_FILE" "${LOG_FILE}.$(date +%Y%m%d_%H%M%S)"
        touch "$LOG_FILE"
    fi
}

# Send notification (placeholder)
send_notification() {
    local status="$1"
    local message="$2"
    
    # This is a placeholder for notification integration
    # You can integrate with email, Slack, webhooks, etc.
    log "INFO" "Notification: [$status] $message"
}

# Main backup function
main() {
    local start_time=$(date +%s)
    
    log "INFO" "Starting ExLog backup process..."
    
    # Rotate log file if needed
    rotate_log
    
    # Pre-flight checks
    check_docker
    check_exlog
    
    # Get authentication if needed
    get_admin_token || log "WARN" "Continuing without API access"
    
    # Create backups
    create_filesystem_backup
    
    if [ -n "$ADMIN_TOKEN" ]; then
        create_api_backup
    fi
    
    # Cleanup old backups
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log "INFO" "Backup process completed in ${duration} seconds"
    send_notification "SUCCESS" "ExLog backup completed successfully"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "ExLog Backup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --dry-run          Show what would be backed up without doing it"
        echo "  --cleanup-only     Only perform cleanup of old backups"
        echo ""
        echo "Environment Variables:"
        echo "  BACKUP_DIR              Base directory for backups (default: ./backups)"
        echo "  BACKUP_RETENTION_DAYS   Days to keep backups (default: 30)"
        echo "  API_URL                 ExLog API URL (default: http://localhost:5000/api/v1)"
        echo "  ADMIN_TOKEN             Admin API token for authenticated backups"
        echo ""
        exit 0
        ;;
    --dry-run)
        log "INFO" "DRY RUN - No actual backup will be performed"
        log "INFO" "Would backup:"
        log "INFO" "  - Data directory: $PROJECT_DIR/data"
        log "INFO" "  - Docker volumes: mongodb_data, exlog_logs, exlog_log_storage, exlog_log_archive"
        log "INFO" "  - API backup (if token available)"
        exit 0
        ;;
    --cleanup-only)
        log "INFO" "Performing cleanup only..."
        cleanup_old_backups
        exit 0
        ;;
    "")
        # Run normal backup
        main
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

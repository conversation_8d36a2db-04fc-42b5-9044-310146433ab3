#!/bin/bash


set -euo pipefail

SSL_DIR="/etc/ssl/exlog"
CERT_FILE="$SSL_DIR/server.crt"
KEY_FILE="$SSL_DIR/server.key"
DAYS_VALID=365

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

create_ssl_directory() {
    log_info "Creating SSL directory..."
    mkdir -p "$SSL_DIR"
    chmod 755 "$SSL_DIR"
    log_success "SSL directory created: $SSL_DIR"
}

generate_certificate() {
    log_info "Generating self-signed SSL certificate..."
    
    if [[ -f "$CERT_FILE" && -f "$KEY_FILE" ]]; then
        if openssl x509 -in "$CERT_FILE" -checkend 86400 -noout 2>/dev/null; then
            log_info "Valid SSL certificate already exists, skipping generation"
            return 0
        else
            log_warning "Existing certificate is expired or invalid, regenerating..."
        fi
    fi

    openssl genrsa -out "$KEY_FILE" 2048

    openssl req -new -x509 -key "$KEY_FILE" -out "$CERT_FILE" -days "$DAYS_VALID" \
        -subj "/C=US/ST=Local/L=Local/O=ExLog/OU=Dashboard/CN=localhost" \
        -addext "subjectAltName=DNS:localhost,DNS:*.localhost,DNS:exlog.local,DNS:*.exlog.local,IP:127.0.0.1,IP:0.0.0.0,IP:**********/12,IP:***********/16,IP:10.0.0.0/8"

    chmod 644 "$CERT_FILE"
    chmod 600 "$KEY_FILE"

    log_success "Self-signed SSL certificate generated successfully"
    log_warning "This is a self-signed certificate - browsers will show security warnings"
    log_info "Certificate valid for $DAYS_VALID days"
    log_info "Certificate includes SANs for localhost and common private IP ranges"
}

main() {
    log_info "ExLog Dashboard - Self-Signed SSL Certificate Generator"
    log_info "====================================================="

    create_ssl_directory
    generate_certificate

    log_success "SSL certificate setup completed!"
    log_info "Certificate: $CERT_FILE"
    log_info "Private Key: $KEY_FILE"
    log_warning "Remember: Self-signed certificates will show browser warnings"
    log_info "You can accept the security warning to proceed with HTTPS"
}

main "$@"

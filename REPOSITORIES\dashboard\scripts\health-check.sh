#!/bin/bash


set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
HEALTH_LOG="/tmp/exlog-health-check.log"

FRONTEND_URL="${FRONTEND_URL:-http://localhost:3000}"
BACKEND_URL="${BACKEND_URL:-http://localhost:5000}"
WEBSOCKET_URL="${WEBSOCKET_URL:-http://localhost:5001}"
AI_SERVICE_URL="${AI_SERVICE_URL:-http://localhost:5002}"
NGINX_URL="${NGINX_URL:-http://localhost:8080}"
MONGODB_URL="${MONGODB_URL:-mongodb://localhost:27017}"

echo -e "${BLUE}ExLog Dashboard Health Check${NC}"
echo "==============================="
echo ""

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] [${level}] $message" | tee -a "$HEALTH_LOG"
}

TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

check_service() {
    local service_name="$1"
    local check_command="$2"
    local description="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "Checking $service_name... "
    
    if eval "$check_command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ PASS${NC}"
        log "INFO" "$service_name: $description - PASS"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗ FAIL${NC}"
        log "ERROR" "$service_name: $description - FAIL"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

check_docker() {
    echo -e "${BLUE}Docker Environment:${NC}"
    
    check_service "Docker Daemon" "docker info" "Docker daemon is running"
    check_service "Docker Compose" "docker compose version" "Docker Compose is available"
    
    if docker compose ps >/dev/null 2>&1; then
        local running_containers=$(docker compose ps --services --filter "status=running" | wc -l)
        local total_containers=$(docker compose ps --services | wc -l)
        
        if [ "$running_containers" -eq "$total_containers" ] && [ "$total_containers" -gt 0 ]; then
            echo -e "Container Status: ${GREEN}✓ All containers running ($running_containers/$total_containers)${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "Container Status: ${RED}✗ Some containers not running ($running_containers/$total_containers)${NC}"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    fi
    
    echo ""
}

check_services() {
    echo -e "${BLUE}Service Health:${NC}"
    
    check_service "Frontend" "curl -f -s --max-time 10 $FRONTEND_URL" "Frontend service is responding"
    
    check_service "Backend API" "curl -f -s --max-time 10 $BACKEND_URL/health" "Backend API health endpoint"
    
    check_service "WebSocket Service" "curl -f -s --max-time 10 $WEBSOCKET_URL/health" "WebSocket service health endpoint"
    
    check_service "AI Service" "curl -f -s --max-time 10 $AI_SERVICE_URL/health" "AI service health endpoint"
    
    check_service "Nginx Proxy" "curl -f -s --max-time 10 $NGINX_URL/health" "Nginx reverse proxy"
    
    echo ""
}

check_database() {
    echo -e "${BLUE}Database Health:${NC}"
    
    check_service "MongoDB Connection" "docker exec -it \$(docker compose ps -q mongodb) mongosh --eval 'db.adminCommand(\"ping\")'" "MongoDB is accessible"
    
    check_service "Database Collections" "docker exec -it \$(docker compose ps -q mongodb) mongosh exlog --eval 'db.stats()'" "ExLog database exists"
    
    echo ""
}

check_performance() {
    echo -e "${BLUE}Performance Metrics:${NC}"
    
    local memory_usage=$(docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" | grep -E "(frontend|backend|mongodb)" | head -3)
    echo -e "Memory Usage:"
    echo "$memory_usage"
    
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}')
    echo -e "Disk Usage: $disk_usage"
    
    local disk_percent=$(echo "$disk_usage" | sed 's/%//')
    if [ "$disk_percent" -gt 80 ]; then
        echo -e "${YELLOW}⚠ Warning: Disk usage is high ($disk_usage)${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    else
        echo -e "${GREEN}✓ Disk usage is acceptable ($disk_usage)${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo ""
}

check_security() {
    echo -e "${BLUE}Security Checks:${NC}"
    
    if grep -q "admin123" "$PROJECT_DIR/.env" 2>/dev/null; then
        echo -e "${RED}✗ Default admin password detected${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    else
        echo -e "${GREEN}✓ Default admin password changed${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if grep -q "your-super-secret-jwt-key" "$PROJECT_DIR/.env" 2>/dev/null; then
        echo -e "${RED}✗ Default JWT secret detected${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    else
        echo -e "${GREEN}✓ JWT secret configured${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo ""
}

generate_report() {
    echo -e "${BLUE}Health Check Summary:${NC}"
    echo "====================="
    echo -e "Total Checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    echo -e "Success Rate: $success_rate%"
    
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All health checks passed! ExLog Dashboard is healthy.${NC}"
        return 0
    elif [ "$success_rate" -ge 80 ]; then
        echo -e "\n${YELLOW}⚠ Most health checks passed, but some issues detected.${NC}"
        return 1
    else
        echo -e "\n${RED}❌ Multiple health check failures detected. Please investigate.${NC}"
        return 2
    fi
}

main() {
    > "$HEALTH_LOG"
    
    log "INFO" "Starting ExLog Dashboard health check"
    
    check_docker
    check_services
    check_database
    check_performance
    check_security
    
    generate_report
    local exit_code=$?
    
    echo ""
    echo -e "${BLUE}Detailed log saved to: $HEALTH_LOG${NC}"
    
    return $exit_code
}

case "${1:-}" in
    --help|-h)
        echo "ExLog Dashboard Health Check Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --docker-only       Check only Docker environment"
        echo "  --services-only     Check only service health"
        echo "  --database-only     Check only database health"
        echo "  --performance-only  Check only performance metrics"
        echo "  --security-only     Check only security settings"
        echo "  --quiet             Suppress output, only return exit code"
        echo ""
        echo "Exit Codes:"
        echo "  0 - All checks passed"
        echo "  1 - Some checks failed (>80% success rate)"
        echo "  2 - Many checks failed (<80% success rate)"
        echo ""
        exit 0
        ;;
    --docker-only)
        check_docker
        generate_report
        ;;
    --services-only)
        check_services
        generate_report
        ;;
    --database-only)
        check_database
        generate_report
        ;;
    --performance-only)
        check_performance
        generate_report
        ;;
    --security-only)
        check_security
        generate_report
        ;;
    --quiet)
        main >/dev/null 2>&1
        ;;
    *)
        main
        ;;
esac

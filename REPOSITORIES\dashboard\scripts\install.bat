@echo off
REM ExLog Dashboard Installation Script for Windows
REM This script automatically installs Docker Desktop and sets up ExLog Dashboard

setlocal enabledelayedexpansion

REM Colors for output (Windows doesn't support colors in batch, but we'll use echo)
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM Configuration
set "EXLOG_VERSION=1.0.0"
set "INSTALL_DIR=%USERPROFILE%\exlog-dashboard"
set "USE_DOCKER_HUB=true"

echo ExLog Dashboard Installation Script for Windows
echo ================================================
echo Version: %EXLOG_VERSION%
echo Install Directory: %INSTALL_DIR%
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo WARNING: Running as administrator. This is not recommended for security reasons.
    set /p "response=Continue anyway? (y/N): "
    if /i not "!response!"=="y" exit /b 1
)

REM Check system requirements
echo [INFO] Checking system requirements...

REM Check Windows version (Windows 10 or later required for Docker Desktop)
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
if %VERSION% LSS 10.0 (
    echo [ERROR] Windows 10 or later is required for Docker Desktop
    exit /b 1
)

REM Check if Hyper-V is available
powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All" | findstr "Enabled" >nul
if %errorLevel% neq 0 (
    echo [WARN] Hyper-V is not enabled. Docker Desktop requires Hyper-V or WSL 2.
    echo Please enable Hyper-V or WSL 2 before continuing.
)

REM Check available memory (minimum 4GB for Windows)
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set "mem_bytes=%%p"
    goto :memory_check
)
:memory_check
set /a mem_gb=mem_bytes/1024/1024/1024
if %mem_gb% LSS 4 (
    echo [WARN] System has %mem_gb%GB RAM. Minimum 4GB recommended for Windows.
) else (
    echo [INFO] Memory check passed: %mem_gb%GB available
)

REM Check if Docker Desktop is installed
docker --version >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Docker is already installed
    goto :verify_docker
)

echo [INFO] Docker Desktop is not installed. Please install Docker Desktop manually.
echo.
echo Please follow these steps:
echo 1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop
echo 2. Run the installer and follow the setup wizard
echo 3. Restart your computer if prompted
echo 4. Start Docker Desktop
echo 5. Run this script again
echo.
pause
exit /b 1

:verify_docker
echo [INFO] Verifying Docker installation...

docker --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker installation verification failed
    exit /b 1
)

docker compose version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker Compose is not available
    exit /b 1
)

REM Test Docker daemon
docker info >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker daemon is not running. Please start Docker Desktop and try again.
    exit /b 1
)

echo [INFO] Docker verification completed successfully

REM Create installation directory
echo [INFO] Creating installation directory: %INSTALL_DIR%

if exist "%INSTALL_DIR%" (
    echo [WARN] Directory already exists. Backing up existing installation...
    set "backup_dir=%INSTALL_DIR%.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    move "%INSTALL_DIR%" "!backup_dir!" >nul
)

mkdir "%INSTALL_DIR%"
cd /d "%INSTALL_DIR%"

REM Download ExLog Dashboard
echo [INFO] Downloading ExLog Dashboard...

if "%USE_DOCKER_HUB%"=="true" (
    REM Download Docker Compose file for Docker Hub images
    powershell -Command "Invoke-WebRequest -Uri 'https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml' -OutFile 'docker-compose.yml'"
    powershell -Command "Invoke-WebRequest -Uri 'https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/.env.template' -OutFile '.env'"
) else (
    REM Check if Git is installed
    git --version >nul 2>&1
    if %errorLevel% neq 0 (
        echo [ERROR] Git is required for local build. Please install Git from: https://git-scm.com/download/win
        exit /b 1
    )
    
    git clone https://gitlab.com/spr888/dashboard.git .
    copy .env.example .env >nul
)

echo [INFO] ExLog Dashboard downloaded successfully

REM Configure environment
echo [INFO] Configuring environment...

REM Generate secure JWT secret using PowerShell
for /f %%i in ('powershell -Command "[System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes([System.Guid]::NewGuid().ToString()))"') do set "jwt_secret=%%i"

REM Update .env file with production settings
powershell -Command "(Get-Content .env) -replace 'NODE_ENV=development', 'NODE_ENV=production' -replace 'your-super-secret-jwt-key-change-in-production-please', '%jwt_secret%' -replace 'ENABLE_AUTO_DELETE=false', 'ENABLE_AUTO_DELETE=true' -replace 'CORS_ORIGIN=\*', 'CORS_ORIGIN=http://localhost:8080,http://localhost:3000' | Set-Content .env"

echo [INFO] Environment configuration completed

REM Start ExLog Dashboard
echo [INFO] Starting ExLog Dashboard...

if "%USE_DOCKER_HUB%"=="true" (
    docker compose pull
) else (
    docker compose build
)

docker compose up -d

REM Wait for services to be ready
echo [INFO] Waiting for services to start...
set "max_attempts=30"
set "attempt=0"

:wait_loop
if %attempt% geq %max_attempts% goto :start_failed

powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8080/health' -UseBasicParsing -TimeoutSec 1 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% == 0 goto :start_success

powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -UseBasicParsing -TimeoutSec 1 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% == 0 goto :start_success

timeout /t 2 /nobreak >nul
set /a attempt+=1
goto :wait_loop

:start_failed
echo [ERROR] Services failed to start within expected time
exit /b 1

:start_success
echo [INFO] ExLog Dashboard started successfully

REM Display completion message
echo.
echo 🎉 ExLog Dashboard Installation Completed Successfully!
echo ========================================================
echo.
echo Access your dashboard at:
echo   • http://localhost:8080 (recommended)
echo   • http://localhost:3000 (alternative)
echo.
echo Installation Directory: %INSTALL_DIR%
echo.
echo Useful Commands:
echo   • View logs: docker compose logs -f
echo   • Stop services: docker compose down
echo   • Restart services: docker compose restart
echo   • Update ExLog: docker compose pull ^&^& docker compose up -d
echo.
echo Default Login:
echo   • Username: admin
echo   • Password: admin123
echo   (Please change the default password after first login)
echo.
echo Documentation:
echo   • Installation Guide: https://gitlab.com/spr888/dashboard/-/blob/main/INSTALLATION.md
echo   • Deployment Guide: https://gitlab.com/spr888/dashboard/-/blob/main/DEPLOYMENT.md
echo   • Troubleshooting: https://gitlab.com/spr888/dashboard/-/blob/main/docs/TROUBLESHOOTING.md
echo.

pause

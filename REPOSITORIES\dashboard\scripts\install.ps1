# ExLog Dashboard Installation Script for Windows PowerShell
# This script automatically installs Docker Desktop and sets up ExLog Dashboard

param(
    [string]$InstallDir = "$env:USERPROFILE\exlog-dashboard",
    [switch]$UseDockerHub = $true,
    [switch]$LocalBuild = $false,
    [switch]$Help = $false
)

# Configuration
$ExLogVersion = "1.0.0"
$DockerDesktopUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"

if ($Help) {
    Write-Host "ExLog Dashboard Installation Script for Windows PowerShell"
    Write-Host ""
    Write-Host "Usage: .\install.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -InstallDir <path>    Set installation directory (default: ~/exlog-dashboard)"
    Write-Host "  -UseDockerHub         Use Docker Hub images (default)"
    Write-Host "  -LocalBuild           Build images locally instead of using Docker Hub"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    exit 0
}

if ($LocalBuild) {
    $UseDockerHub = $false
}

Write-Host "ExLog Dashboard Installation Script for Windows PowerShell" -ForegroundColor Blue
Write-Host "============================================================" -ForegroundColor Blue
Write-Host "Version: $ExLogVersion"
Write-Host "Install Directory: $InstallDir"
Write-Host ""

# Function to write log messages
function Write-Log {
    param(
        [string]$Level,
        [string]$Message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "INFO" { "Green" }
        "WARN" { "Yellow" }
        "ERROR" { "Red" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Function to handle errors
function Stop-Installation {
    param([string]$Message)
    Write-Log "ERROR" $Message
    Write-Host "Installation failed. Please check the error above and try again." -ForegroundColor Red
    exit 1
}

# Check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (Test-Administrator) {
    Write-Log "WARN" "Running as administrator. This is not recommended for security reasons."
    $response = Read-Host "Continue anyway? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        exit 1
    }
}

# Check system requirements
Write-Log "INFO" "Checking system requirements..."

# Check Windows version
$osVersion = [System.Environment]::OSVersion.Version
if ($osVersion.Major -lt 10) {
    Stop-Installation "Windows 10 or later is required for Docker Desktop"
}

# Check if Hyper-V is available
try {
    $hyperV = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All -ErrorAction SilentlyContinue
    if ($hyperV.State -ne "Enabled") {
        Write-Log "WARN" "Hyper-V is not enabled. Docker Desktop requires Hyper-V or WSL 2."
        Write-Log "WARN" "Please enable Hyper-V or WSL 2 before continuing."
    }
} catch {
    Write-Log "WARN" "Could not check Hyper-V status. Please ensure virtualization is enabled."
}

# Check available memory
$memory = Get-CimInstance -ClassName Win32_ComputerSystem
$memoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
if ($memoryGB -lt 4) {
    Write-Log "WARN" "System has ${memoryGB}GB RAM. Minimum 4GB recommended for Windows."
} else {
    Write-Log "INFO" "Memory check passed: ${memoryGB}GB available"
}

# Check if Docker Desktop is installed
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "INFO" "Docker is already installed: $dockerVersion"
    } else {
        throw "Docker not found"
    }
} catch {
    Write-Log "INFO" "Docker Desktop is not installed."
    Write-Host ""
    Write-Host "Please install Docker Desktop manually:" -ForegroundColor Yellow
    Write-Host "1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    Write-Host "2. Run the installer and follow the setup wizard" -ForegroundColor Yellow
    Write-Host "3. Restart your computer if prompted" -ForegroundColor Yellow
    Write-Host "4. Start Docker Desktop" -ForegroundColor Yellow
    Write-Host "5. Run this script again" -ForegroundColor Yellow
    Write-Host ""
    
    $response = Read-Host "Would you like to download Docker Desktop now? (y/N)"
    if ($response -eq "y" -or $response -eq "Y") {
        Start-Process $DockerDesktopUrl
    }
    
    Read-Host "Press Enter when Docker Desktop is installed and running..."
}

# Verify Docker installation
Write-Log "INFO" "Verifying Docker installation..."

try {
    $null = docker --version
    if ($LASTEXITCODE -ne 0) {
        throw "Docker verification failed"
    }
} catch {
    Stop-Installation "Docker installation verification failed"
}

try {
    $null = docker compose version
    if ($LASTEXITCODE -ne 0) {
        throw "Docker Compose verification failed"
    }
} catch {
    Stop-Installation "Docker Compose is not available"
}

# Test Docker daemon
try {
    $null = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker daemon not running"
    }
} catch {
    Stop-Installation "Docker daemon is not running. Please start Docker Desktop and try again."
}

Write-Log "INFO" "Docker verification completed successfully"

# Create installation directory
Write-Log "INFO" "Creating installation directory: $InstallDir"

if (Test-Path $InstallDir) {
    Write-Log "WARN" "Directory already exists. Backing up existing installation..."
    $backupDir = "$InstallDir.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Move-Item $InstallDir $backupDir
}

New-Item -ItemType Directory -Path $InstallDir -Force | Out-Null
Set-Location $InstallDir

# Download ExLog Dashboard
Write-Log "INFO" "Downloading ExLog Dashboard..."

if ($UseDockerHub) {
    # Download Docker Compose file for Docker Hub images
    try {
        Invoke-WebRequest -Uri "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml" -OutFile "docker-compose.yml"
        Invoke-WebRequest -Uri "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/.env.template" -OutFile ".env"
    } catch {
        Stop-Installation "Failed to download ExLog Dashboard files: $($_.Exception.Message)"
    }
} else {
    # Check if Git is installed
    try {
        $null = git --version
        if ($LASTEXITCODE -ne 0) {
            throw "Git not found"
        }
    } catch {
        Stop-Installation "Git is required for local build. Please install Git from: https://git-scm.com/download/win"
    }
    
    try {
        git clone https://gitlab.com/spr888/dashboard.git .
        Copy-Item ".env.example" ".env"
    } catch {
        Stop-Installation "Failed to clone ExLog Dashboard repository: $($_.Exception.Message)"
    }
}

Write-Log "INFO" "ExLog Dashboard downloaded successfully"

# Configure environment
Write-Log "INFO" "Configuring environment..."

# Generate secure JWT secret
$jwtSecret = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes([System.Guid]::NewGuid().ToString()))

# Update .env file with production settings
try {
    $envContent = Get-Content ".env"
    $envContent = $envContent -replace "NODE_ENV=development", "NODE_ENV=production"
    $envContent = $envContent -replace "your-super-secret-jwt-key-change-in-production-please", $jwtSecret
    $envContent = $envContent -replace "ENABLE_AUTO_DELETE=false", "ENABLE_AUTO_DELETE=true"
    $envContent = $envContent -replace "CORS_ORIGIN=\*", "CORS_ORIGIN=http://localhost:8080,http://localhost:3000"
    Set-Content ".env" $envContent
} catch {
    Stop-Installation "Failed to configure environment: $($_.Exception.Message)"
}

Write-Log "INFO" "Environment configuration completed"

# Start ExLog Dashboard
Write-Log "INFO" "Starting ExLog Dashboard..."

try {
    if ($UseDockerHub) {
        docker compose pull
    } else {
        docker compose build
    }
    
    docker compose up -d
} catch {
    Stop-Installation "Failed to start ExLog Dashboard: $($_.Exception.Message)"
}

# Wait for services to be ready
Write-Log "INFO" "Waiting for services to start..."
$maxAttempts = 30
$attempt = 0

do {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 1 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            break
        }
    } catch {
        # Try alternative port
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 1 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                break
            }
        } catch {
            # Continue waiting
        }
    }
    
    Start-Sleep -Seconds 2
    $attempt++
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Stop-Installation "Services failed to start within expected time"
}

Write-Log "INFO" "ExLog Dashboard started successfully"

# Display completion message
Write-Host ""
Write-Host "🎉 ExLog Dashboard Installation Completed Successfully!" -ForegroundColor Green
Write-Host "========================================================"
Write-Host ""
Write-Host "Access your dashboard at:" -ForegroundColor Blue
Write-Host "  • http://localhost:8080 (recommended)"
Write-Host "  • http://localhost:3000 (alternative)"
Write-Host ""
Write-Host "Installation Directory: $InstallDir" -ForegroundColor Blue
Write-Host ""
Write-Host "Useful Commands:" -ForegroundColor Blue
Write-Host "  • View logs: docker compose logs -f" -ForegroundColor Yellow
Write-Host "  • Stop services: docker compose down" -ForegroundColor Yellow
Write-Host "  • Restart services: docker compose restart" -ForegroundColor Yellow
Write-Host "  • Update ExLog: docker compose pull && docker compose up -d" -ForegroundColor Yellow
Write-Host ""
Write-Host "Default Login:" -ForegroundColor Blue
Write-Host "  • Username: admin"
Write-Host "  • Password: admin123"
Write-Host "  (Please change the default password after first login)" -ForegroundColor Yellow
Write-Host ""
Write-Host "Documentation:" -ForegroundColor Blue
Write-Host "  • Installation Guide: https://gitlab.com/spr888/dashboard/-/blob/main/INSTALLATION.md"
Write-Host "  • Deployment Guide: https://gitlab.com/spr888/dashboard/-/blob/main/DEPLOYMENT.md"
Write-Host "  • Troubleshooting: https://gitlab.com/spr888/dashboard/-/blob/main/docs/TROUBLESHOOTING.md"
Write-Host ""

Read-Host "Press Enter to exit..."

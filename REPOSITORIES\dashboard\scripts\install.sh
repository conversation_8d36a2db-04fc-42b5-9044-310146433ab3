#!/bin/bash


set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
EXLOG_VERSION="1.0.0"
DOCKER_COMPOSE_VERSION="2.20.0"
INSTALL_DIR="${INSTALL_DIR:-$HOME/exlog-dashboard}"
USE_DOCKER_HUB="${USE_DOCKER_HUB:-true}"

echo -e "${BLUE}ExLog Dashboard Installation Script${NC}"
echo "========================================"
echo -e "Version: ${EXLOG_VERSION}"
echo -e "Install Directory: ${INSTALL_DIR}"
echo ""

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] [${level}] $message"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    echo -e "${RED}Installation failed. Please check the error above and try again.${NC}"
    exit 1
}

check_root() {
    if [ "$EUID" -eq 0 ]; then
        log "WARN" "Running as root. This is not recommended for security reasons."
        echo -e "${YELLOW}Continue anyway? (y/N)${NC}"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/os-release ]; then
            . /etc/os-release
            OS=$ID
            OS_VERSION=$VERSION_ID
        else
            error_exit "Cannot detect Linux distribution"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        OS_VERSION=$(sw_vers -productVersion)
    else
        error_exit "Unsupported operating system: $OSTYPE"
    fi
    
    log "INFO" "Detected OS: $OS $OS_VERSION"
}

check_requirements() {
    log "INFO" "Checking system requirements..."
    
    if [[ "$OS" == "linux-gnu" ]] || [[ "$OS" == "ubuntu" ]] || [[ "$OS" == "debian" ]] || [[ "$OS" == "centos" ]] || [[ "$OS" == "rhel" ]]; then
        local mem_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        local mem_gb=$((mem_kb / 1024 / 1024))
    elif [[ "$OS" == "macos" ]]; then
        local mem_bytes=$(sysctl -n hw.memsize)
        local mem_gb=$((mem_bytes / 1024 / 1024 / 1024))
    fi
    
    if [ "$mem_gb" -lt 2 ]; then
        log "WARN" "System has ${mem_gb}GB RAM. Minimum 2GB recommended."
    else
        log "INFO" "Memory check passed: ${mem_gb}GB available"
    fi
    
    local available_space=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$available_space" -lt 5 ]; then
        log "WARN" "Low disk space: ${available_space}GB available. Minimum 5GB recommended."
    else
        log "INFO" "Disk space check passed: ${available_space}GB available"
    fi
}

install_docker() {
    if command -v docker >/dev/null 2>&1; then
        log "INFO" "Docker is already installed: $(docker --version)"
        return 0
    fi
    
    log "INFO" "Installing Docker..."
    
    case "$OS" in
        ubuntu|debian)
            sudo apt-get update
            sudo apt-get install -y ca-certificates curl gnupg lsb-release
            sudo mkdir -p /etc/apt/keyrings
            curl -fsSL https://download.docker.com/linux/$OS/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$OS $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        centos|rhel)
            sudo yum install -y yum-utils
            sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            sudo systemctl start docker
            sudo systemctl enable docker
            ;;
        macos)
            if ! command -v brew >/dev/null 2>&1; then
                error_exit "Homebrew is required for macOS installation. Please install Homebrew first: https://brew.sh"
            fi
            brew install --cask docker
            log "INFO" "Please start Docker Desktop manually and return to continue installation"
            echo -e "${YELLOW}Press Enter when Docker Desktop is running...${NC}"
            read -r
            ;;
        *)
            error_exit "Unsupported OS for automatic Docker installation: $OS"
            ;;
    esac
    
    if [[ "$OS" != "macos" ]]; then
        sudo usermod -aG docker $USER
        log "INFO" "Added user to docker group. You may need to log out and back in for changes to take effect."
    fi
    
    log "INFO" "Docker installation completed"
}

install_docker_compose() {
    if docker compose version >/dev/null 2>&1; then
        log "INFO" "Docker Compose is already available: $(docker compose version)"
        return 0
    fi
    
    if command -v docker-compose >/dev/null 2>&1; then
        log "INFO" "Docker Compose (standalone) is already installed: $(docker-compose --version)"
        return 0
    fi
    
    log "INFO" "Installing Docker Compose..."
    
    case "$OS" in
        ubuntu|debian|centos|rhel)
            sudo curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            ;;
        macos)
            brew install docker-compose
            ;;
        *)
            error_exit "Unsupported OS for Docker Compose installation: $OS"
            ;;
    esac
    
    log "INFO" "Docker Compose installation completed"
}

verify_docker() {
    log "INFO" "Verifying Docker installation..."
    
    if ! docker --version >/dev/null 2>&1; then
        error_exit "Docker installation verification failed"
    fi
    
    if ! docker compose version >/dev/null 2>&1 && ! docker-compose --version >/dev/null 2>&1; then
        error_exit "Docker Compose installation verification failed"
    fi
    
    if ! docker info >/dev/null 2>&1; then
        error_exit "Docker daemon is not running. Please start Docker and try again."
    fi
    
    log "INFO" "Docker verification completed successfully"
}

create_install_dir() {
    log "INFO" "Creating installation directory: $INSTALL_DIR"
    
    if [ -d "$INSTALL_DIR" ]; then
        log "WARN" "Directory already exists. Backing up existing installation..."
        mv "$INSTALL_DIR" "${INSTALL_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$INSTALL_DIR"
    cd "$INSTALL_DIR"
}

download_exlog() {
    log "INFO" "Downloading ExLog Dashboard..."
    
    if [ "$USE_DOCKER_HUB" = "true" ]; then
        curl -fsSL "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml" -o docker-compose.yml
        curl -fsSL "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/.env.template" -o .env
    else
        if ! command -v git >/dev/null 2>&1; then
            case "$OS" in
                ubuntu|debian)
                    sudo apt-get install -y git
                    ;;
                centos|rhel)
                    sudo yum install -y git
                    ;;
                macos)
                    if command -v brew >/dev/null 2>&1; then
                        brew install git
                    else
                        error_exit "Git is required. Please install Git and try again."
                    fi
                    ;;
            esac
        fi
        
        git clone https://gitlab.com/spr888/dashboard.git .
        cp .env.example .env
    fi
    
    log "INFO" "ExLog Dashboard downloaded successfully"
}

configure_environment() {
    log "INFO" "Configuring environment..."
    
    local jwt_secret=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
    
    sed -i.bak \
        -e "s/NODE_ENV=development/NODE_ENV=production/" \
        -e "s/your-super-secret-jwt-key-change-in-production-please/$jwt_secret/" \
        -e "s/ENABLE_AUTO_DELETE=false/ENABLE_AUTO_DELETE=true/" \
        -e "s/CORS_ORIGIN=\*/CORS_ORIGIN=http:\/\/localhost:8080,http:\/\/localhost:3000/" \
        .env
    
    log "INFO" "Environment configuration completed"
}

start_exlog() {
    log "INFO" "Starting ExLog Dashboard..."
    
    if [ "$USE_DOCKER_HUB" = "true" ]; then
        docker compose pull
    else
        docker compose build
    fi
    
    docker compose up -d
    
    log "INFO" "Waiting for services to start..."
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:8080/health >/dev/null 2>&1 || curl -s http://localhost:3000 >/dev/null 2>&1; then
            break
        fi
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        error_exit "Services failed to start within expected time"
    fi
    
    log "INFO" "ExLog Dashboard started successfully"
}

show_completion() {
    echo ""
    echo -e "${GREEN}🎉 ExLog Dashboard Installation Completed Successfully!${NC}"
    echo "========================================================"
    echo ""
    echo -e "${BLUE}Access your dashboard at:${NC}"
    echo -e "  • http://localhost:8080 (recommended)"
    echo -e "  • http://localhost:3000 (alternative)"
    echo ""
    echo -e "${BLUE}Installation Directory:${NC} $INSTALL_DIR"
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    echo -e "  • View logs: ${YELLOW}docker compose logs -f${NC}"
    echo -e "  • Stop services: ${YELLOW}docker compose down${NC}"
    echo -e "  • Restart services: ${YELLOW}docker compose restart${NC}"
    echo -e "  • Update ExLog: ${YELLOW}docker compose pull && docker compose up -d${NC}"
    echo ""
    echo -e "${BLUE}Default Login:${NC}"
    echo -e "  • Username: admin"
    echo -e "  • Password: admin123"
    echo -e "  ${YELLOW}(Please change the default password after first login)${NC}"
    echo ""
    echo -e "${BLUE}Documentation:${NC}"
    echo -e "  • Installation Guide: https://gitlab.com/spr888/dashboard/-/blob/main/INSTALLATION.md"
    echo -e "  • Deployment Guide: https://gitlab.com/spr888/dashboard/-/blob/main/DEPLOYMENT.md"
    echo -e "  • Troubleshooting: https://gitlab.com/spr888/dashboard/-/blob/main/docs/TROUBLESHOOTING.md"
    echo ""
}

cleanup_on_failure() {
    if [ -d "$INSTALL_DIR" ] && [ "$INSTALL_DIR" != "$HOME" ] && [ "$INSTALL_DIR" != "/" ]; then
        log "INFO" "Cleaning up installation directory due to failure..."
        rm -rf "$INSTALL_DIR"
    fi
}

main() {
    trap cleanup_on_failure ERR
    
    check_root
    detect_os
    check_requirements
    install_docker
    install_docker_compose
    verify_docker
    create_install_dir
    download_exlog
    configure_environment
    start_exlog
    show_completion
}

case "${1:-}" in
    --help|-h)
        echo "ExLog Dashboard Installation Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --docker-hub        Use Docker Hub images (default)"
        echo "  --local-build       Build images locally instead of using Docker Hub"
        echo "  --install-dir DIR   Set installation directory (default: ~/exlog-dashboard)"
        echo ""
        echo "Environment Variables:"
        echo "  INSTALL_DIR         Installation directory"
        echo "  USE_DOCKER_HUB      Use Docker Hub images (true/false)"
        echo ""
        exit 0
        ;;
    --docker-hub)
        USE_DOCKER_HUB="true"
        ;;
    --local-build)
        USE_DOCKER_HUB="false"
        ;;
    --install-dir)
        INSTALL_DIR="$2"
        shift
        ;;
esac

main "$@"

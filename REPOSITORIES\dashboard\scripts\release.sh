#!/bin/bash


set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
VERSION_FILE="$PROJECT_DIR/VERSION"
PACKAGE_JSON="$PROJECT_DIR/package.json"
AI_PACKAGE_JSON="$PROJECT_DIR/ai-service/package.json"
BACKEND_PACKAGE_JSON="$PROJECT_DIR/backend/package.json"
FRONTEND_PACKAGE_JSON="$PROJECT_DIR/frontend/package.json"

echo -e "${BLUE}ExLog Dashboard Release Script${NC}"
echo "======================================"
echo ""

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] [${level}] $message"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

get_current_version() {
    if [ -f "$VERSION_FILE" ]; then
        cat "$VERSION_FILE"
    elif [ -f "$PACKAGE_JSON" ]; then
        grep '"version"' "$PACKAGE_JSON" | sed 's/.*"version": *"\([^"]*\)".*/\1/'
    else
        echo "1.0.0"
    fi
}

validate_version() {
    local version="$1"
    if [[ ! "$version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        error_exit "Invalid version format: $version. Expected format: X.Y.Z"
    fi
}

increment_version() {
    local version="$1"
    local type="$2"
    
    local major minor patch
    IFS='.' read -r major minor patch <<< "$version"
    
    case "$type" in
        major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        minor)
            minor=$((minor + 1))
            patch=0
            ;;
        patch)
            patch=$((patch + 1))
            ;;
        *)
            error_exit "Invalid version type: $type. Use major, minor, or patch"
            ;;
    esac
    
    echo "$major.$minor.$patch"
}

update_version_files() {
    local new_version="$1"
    
    log "INFO" "Updating version to $new_version in all files..."
    
    echo "$new_version" > "$VERSION_FILE"
    
    if [ -f "$PACKAGE_JSON" ]; then
        sed -i.bak "s/\"version\": *\"[^\"]*\"/\"version\": \"$new_version\"/" "$PACKAGE_JSON"
        rm -f "$PACKAGE_JSON.bak"
    fi
    
    if [ -f "$AI_PACKAGE_JSON" ]; then
        sed -i.bak "s/\"version\": *\"[^\"]*\"/\"version\": \"$new_version\"/" "$AI_PACKAGE_JSON"
        rm -f "$AI_PACKAGE_JSON.bak"
    fi
    
    if [ -f "$BACKEND_PACKAGE_JSON" ]; then
        sed -i.bak "s/\"version\": *\"[^\"]*\"/\"version\": \"$new_version\"/" "$BACKEND_PACKAGE_JSON"
        rm -f "$BACKEND_PACKAGE_JSON.bak"
    fi
    
    if [ -f "$FRONTEND_PACKAGE_JSON" ]; then
        sed -i.bak "s/\"version\": *\"[^\"]*\"/\"version\": \"$new_version\"/" "$FRONTEND_PACKAGE_JSON"
        rm -f "$FRONTEND_PACKAGE_JSON.bak"
    fi
    
    log "INFO" "Version files updated successfully"
}

generate_changelog_entry() {
    local version="$1"
    local date=$(date '+%Y-%m-%d')
    
    local last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
    local commits
    
    if [ -n "$last_tag" ]; then
        commits=$(git log --oneline "$last_tag"..HEAD --pretty=format:"- %s" 2>/dev/null || echo "- Initial release")
    else
        commits="- Initial release"
    fi
    
    local changelog_entry="## [$version] - $date

$commits

"
    
    if [ -f "$PROJECT_DIR/CHANGELOG.md" ]; then
        local temp_file=$(mktemp)
        echo "$changelog_entry" > "$temp_file"
        cat "$PROJECT_DIR/CHANGELOG.md" >> "$temp_file"
        mv "$temp_file" "$PROJECT_DIR/CHANGELOG.md"
    else
        echo "# Changelog

$changelog_entry" > "$PROJECT_DIR/CHANGELOG.md"
    fi
    
    log "INFO" "Changelog updated with version $version"
}

check_git_status() {
    log "INFO" "Checking git status..."
    
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        error_exit "Not in a git repository"
    fi
    
    if ! git diff-index --quiet HEAD --; then
        log "WARN" "Working directory has uncommitted changes:"
        git status --porcelain
        echo ""
        read -p "Continue with uncommitted changes? (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log "INFO" "Git status check completed"
}

create_git_tag() {
    local version="$1"
    local tag_name="v$version"
    
    log "INFO" "Creating git tag: $tag_name"
    
    if git tag -l | grep -q "^$tag_name$"; then
        error_exit "Tag $tag_name already exists"
    fi
    
    git tag -a "$tag_name" -m "Release version $version

$(git log --oneline $(git describe --tags --abbrev=0 2>/dev/null || git rev-list --max-parents=0 HEAD)..HEAD 2>/dev/null || echo "Initial release")"
    
    log "INFO" "Git tag $tag_name created successfully"
}

build_and_test() {
    log "INFO" "Building and testing..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "package.json" ]; then
        if command -v npm >/dev/null 2>&1; then
            npm install
        fi
    fi
    
    if [ -f "package.json" ] && grep -q '"test"' package.json; then
        if command -v npm >/dev/null 2>&1; then
            npm test || log "WARN" "Tests failed, but continuing with release"
        fi
    fi
    
    if [ -f "docker-compose.yml" ]; then
        log "INFO" "Building Docker images..."
        docker compose build || error_exit "Docker build failed"
    fi
    
    log "INFO" "Build and test completed"
}

push_to_remote() {
    local version="$1"
    local tag_name="v$version"
    
    log "INFO" "Pushing changes to remote repository..."
    
    git push origin HEAD
    
    git push origin "$tag_name"
    
    log "INFO" "Changes pushed to remote repository"
}

show_release_summary() {
    local version="$1"
    local tag_name="v$version"
    
    echo ""
    echo -e "${GREEN}🎉 Release $version Created Successfully!${NC}"
    echo "========================================"
    echo ""
    echo -e "${BLUE}Release Details:${NC}"
    echo -e "  • Version: $version"
    echo -e "  • Git Tag: $tag_name"
    echo -e "  • Date: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo -e "  • Build and push Docker images: ${YELLOW}./.dockerhub/build-and-push.sh${NC}"
    echo -e "  • Update documentation if needed"
    echo -e "  • Announce the release"
    echo ""
    echo -e "${BLUE}Docker Hub Images:${NC}"
    echo -e "  • exlog/exlog-frontend:$version"
    echo -e "  • exlog/exlog-backend:$version"
    echo -e "  • exlog/exlog-websocket:$version"
    echo -e "  • exlog/exlog-ai-service:$version"
    echo ""
}

main() {
    local version_type="${1:-patch}"
    local custom_version="$2"
    
    check_git_status
    
    local current_version
    current_version=$(get_current_version)
    log "INFO" "Current version: $current_version"
    
    local new_version
    if [ -n "$custom_version" ]; then
        validate_version "$custom_version"
        new_version="$custom_version"
    else
        new_version=$(increment_version "$current_version" "$version_type")
    fi
    
    log "INFO" "New version: $new_version"
    
    echo ""
    echo -e "${YELLOW}About to create release $new_version${NC}"
    read -p "Continue? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "INFO" "Release cancelled"
        exit 0
    fi
    
    update_version_files "$new_version"
    generate_changelog_entry "$new_version"
    build_and_test
    
    git add VERSION package.json ai-service/package.json backend/package.json frontend/package.json CHANGELOG.md 2>/dev/null || true
    git commit -m "Release version $new_version

- Updated version to $new_version
- Updated changelog
- Prepared for release"
    
    create_git_tag "$new_version"
    push_to_remote "$new_version"
    show_release_summary "$new_version"
}

case "${1:-}" in
    --help|-h)
        echo "ExLog Dashboard Release Script"
        echo ""
        echo "Usage: $0 [version_type] [custom_version]"
        echo ""
        echo "Version Types:"
        echo "  patch    Increment patch version (default)"
        echo "  minor    Increment minor version"
        echo "  major    Increment major version"
        echo ""
        echo "Custom Version:"
        echo "  Specify exact version (e.g., 2.1.0)"
        echo ""
        echo "Examples:"
        echo "  $0                    # Increment patch version"
        echo "  $0 minor              # Increment minor version"
        echo "  $0 major              # Increment major version"
        echo "  $0 custom 2.1.0       # Set specific version"
        echo ""
        echo "Options:"
        echo "  --help, -h            Show this help message"
        echo "  --dry-run            Show what would be done without doing it"
        echo ""
        exit 0
        ;;
    --dry-run)
        current_version=$(get_current_version)
        new_version=$(increment_version "$current_version" "${2:-patch}")
        echo "DRY RUN - Would create release:"
        echo "  Current version: $current_version"
        echo "  New version: $new_version"
        echo "  Git tag: v$new_version"
        echo ""
        echo "Files that would be updated:"
        echo "  • VERSION"
        echo "  • package.json"
        echo "  • ai-service/package.json"
        echo "  • backend/package.json"
        echo "  • frontend/package.json"
        echo "  • CHANGELOG.md"
        exit 0
        ;;
    custom)
        main "custom" "$2"
        ;;
    major|minor|patch)
        main "$1"
        ;;
    "")
        main "patch"
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

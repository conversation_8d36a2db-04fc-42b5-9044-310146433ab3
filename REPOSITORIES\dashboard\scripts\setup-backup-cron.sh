#!/bin/bash

# ExLog Backup Cron Setup Script
# This script sets up automated backups using cron

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_SCRIPT="$SCRIPT_DIR/backup-logs.sh"

echo -e "${BLUE}ExLog Backup Cron Setup${NC}"
echo "========================================"
echo ""

# Check if backup script exists
if [ ! -f "$BACKUP_SCRIPT" ]; then
    echo -e "${RED}Error: Backup script not found at $BACKUP_SCRIPT${NC}"
    exit 1
fi

# Make sure backup script is executable
chmod +x "$BACKUP_SCRIPT"

# Function to add cron job
add_cron_job() {
    local schedule="$1"
    local description="$2"
    
    # Create cron job entry
    local cron_entry="$schedule cd $PROJECT_DIR && $BACKUP_SCRIPT >> $PROJECT_DIR/backups/cron.log 2>&1"
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT"; then
        echo -e "${YELLOW}Cron job already exists. Updating...${NC}"
        # Remove existing job
        crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT" | crontab -
    fi
    
    # Add new cron job
    (crontab -l 2>/dev/null; echo "$cron_entry") | crontab -
    
    echo -e "${GREEN}✓ Cron job added: $description${NC}"
    echo "  Schedule: $schedule"
    echo "  Command: $cron_entry"
    echo ""
}

# Function to show current cron jobs
show_cron_jobs() {
    echo -e "${BLUE}Current cron jobs:${NC}"
    if crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT"; then
        crontab -l 2>/dev/null | grep "$BACKUP_SCRIPT"
    else
        echo "No ExLog backup cron jobs found."
    fi
    echo ""
}

# Function to remove cron jobs
remove_cron_jobs() {
    if crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT"; then
        crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT" | crontab -
        echo -e "${GREEN}✓ ExLog backup cron jobs removed${NC}"
    else
        echo -e "${YELLOW}No ExLog backup cron jobs found to remove${NC}"
    fi
    echo ""
}

# Function to test backup script
test_backup() {
    echo -e "${BLUE}Testing backup script...${NC}"
    
    if "$BACKUP_SCRIPT" --dry-run; then
        echo -e "${GREEN}✓ Backup script test passed${NC}"
    else
        echo -e "${RED}✗ Backup script test failed${NC}"
        return 1
    fi
    echo ""
}

# Main menu
show_menu() {
    echo "Please select a backup schedule:"
    echo ""
    echo "1) Daily at 2:00 AM"
    echo "2) Daily at 6:00 PM"
    echo "3) Weekly on Sunday at 3:00 AM"
    echo "4) Custom schedule"
    echo "5) Show current cron jobs"
    echo "6) Remove all backup cron jobs"
    echo "7) Test backup script"
    echo "8) Exit"
    echo ""
}

# Handle user input
handle_choice() {
    local choice="$1"
    
    case $choice in
        1)
            add_cron_job "0 2 * * *" "Daily backup at 2:00 AM"
            ;;
        2)
            add_cron_job "0 18 * * *" "Daily backup at 6:00 PM"
            ;;
        3)
            add_cron_job "0 3 * * 0" "Weekly backup on Sunday at 3:00 AM"
            ;;
        4)
            echo -e "${YELLOW}Enter custom cron schedule (e.g., '0 2 * * *' for daily at 2 AM):${NC}"
            read -r custom_schedule
            if [ -n "$custom_schedule" ]; then
                add_cron_job "$custom_schedule" "Custom backup schedule"
            else
                echo -e "${RED}Invalid schedule entered${NC}"
            fi
            ;;
        5)
            show_cron_jobs
            ;;
        6)
            remove_cron_jobs
            ;;
        7)
            test_backup
            ;;
        8)
            echo -e "${GREEN}Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid choice. Please try again.${NC}"
            ;;
    esac
}

# Check if cron is available
if ! command -v crontab >/dev/null 2>&1; then
    echo -e "${RED}Error: crontab command not found. Please install cron.${NC}"
    exit 1
fi

# Create backup directory
mkdir -p "$PROJECT_DIR/backups"

# Check for command line arguments
case "${1:-}" in
    --daily)
        add_cron_job "0 2 * * *" "Daily backup at 2:00 AM"
        exit 0
        ;;
    --weekly)
        add_cron_job "0 3 * * 0" "Weekly backup on Sunday at 3:00 AM"
        exit 0
        ;;
    --remove)
        remove_cron_jobs
        exit 0
        ;;
    --test)
        test_backup
        exit 0
        ;;
    --help|-h)
        echo "ExLog Backup Cron Setup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --daily     Set up daily backup at 2:00 AM"
        echo "  --weekly    Set up weekly backup on Sunday at 3:00 AM"
        echo "  --remove    Remove all backup cron jobs"
        echo "  --test      Test the backup script"
        echo "  --help, -h  Show this help message"
        echo ""
        echo "If no options are provided, an interactive menu will be shown."
        exit 0
        ;;
esac

# Interactive mode
echo -e "${BLUE}Setting up automated backups for ExLog...${NC}"
echo ""

# Test backup script first
if ! test_backup; then
    echo -e "${RED}Backup script test failed. Please fix the issues before setting up cron.${NC}"
    exit 1
fi

# Show current status
show_cron_jobs

# Interactive menu loop
while true; do
    show_menu
    echo -n "Enter your choice (1-8): "
    read -r choice
    echo ""
    
    handle_choice "$choice"
    
    if [ "$choice" != "8" ]; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        echo ""
    fi
done

@echo off
REM ExLog Log Storage Setup Script for Windows
REM This script creates the necessary directories for persistent log storage

setlocal enabledelayedexpansion

echo ExLog Log Storage Setup
echo ========================================
echo.

REM Default paths
set "DEFAULT_LOG_HOST_PATH=.\data\logs"
set "DEFAULT_LOG_STORAGE_HOST_PATH=.\data\logs\storage"
set "DEFAULT_LOG_ARCHIVE_HOST_PATH=.\data\logs\archive"

REM Get paths from environment variables or use defaults
if "%LOG_HOST_PATH%"=="" set "LOG_HOST_PATH=%DEFAULT_LOG_HOST_PATH%"
if "%LOG_STORAGE_HOST_PATH%"=="" set "LOG_STORAGE_HOST_PATH=%DEFAULT_LOG_STORAGE_HOST_PATH%"
if "%LOG_ARCHIVE_HOST_PATH%"=="" set "LOG_ARCHIVE_HOST_PATH=%DEFAULT_LOG_ARCHIVE_HOST_PATH%"

echo Setting up log storage directories...
echo.

REM Function to create directory
call :create_directory "%LOG_HOST_PATH%" "main log"
call :create_directory "%LOG_STORAGE_HOST_PATH%" "log storage"
call :create_directory "%LOG_ARCHIVE_HOST_PATH%" "log archive"

REM Check if .env file exists
if not exist ".env" (
    echo Creating .env file with log storage configuration...
    (
        echo # Log Storage Configuration
        echo LOG_HOST_PATH=%LOG_HOST_PATH%
        echo LOG_STORAGE_HOST_PATH=%LOG_STORAGE_HOST_PATH%
        echo LOG_ARCHIVE_HOST_PATH=%LOG_ARCHIVE_HOST_PATH%
        echo.
        echo # Log Retention Configuration
        echo LOG_RETENTION_SECONDS=7776000
        echo ALERT_RETENTION_SECONDS=31536000
        echo ENABLE_AUTO_DELETE=false
        echo ARCHIVE_BEFORE_DELETE=true
        echo ARCHIVE_RETENTION_DAYS=2555
        echo.
        echo # Log Compression
        echo LOG_COMPRESSION_ENABLED=true
        echo LOG_COMPRESSION_ALGORITHM=gzip
        echo LOG_COMPRESSION_LEVEL=6
        echo.
        echo # External Storage ^(uncomment and configure if needed^)
        echo # EXTERNAL_STORAGE_TYPE=local
        echo # S3_BUCKET=your-log-bucket
        echo # S3_REGION=us-east-1
        echo # S3_ACCESS_KEY_ID=your-access-key
        echo # S3_SECRET_ACCESS_KEY=your-secret-key
    ) > .env
    echo [32m✓ .env file created[0m
) else (
    echo [33m⚠ .env file already exists. Please manually add log storage configuration if needed.[0m
)

echo.
echo [32m✓ Log storage setup completed successfully![0m
echo.
echo Directory Structure:
echo ├── %LOG_HOST_PATH% ^(Application logs^)
echo ├── %LOG_STORAGE_HOST_PATH% ^(Processed log storage^)
echo └── %LOG_ARCHIVE_HOST_PATH% ^(Archived logs^)
echo.
echo Next Steps:
echo 1. Review the .env file and adjust paths if needed
echo 2. Configure external storage ^(S3, Azure, GCP^) if required
echo 3. Start the application with: docker-compose up -d
echo.
echo Note: These directories will persist log data even when containers are stopped or removed.

goto :eof

:create_directory
set "dir_path=%~1"
set "description=%~2"

echo Creating %description% directory: %dir_path%

if exist "%dir_path%" (
    echo [32m✓ Directory already exists[0m
) else (
    mkdir "%dir_path%" 2>nul
    if !errorlevel! equ 0 (
        echo [32m✓ Directory created[0m
    ) else (
        echo [31m✗ Failed to create directory[0m
    )
)
echo.
goto :eof

#!/bin/bash

# ExLog Log Storage Setup Script
# This script creates the necessary directories for persistent log storage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default paths
DEFAULT_LOG_HOST_PATH="./data/logs"
DEFAULT_LOG_STORAGE_HOST_PATH="./data/logs/storage"
DEFAULT_LOG_ARCHIVE_HOST_PATH="./data/logs/archive"

# Get paths from environment variables or use defaults
LOG_HOST_PATH=${LOG_HOST_PATH:-$DEFAULT_LOG_HOST_PATH}
LOG_STORAGE_HOST_PATH=${LOG_STORAGE_HOST_PATH:-$DEFAULT_LOG_STORAGE_HOST_PATH}
LOG_ARCHIVE_HOST_PATH=${LOG_ARCHIVE_HOST_PATH:-$DEFAULT_LOG_ARCHIVE_HOST_PATH}

echo -e "${BLUE}ExLog Log Storage Setup${NC}"
echo "========================================"
echo ""

# Function to create directory with proper permissions
create_directory() {
    local dir_path="$1"
    local description="$2"
    
    echo -e "${YELLOW}Creating $description directory: $dir_path${NC}"
    
    if [ -d "$dir_path" ]; then
        echo -e "${GREEN}✓ Directory already exists${NC}"
    else
        mkdir -p "$dir_path"
        echo -e "${GREEN}✓ Directory created${NC}"
    fi
    
    # Set appropriate permissions
    chmod 755 "$dir_path"
    echo -e "${GREEN}✓ Permissions set (755)${NC}"
    echo ""
}

# Function to check disk space
check_disk_space() {
    local dir_path="$1"
    local min_space_gb=5
    
    # Get available space in GB
    local available_space=$(df -BG "$dir_path" | awk 'NR==2 {print $4}' | sed 's/G//')
    
    if [ "$available_space" -lt "$min_space_gb" ]; then
        echo -e "${RED}⚠ Warning: Low disk space in $dir_path (${available_space}GB available)${NC}"
        echo -e "${YELLOW}  Recommended minimum: ${min_space_gb}GB${NC}"
    else
        echo -e "${GREEN}✓ Sufficient disk space: ${available_space}GB available${NC}"
    fi
}

# Main setup
echo -e "${BLUE}Setting up log storage directories...${NC}"
echo ""

# Create directories
create_directory "$LOG_HOST_PATH" "main log"
create_directory "$LOG_STORAGE_HOST_PATH" "log storage"
create_directory "$LOG_ARCHIVE_HOST_PATH" "log archive"

# Check disk space
echo -e "${BLUE}Checking disk space...${NC}"
check_disk_space "$(dirname "$LOG_HOST_PATH")"
echo ""

# Create .env file with paths if it doesn't exist
ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}Creating .env file with log storage configuration...${NC}"
    cat > "$ENV_FILE" << EOF
# Log Storage Configuration
LOG_HOST_PATH=$LOG_HOST_PATH
LOG_STORAGE_HOST_PATH=$LOG_STORAGE_HOST_PATH
LOG_ARCHIVE_HOST_PATH=$LOG_ARCHIVE_HOST_PATH

# Log Retention Configuration
LOG_RETENTION_SECONDS=7776000
ALERT_RETENTION_SECONDS=31536000
ENABLE_AUTO_DELETE=false
ARCHIVE_BEFORE_DELETE=true
ARCHIVE_RETENTION_DAYS=2555

# Log Compression
LOG_COMPRESSION_ENABLED=true
LOG_COMPRESSION_ALGORITHM=gzip
LOG_COMPRESSION_LEVEL=6

# External Storage (uncomment and configure if needed)
# EXTERNAL_STORAGE_TYPE=local
# S3_BUCKET=your-log-bucket
# S3_REGION=us-east-1
# S3_ACCESS_KEY_ID=your-access-key
# S3_SECRET_ACCESS_KEY=your-secret-key
EOF
    echo -e "${GREEN}✓ .env file created${NC}"
else
    echo -e "${YELLOW}⚠ .env file already exists. Please manually add log storage configuration if needed.${NC}"
fi

echo ""
echo -e "${GREEN}✓ Log storage setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}Directory Structure:${NC}"
echo "├── $LOG_HOST_PATH (Application logs)"
echo "├── $LOG_STORAGE_HOST_PATH (Processed log storage)"
echo "└── $LOG_ARCHIVE_HOST_PATH (Archived logs)"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Review the .env file and adjust paths if needed"
echo "2. Configure external storage (S3, Azure, GCP) if required"
echo "3. Start the application with: docker-compose up -d"
echo ""
echo -e "${YELLOW}Note: These directories will persist log data even when containers are stopped or removed.${NC}"

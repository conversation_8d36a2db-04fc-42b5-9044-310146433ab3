#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

DOMAIN=""
EMAIL=""
STAGING=false
FORCE_RENEWAL=false
NGINX_CONTAINER="exlog-nginx"
COMPOSE_FILE="docker-compose.yml"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
ExLog Dashboard - Automated SSL Setup

Usage: $0 [OPTIONS]

OPTIONS:
    -d, --domain DOMAIN     Domain name for SSL certificate (required)
    -e, --email EMAIL       Email address for Let's Encrypt (required)
    -s, --staging           Use Let's Encrypt staging environment (for testing)
    -f, --force             Force certificate renewal
    -c, --compose FILE      Docker compose file path (default: docker-compose.yml)
    -h, --help              Show this help message

EXAMPLES:
    $0 -d example.com -e <EMAIL>
    
    $0 -d example.com -e <EMAIL> --staging
    
    $0 -d example.com -e <EMAIL> --force

REQUIREMENTS:
    - Docker and Docker Compose installed
    - Domain pointing to this server's public IP
    - Ports 80 and 443 accessible from internet
    - ExLog Dashboard containers running

EOF
}

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL="$2"
            shift 2
            ;;
        -s|--staging)
            STAGING=true
            shift
            ;;
        -f|--force)
            FORCE_RENEWAL=true
            shift
            ;;
        -c|--compose)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

if [[ -z "$DOMAIN" ]]; then
    print_error "Domain name is required. Use -d or --domain option."
    show_usage
    exit 1
fi

if [[ -z "$EMAIL" ]]; then
    print_error "Email address is required. Use -e or --email option."
    show_usage
    exit 1
fi

if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
    print_error "Invalid email format: $EMAIL"
    exit 1
fi

if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. Consider using a non-root user with sudo privileges."
fi

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker service."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
else
    DOCKER_COMPOSE="docker compose"
fi

print_status "Starting SSL setup for domain: $DOMAIN"
print_status "Email: $EMAIL"
print_status "Staging mode: $STAGING"
print_status "Force renewal: $FORCE_RENEWAL"

if [[ ! -f "$COMPOSE_FILE" ]]; then
    print_error "Docker compose file not found: $COMPOSE_FILE"
    exit 1
fi

print_status "Creating SSL certificate directories..."
sudo mkdir -p /etc/letsencrypt/live
sudo mkdir -p /etc/letsencrypt/archive
sudo mkdir -p /var/lib/letsencrypt
sudo mkdir -p /var/log/letsencrypt

if ! command -v certbot &> /dev/null; then
    print_status "Installing Certbot..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y certbot
    elif command -v yum &> /dev/null; then
        sudo yum install -y certbot
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y certbot
    else
        print_error "Cannot install Certbot automatically. Please install it manually."
        exit 1
    fi
fi

print_status "Checking ExLog Dashboard containers..."
if ! $DOCKER_COMPOSE -f "$COMPOSE_FILE" ps | grep -q "Up"; then
    print_error "ExLog Dashboard containers are not running. Please start them first:"
    print_error "$DOCKER_COMPOSE -f $COMPOSE_FILE up -d"
    exit 1
fi

print_status "Temporarily stopping nginx for certificate generation..."
$DOCKER_COMPOSE -f "$COMPOSE_FILE" stop nginx || true

CERTBOT_CMD="certbot certonly --standalone"
CERTBOT_CMD="$CERTBOT_CMD --non-interactive --agree-tos"
CERTBOT_CMD="$CERTBOT_CMD --email $EMAIL"
CERTBOT_CMD="$CERTBOT_CMD -d $DOMAIN"

if [[ "$STAGING" == true ]]; then
    CERTBOT_CMD="$CERTBOT_CMD --staging"
    print_warning "Using Let's Encrypt staging environment (certificates will not be trusted by browsers)"
fi

if [[ "$FORCE_RENEWAL" == true ]]; then
    CERTBOT_CMD="$CERTBOT_CMD --force-renewal"
fi

print_status "Generating SSL certificate for $DOMAIN..."
if sudo $CERTBOT_CMD; then
    print_success "SSL certificate generated successfully!"
else
    print_error "Failed to generate SSL certificate"
    print_status "Restarting nginx..."
    $DOCKER_COMPOSE -f "$COMPOSE_FILE" start nginx
    exit 1
fi

print_status "Creating SSL-enabled nginx configuration..."
SSL_NGINX_CONF="/tmp/nginx-ssl.conf"

cat > "$SSL_NGINX_CONF" << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    limit_req_zone $binary_remote_addr zone=api:10m rate=10000r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=20r/m;

    resolver 127.0.0.11 valid=30s;

    upstream frontend {
        server frontend:3000;
    }

    upstream backend {
        server backend:5000;
    }

    upstream websocket {
        server websocket:5001;
    }

    upstream ai_service {
        server ai-insights:5002;
    }

    server {
        listen 80;
        server_name DOMAIN_PLACEHOLDER;
        
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }
        
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    server {
        listen 443 ssl http2;
        server_name DOMAIN_PLACEHOLDER;

        ssl_certificate /etc/letsencrypt/live/DOMAIN_PLACEHOLDER/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/DOMAIN_PLACEHOLDER/privkey.pem;
        
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_timeout 1d;
        ssl_session_cache shared:MozTLS:10m;
        ssl_session_tickets off;

        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' http: https: ws: wss:; img-src 'self' data: https:; frame-ancestors 'self';" always;

        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        location /api/ai/ {
            limit_req zone=api burst=10 nodelay;

            proxy_pass http://ai_service/api/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        location /api/v1/auth/login {
            limit_req zone=login burst=5 nodelay;

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /ws {
            proxy_pass http://websocket;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        location / {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}
EOF

sed -i "s/DOMAIN_PLACEHOLDER/$DOMAIN/g" "$SSL_NGINX_CONF"

print_status "Updating nginx configuration for SSL..."
if [[ -f "nginx/nginx.conf" ]]; then
    cp nginx/nginx.conf nginx/nginx.conf.backup
    cp "$SSL_NGINX_CONF" nginx/nginx.conf
elif [[ -f "quick-start/nginx.conf" ]]; then
    cp quick-start/nginx.conf quick-start/nginx.conf.backup
    cp "$SSL_NGINX_CONF" quick-start/nginx.conf
else
    print_error "Could not find nginx configuration file"
    exit 1
fi

print_status "Updating Docker Compose configuration for SSL..."
COMPOSE_BACKUP="${COMPOSE_FILE}.backup"
cp "$COMPOSE_FILE" "$COMPOSE_BACKUP"

if grep -q "volumes:" "$COMPOSE_FILE"; then
    if ! grep -q "/etc/letsencrypt" "$COMPOSE_FILE"; then
        sed -i '/nginx:/,/volumes:/{
            /volumes:/a\
      - /etc/letsencrypt:/etc/letsencrypt:ro\
      - /var/www/certbot:/var/www/certbot:ro
        }' "$COMPOSE_FILE"
    fi
else
    sed -i '/nginx:/,/^[[:space:]]*[^[:space:]]/{
        /restart:/a\
    volumes:\
      - /etc/letsencrypt:/etc/letsencrypt:ro\
      - /var/www/certbot:/var/www/certbot:ro
    }' "$COMPOSE_FILE"
fi

if ! grep -q "443:443" "$COMPOSE_FILE"; then
    sed -i '/nginx:/,/ports:/{
        /- "8080:80"/a\
      - "443:443"
    }' "$COMPOSE_FILE"
fi

print_status "Restarting nginx with SSL configuration..."
$DOCKER_COMPOSE -f "$COMPOSE_FILE" up -d nginx

sleep 5

print_status "Testing SSL configuration..."
if curl -k -s "https://$DOMAIN/health" | grep -q "healthy"; then
    print_success "SSL configuration is working!"
else
    print_warning "SSL test failed, but this might be due to DNS propagation. Please test manually."
fi

print_status "Setting up automatic certificate renewal..."
RENEWAL_SCRIPT="/etc/cron.d/exlog-ssl-renewal"
sudo tee "$RENEWAL_SCRIPT" > /dev/null << EOF
0 12 * * * root certbot renew --quiet --deploy-hook "cd $(pwd) && $DOCKER_COMPOSE -f $COMPOSE_FILE restart nginx"
0 0 * * * root certbot renew --quiet --deploy-hook "cd $(pwd) && $DOCKER_COMPOSE -f $COMPOSE_FILE restart nginx"
EOF

print_success "SSL setup completed successfully!"
print_status "Certificate location: /etc/letsencrypt/live/$DOMAIN/"
print_status "Automatic renewal configured via cron"
print_status "Your ExLog Dashboard is now accessible at: https://$DOMAIN"

cat << EOF

${GREEN}Next Steps:${NC}
1. Test your SSL setup by visiting: https://$DOMAIN
2. Update your DNS records if needed
3. Configure firewall to allow HTTPS traffic (port 443)
4. Update any hardcoded HTTP URLs in your application configuration

${YELLOW}Important Notes:${NC}
- Certificate will auto-renew every 60 days
- Backup files created: $COMPOSE_BACKUP, nginx/nginx.conf.backup
- To disable SSL, restore backup files and restart containers

${BLUE}Troubleshooting:${NC}
- Check nginx logs: $DOCKER_COMPOSE -f $COMPOSE_FILE logs nginx
- Verify certificate: openssl x509 -in /etc/letsencrypt/live/$DOMAIN/cert.pem -text -noout
- Test SSL: curl -I https://$DOMAIN

EOF

rm -f "$SSL_NGINX_CONF"

print_success "SSL automation setup complete!"

#!/bin/bash


set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
INSTALL_DIR="${INSTALL_DIR:-$HOME/exlog-dashboard}"

echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                ExLog Dashboard Setup Wizard                 ║${NC}"
echo -e "${BLUE}║              Welcome to ExLog Dashboard!                    ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    case "$level" in
        "INFO") echo -e "${GREEN}[INFO]${NC} $message" ;;
        "WARN") echo -e "${YELLOW}[WARN]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        *) echo -e "${CYAN}[$level]${NC} $message" ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    echo -e "${RED}Setup failed. Please check the error above and try again.${NC}"
    exit 1
}

show_welcome() {
    echo -e "${CYAN}This wizard will help you set up ExLog Dashboard quickly and easily.${NC}"
    echo ""
    echo -e "${BLUE}What you'll get:${NC}"
    echo -e "  • Complete log management and analysis platform"
    echo -e "  • Real-time dashboard with AI-powered insights"
    echo -e "  • Automated security monitoring and alerting"
    echo -e "  • Easy-to-use web interface"
    echo ""
    echo -e "${BLUE}System Requirements:${NC}"
    echo -e "  • Docker and Docker Compose"
    echo -e "  • 2GB+ RAM (4GB recommended)"
    echo -e "  • 5GB+ disk space"
    echo ""
    read -p "Ready to begin? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled. Run this script again when you're ready!"
        exit 0
    fi
    echo ""
}

check_system() {
    log "INFO" "Checking system requirements..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        error_exit "Unsupported operating system: $OSTYPE"
    fi
    
    if [[ "$OS" == "linux" ]]; then
        local mem_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        local mem_gb=$((mem_kb / 1024 / 1024))
    elif [[ "$OS" == "macos" ]]; then
        local mem_bytes=$(sysctl -n hw.memsize)
        local mem_gb=$((mem_bytes / 1024 / 1024 / 1024))
    fi
    
    if [ "$mem_gb" -lt 2 ]; then
        log "WARN" "System has ${mem_gb}GB RAM. 2GB minimum recommended."
        echo -e "${YELLOW}Continue anyway? (y/N):${NC}"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log "INFO" "Memory check passed: ${mem_gb}GB available"
    fi
    
    local available_space=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$available_space" -lt 5 ]; then
        log "WARN" "Low disk space: ${available_space}GB available. 5GB minimum recommended."
        echo -e "${YELLOW}Continue anyway? (y/N):${NC}"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log "INFO" "Disk space check passed: ${available_space}GB available"
    fi
    
    echo ""
}

check_docker() {
    log "INFO" "Checking Docker installation..."
    
    if ! command -v docker >/dev/null 2>&1; then
        echo -e "${YELLOW}Docker is not installed.${NC}"
        echo ""
        echo -e "${BLUE}Installation options:${NC}"
        echo -e "  1. Install Docker automatically (recommended)"
        echo -e "  2. Install Docker manually"
        echo -e "  3. Exit and install Docker yourself"
        echo ""
        read -p "Choose an option (1-3): " -r choice
        
        case "$choice" in
            1)
                install_docker_auto
                ;;
            2)
                show_docker_manual_instructions
                exit 0
                ;;
            3)
                echo "Please install Docker and run this wizard again."
                echo "Visit: https://docs.docker.com/get-docker/"
                exit 0
                ;;
            *)
                error_exit "Invalid choice"
                ;;
        esac
    else
        log "INFO" "Docker is already installed: $(docker --version)"
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log "WARN" "Docker daemon is not running."
        echo -e "${YELLOW}Please start Docker and press Enter to continue...${NC}"
        read -r
        
        if ! docker info >/dev/null 2>&1; then
            error_exit "Docker daemon is still not running. Please start Docker and try again."
        fi
    fi
    
    if ! docker compose version >/dev/null 2>&1 && ! docker-compose --version >/dev/null 2>&1; then
        log "WARN" "Docker Compose is not available."
        install_docker_compose
    else
        log "INFO" "Docker Compose is available"
    fi
    
    echo ""
}

install_docker_auto() {
    log "INFO" "Installing Docker automatically..."
    
    case "$OS" in
        linux)
            if [ -f /etc/os-release ]; then
                . /etc/os-release
                case "$ID" in
                    ubuntu|debian)
                        sudo apt-get update
                        sudo apt-get install -y ca-certificates curl gnupg lsb-release
                        sudo mkdir -p /etc/apt/keyrings
                        curl -fsSL https://download.docker.com/linux/$ID/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
                        echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$ID $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
                        sudo apt-get update
                        sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
                        sudo usermod -aG docker $USER
                        ;;
                    centos|rhel)
                        sudo yum install -y yum-utils
                        sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
                        sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
                        sudo systemctl start docker
                        sudo systemctl enable docker
                        sudo usermod -aG docker $USER
                        ;;
                    *)
                        error_exit "Automatic Docker installation not supported for $ID. Please install manually."
                        ;;
                esac
            else
                error_exit "Cannot detect Linux distribution. Please install Docker manually."
            fi
            ;;
        macos)
            if command -v brew >/dev/null 2>&1; then
                brew install --cask docker
                log "INFO" "Docker Desktop installed. Please start Docker Desktop and return to continue."
                echo -e "${YELLOW}Press Enter when Docker Desktop is running...${NC}"
                read -r
            else
                error_exit "Homebrew is required for automatic installation on macOS. Please install Docker Desktop manually from https://www.docker.com/products/docker-desktop"
            fi
            ;;
        *)
            error_exit "Automatic Docker installation not supported for $OS"
            ;;
    esac
    
    log "INFO" "Docker installation completed"
}

show_docker_manual_instructions() {
    echo -e "${BLUE}Manual Docker Installation Instructions:${NC}"
    echo ""
    case "$OS" in
        linux)
            echo -e "${CYAN}For Ubuntu/Debian:${NC}"
            echo "  curl -fsSL https://get.docker.com -o get-docker.sh"
            echo "  sudo sh get-docker.sh"
            echo "  sudo usermod -aG docker \$USER"
            echo ""
            echo -e "${CYAN}For CentOS/RHEL:${NC}"
            echo "  sudo yum install -y docker"
            echo "  sudo systemctl start docker"
            echo "  sudo systemctl enable docker"
            echo "  sudo usermod -aG docker \$USER"
            ;;
        macos)
            echo -e "${CYAN}For macOS:${NC}"
            echo "  1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop"
            echo "  2. Install and start Docker Desktop"
            echo "  3. Verify installation: docker --version"
            ;;
    esac
    echo ""
    echo "After installing Docker, run this wizard again."
}

install_docker_compose() {
    log "INFO" "Installing Docker Compose..."
    
    case "$OS" in
        linux)
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            ;;
        macos)
            if command -v brew >/dev/null 2>&1; then
                brew install docker-compose
            else
                error_exit "Please install Docker Compose manually"
            fi
            ;;
    esac
    
    log "INFO" "Docker Compose installation completed"
}

choose_installation_method() {
    echo -e "${BLUE}Choose Installation Method:${NC}"
    echo ""
    echo -e "${CYAN}1. Quick Start (Recommended)${NC}"
    echo -e "   • Uses pre-built Docker images from Docker Hub"
    echo -e "   • Fastest setup (5 minutes)"
    echo -e "   • Perfect for evaluation and production use"
    echo ""
    echo -e "${CYAN}2. Local Build${NC}"
    echo -e "   • Builds images from source code"
    echo -e "   • Longer setup time (15-30 minutes)"
    echo -e "   • Good for development and customization"
    echo ""
    read -p "Choose installation method (1-2): " -r method
    
    case "$method" in
        1)
            INSTALLATION_METHOD="quick"
            log "INFO" "Selected: Quick Start with Docker Hub images"
            ;;
        2)
            INSTALLATION_METHOD="local"
            log "INFO" "Selected: Local build from source"
            ;;
        *)
            error_exit "Invalid choice"
            ;;
    esac
    echo ""
}

configure_directory() {
    echo -e "${BLUE}Installation Directory:${NC}"
    echo -e "Default: ${INSTALL_DIR}"
    echo ""
    read -p "Use default directory? (Y/n): " -r use_default
    
    if [[ "$use_default" =~ ^[Nn]$ ]]; then
        read -p "Enter installation directory: " -r custom_dir
        if [ -n "$custom_dir" ]; then
            INSTALL_DIR="$custom_dir"
        fi
    fi
    
    log "INFO" "Installation directory: $INSTALL_DIR"
    echo ""
}

configure_security() {
    echo -e "${BLUE}Security Configuration:${NC}"
    echo ""
    echo -e "${YELLOW}Important: Change default passwords for production use!${NC}"
    echo ""
    
    echo -e "${CYAN}JWT Secret (for session security):${NC}"
    read -p "Generate secure JWT secret automatically? (Y/n): " -r auto_jwt
    
    if [[ "$auto_jwt" =~ ^[Nn]$ ]]; then
        read -p "Enter JWT secret (leave empty for auto-generation): " -r jwt_secret
    fi
    
    if [ -z "$jwt_secret" ]; then
        jwt_secret=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
        log "INFO" "Generated secure JWT secret"
    fi
    
    echo ""
    echo -e "${CYAN}MongoDB Password:${NC}"
    read -p "Generate secure MongoDB password automatically? (Y/n): " -r auto_mongo
    
    if [[ "$auto_mongo" =~ ^[Nn]$ ]]; then
        read -s -p "Enter MongoDB password (leave empty for auto-generation): " mongo_password
        echo ""
    fi
    
    if [ -z "$mongo_password" ]; then
        mongo_password=$(openssl rand -base64 16 2>/dev/null || head -c 16 /dev/urandom | base64)
        log "INFO" "Generated secure MongoDB password"
    fi
    
    echo ""
}

configure_network() {
    echo -e "${BLUE}Network Configuration:${NC}"
    echo ""
    
    echo -e "${CYAN}Web Interface Port:${NC}"
    read -p "Port for web interface (default: 8080): " -r web_port
    web_port="${web_port:-8080}"
    
    if netstat -tuln 2>/dev/null | grep -q ":$web_port "; then
        log "WARN" "Port $web_port is already in use"
        read -p "Use a different port? (Y/n): " -r change_port
        if [[ ! "$change_port" =~ ^[Nn]$ ]]; then
            read -p "Enter alternative port: " -r web_port
        fi
    fi
    
    log "INFO" "Web interface will be available at: http://localhost:$web_port"
    echo ""
}

create_installation() {
    log "INFO" "Creating installation directory: $INSTALL_DIR"
    
    if [ -d "$INSTALL_DIR" ]; then
        log "WARN" "Directory already exists. Creating backup..."
        mv "$INSTALL_DIR" "${INSTALL_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$INSTALL_DIR"
    cd "$INSTALL_DIR"
    
    if [ "$INSTALLATION_METHOD" = "quick" ]; then
        log "INFO" "Downloading Docker Compose configuration..."
        curl -fsSL "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/docker-compose.yml" -o docker-compose.yml
        curl -fsSL "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/.env.template" -o .env
        curl -fsSL "https://gitlab.com/spr888/dashboard/-/raw/main/quick-start/nginx.conf" -o nginx.conf
    else
        log "INFO" "Cloning repository for local build..."
        if ! command -v git >/dev/null 2>&1; then
            error_exit "Git is required for local build. Please install Git and try again."
        fi
        git clone https://gitlab.com/spr888/dashboard.git .
        cp .env.example .env
    fi
    
    log "INFO" "Configuring environment..."
    
    sed -i.bak \
        -e "s/NODE_ENV=development/NODE_ENV=production/" \
        -e "s/your-super-secret-jwt-key-change-in-production-please/$jwt_secret/" \
        -e "s/MONGODB_PASSWORD=password/MONGODB_PASSWORD=$mongo_password/" \
        -e "s/CORS_ORIGIN=\*/CORS_ORIGIN=http:\/\/localhost:$web_port/" \
        .env
    
    if [ "$web_port" != "8080" ]; then
        sed -i.bak "s/8080:80/$web_port:80/" docker-compose.yml
    fi
    
    rm -f .env.bak docker-compose.yml.bak 2>/dev/null || true
    
    log "INFO" "Installation files created successfully"
}

start_services() {
    log "INFO" "Starting ExLog Dashboard..."
    
    if [ "$INSTALLATION_METHOD" = "quick" ]; then
        log "INFO" "Pulling Docker images..."
        docker compose pull
    else
        log "INFO" "Building Docker images (this may take 15-30 minutes)..."
        docker compose build
    fi
    
    log "INFO" "Starting services..."
    docker compose up -d
    
    log "INFO" "Waiting for services to start..."
    local max_attempts=60
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s "http://localhost:$web_port/health" >/dev/null 2>&1; then
            break
        fi
        sleep 2
        attempt=$((attempt + 1))
        
        if [ $((attempt % 10)) -eq 0 ]; then
            log "INFO" "Still waiting for services to start... ($attempt/$max_attempts)"
        fi
    done
    
    if [ $attempt -eq $max_attempts ]; then
        error_exit "Services failed to start within expected time. Check logs with: docker compose logs"
    fi
    
    log "INFO" "ExLog Dashboard started successfully!"
}

show_completion() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    🎉 Setup Complete! 🎉                    ║${NC}"
    echo -e "${GREEN}║              ExLog Dashboard is now running!                 ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${BLUE}Access your dashboard:${NC}"
    echo -e "  🌐 Web Interface: ${CYAN}http://localhost:$web_port${NC}"
    echo ""
    echo -e "${BLUE}Default Login Credentials:${NC}"
    echo -e "  👤 Username: ${CYAN}admin${NC}"
    echo -e "  🔑 Password: ${CYAN}admin123${NC}"
    echo -e "  ${YELLOW}⚠️  Please change the password after first login!${NC}"
    echo ""
    echo -e "${BLUE}Installation Details:${NC}"
    echo -e "  📁 Directory: ${CYAN}$INSTALL_DIR${NC}"
    echo -e "  🐳 Method: ${CYAN}$INSTALLATION_METHOD${NC}"
    echo -e "  🔒 JWT Secret: ${CYAN}Configured${NC}"
    echo -e "  🗄️  MongoDB Password: ${CYAN}Configured${NC}"
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    echo -e "  📊 View logs: ${YELLOW}docker compose logs -f${NC}"
    echo -e "  🛑 Stop services: ${YELLOW}docker compose down${NC}"
    echo -e "  🔄 Restart services: ${YELLOW}docker compose restart${NC}"
    echo -e "  ⬆️  Update: ${YELLOW}docker compose pull && docker compose up -d${NC}"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo -e "  1. 🔐 Change default admin password"
    echo -e "  2. 📝 Configure log sources and agents"
    echo -e "  3. 🚨 Set up alerting rules"
    echo -e "  4. 💾 Configure backup schedules"
    echo ""
    echo -e "${BLUE}Documentation:${NC}"
    echo -e "  📖 User Guide: ${CYAN}https://gitlab.com/spr888/dashboard/-/blob/main/README.md${NC}"
    echo -e "  🚀 Deployment Guide: ${CYAN}https://gitlab.com/spr888/dashboard/-/blob/main/DEPLOYMENT.md${NC}"
    echo -e "  🔧 Troubleshooting: ${CYAN}https://gitlab.com/spr888/dashboard/-/blob/main/docs/TROUBLESHOOTING.md${NC}"
    echo ""
    echo -e "${GREEN}Thank you for choosing ExLog Dashboard!${NC}"
    echo ""
}

main() {
    show_welcome
    check_system
    check_docker
    choose_installation_method
    configure_directory
    configure_security
    configure_network
    create_installation
    start_services
    show_completion
}

case "${1:-}" in
    --help|-h)
        echo "ExLog Dashboard Interactive Setup Wizard"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --quick             Skip interactive prompts and use defaults"
        echo "  --install-dir DIR   Set installation directory"
        echo ""
        echo "Environment Variables:"
        echo "  INSTALL_DIR         Installation directory"
        echo ""
        exit 0
        ;;
    --quick)
        INSTALLATION_METHOD="quick"
        jwt_secret=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
        mongo_password=$(openssl rand -base64 16 2>/dev/null || head -c 16 /dev/urandom | base64)
        web_port="8080"
        
        check_system
        check_docker
        create_installation
        start_services
        show_completion
        ;;
    *)
        main "$@"
        ;;
esac

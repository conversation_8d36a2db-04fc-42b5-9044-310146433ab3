#!/usr/bin/env node

/**
 * Test script for AI Insights service
 * This script tests the AI service endpoints and functionality
 */

const axios = require('axios');

// Configuration
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:5002';
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5000';

// Test data
const testLogs = [
  {
    _id: '1',
    message: 'Failed login attempt for user admin from *************',
    logLevel: 'error',
    sourceType: 'security',
    severity: 3,
    timestamp: new Date().toISOString()
  },
  {
    _id: '2',
    message: 'Suspicious process execution detected: powershell.exe',
    logLevel: 'warn',
    sourceType: 'system',
    severity: 2,
    timestamp: new Date().toISOString()
  },
  {
    _id: '3',
    message: 'Large file transfer detected: 500MB uploaded',
    logLevel: 'info',
    sourceType: 'network',
    severity: 1,
    timestamp: new Date().toISOString()
  },
  {
    _id: '4',
    message: 'Multiple failed authentication attempts detected',
    logLevel: 'error',
    sourceType: 'security',
    severity: 4,
    timestamp: new Date().toISOString()
  },
  {
    _id: '5',
    message: 'Unauthorized access attempt to admin panel',
    logLevel: 'error',
    sourceType: 'security',
    severity: 4,
    timestamp: new Date().toISOString()
  }
];

// Helper function to make HTTP requests with error handling
async function makeRequest(url, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data
    };
  }
}

// Test functions
async function testAIServiceHealth() {
  console.log('\n🔍 Testing AI Service Health...');
  const result = await makeRequest(`${AI_SERVICE_URL}/health`);
  
  if (result.success) {
    console.log('✅ AI Service is healthy');
    console.log(`   Status: ${result.data.status}`);
    console.log(`   Service: ${result.data.service}`);
    console.log(`   AI Analyzer: ${result.data.aiAnalyzer}`);
  } else {
    console.log('❌ AI Service health check failed');
    console.log(`   Error: ${result.error}`);
  }
  
  return result.success;
}

async function testAnomalyDetection() {
  console.log('\n🤖 Testing Anomaly Detection...');
  const result = await makeRequest(`${AI_SERVICE_URL}/api/analyze`, 'POST', {
    timeRange: '1h',
    logTypes: ['error', 'warn'],
    options: {}
  });
  
  if (result.success) {
    console.log('✅ Anomaly detection completed');
    console.log(`   Total logs analyzed: ${result.data.summary?.totalLogs || 0}`);
    console.log(`   Anomalies detected: ${result.data.summary?.anomalies || 0}`);
    console.log(`   Threats identified: ${result.data.summary?.threats || 0}`);
    console.log(`   Risk level: ${result.data.summary?.riskLevel || 'unknown'}`);
  } else {
    console.log('❌ Anomaly detection failed');
    console.log(`   Error: ${result.error}`);
    if (result.data) {
      console.log(`   Details: ${JSON.stringify(result.data, null, 2)}`);
    }
  }
  
  return result.success;
}

async function testInsightsEndpoint() {
  console.log('\n📊 Testing Insights Endpoint...');
  const result = await makeRequest(`${AI_SERVICE_URL}/api/insights`);
  
  if (result.success) {
    console.log('✅ Insights endpoint working');
    console.log(`   Summary available: ${!!result.data.summary}`);
    console.log(`   Anomalies: ${result.data.anomalies?.length || 0}`);
    console.log(`   Threats: ${result.data.threats?.length || 0}`);
  } else {
    console.log('❌ Insights endpoint failed');
    console.log(`   Error: ${result.error}`);
  }
  
  return result.success;
}

async function testPerformanceMetrics() {
  console.log('\n📈 Testing Performance Metrics...');
  const result = await makeRequest(`${AI_SERVICE_URL}/api/performance`);
  
  if (result.success) {
    console.log('✅ Performance metrics available');
    console.log(`   Total analyses: ${result.data.totalAnalyses || 0}`);
    console.log(`   Average response time: ${Math.round(result.data.averageResponseTime || 0)}ms`);
    console.log(`   Memory usage: ${Math.round((result.data.memoryUsage?.heapUsed || 0) / 1024 / 1024)}MB`);
    console.log(`   Uptime: ${Math.round((result.data.uptime || 0) / 1000 / 60)}m`);
  } else {
    console.log('❌ Performance metrics failed');
    console.log(`   Error: ${result.error}`);
  }
  
  return result.success;
}

async function testBackendIntegration() {
  console.log('\n🔗 Testing Backend Integration...');
  
  // Test if backend AI routes are accessible
  const result = await makeRequest(`${BACKEND_URL}/api/v1/ai/insights`);
  
  if (result.success) {
    console.log('✅ Backend AI integration working');
    console.log(`   Response: ${result.data.success ? 'Success' : 'Failed'}`);
  } else {
    console.log('❌ Backend AI integration failed');
    console.log(`   Error: ${result.error}`);
    console.log(`   Status: ${result.status}`);
    
    if (result.status === 401) {
      console.log('   Note: Authentication required for backend endpoints');
    } else if (result.status === 503) {
      console.log('   Note: AI service may not be running or accessible from backend');
    }
  }
  
  return result.success;
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting AI Insights Service Tests');
  console.log('=====================================');
  
  const tests = [
    { name: 'AI Service Health', fn: testAIServiceHealth },
    { name: 'Anomaly Detection', fn: testAnomalyDetection },
    { name: 'Insights Endpoint', fn: testInsightsEndpoint },
    { name: 'Performance Metrics', fn: testPerformanceMetrics },
    { name: 'Backend Integration', fn: testBackendIntegration }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const success = await test.fn();
      results.push({ name: test.name, success });
    } catch (error) {
      console.log(`❌ ${test.name} threw an error: ${error.message}`);
      results.push({ name: test.name, success: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n📋 Test Summary');
  console.log('================');
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log(`\n🎯 Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! AI Insights service is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
    console.log('\nTroubleshooting tips:');
    console.log('- Ensure AI service is running on port 5002');
    console.log('- Ensure backend service is running on port 5000');
    console.log('- Check Docker containers are up and healthy');
    console.log('- Verify MongoDB is accessible');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('AI Insights Service Test Script');
  console.log('Usage: node test-ai-service.js [options]');
  console.log('');
  console.log('Environment Variables:');
  console.log('  AI_SERVICE_URL  - AI service URL (default: http://localhost:5002)');
  console.log('  BACKEND_URL     - Backend service URL (default: http://localhost:5000)');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h      - Show this help message');
  process.exit(0);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error.message);
  process.exit(1);
});

const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = 'mongodb://mongodb:27017/exlog';

// Sample log data
const sampleLogs = [
  // Security logs
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'error',
    message: 'Failed login attempt from IP *************',
    sourceType: 'security',
    source: 'Security',
    host: 'server-1.example.com',
    severity: 4,
    userId: 'user-123',
    sessionId: 'session-456',
    additionalFields: {
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (compatible; ExLog/1.0)',
      category: 'security'
    },
    metadata: {
      agentId: 'agent-1',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'warning',
    message: 'Multiple failed login attempts detected',
    sourceType: 'security',
    source: 'Security',
    host: 'server-1.example.com',
    severity: 3,
    userId: 'user-123',
    sessionId: 'session-789',
    additionalFields: {
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (compatible; ExLog/1.0)',
      category: 'security'
    },
    metadata: {
      agentId: 'agent-1',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'critical',
    message: 'Brute force attack in progress',
    sourceType: 'security',
    source: 'Security',
    host: 'server-2.example.com',
    severity: 5,
    userId: 'unknown',
    sessionId: 'session-attack',
    additionalFields: {
      ipAddress: '*********',
      userAgent: 'curl/7.68.0',
      category: 'security'
    },
    metadata: {
      agentId: 'agent-2',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  // Anomaly logs
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'warning',
    message: 'Unusual network traffic pattern detected',
    sourceType: 'network',
    source: 'Network',
    host: 'server-3.example.com',
    severity: 3,
    userId: 'system',
    sessionId: 'system-monitor',
    additionalFields: {
      ipAddress: '***********',
      userAgent: 'System Monitor',
      category: 'anomalies'
    },
    metadata: {
      agentId: 'agent-3',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'error',
    message: 'Process injection attempt detected',
    sourceType: 'security',
    source: 'System',
    host: 'server-4.example.com',
    severity: 4,
    userId: 'admin',
    sessionId: 'admin-session',
    additionalFields: {
      ipAddress: '***********0',
      userAgent: 'Windows NT',
      category: 'anomalies'
    },
    metadata: {
      agentId: 'agent-4',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  // Error logs
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'error',
    message: 'Database connection failed',
    sourceType: 'application',
    source: 'Application',
    host: 'db-server.example.com',
    severity: 4,
    userId: 'dbuser',
    sessionId: 'db-session',
    additionalFields: {
      ipAddress: '************',
      userAgent: 'Database Client',
      category: 'errors'
    },
    metadata: {
      agentId: 'agent-5',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'critical',
    message: 'Disk space critical',
    sourceType: 'event',
    source: 'System',
    host: 'storage-server.example.com',
    severity: 5,
    userId: 'system',
    sessionId: 'system-alert',
    additionalFields: {
      ipAddress: '************',
      userAgent: 'System Monitor',
      category: 'errors'
    },
    metadata: {
      agentId: 'agent-6',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  // Normal logs
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'info',
    message: 'User login successful',
    sourceType: 'application',
    source: 'Application',
    host: 'app-server.example.com',
    severity: 2,
    userId: 'user-456',
    sessionId: 'session-success',
    additionalFields: {
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      category: 'normal'
    },
    metadata: {
      agentId: 'agent-7',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'info',
    message: 'File uploaded successfully',
    sourceType: 'application',
    source: 'Application',
    host: 'web-server.example.com',
    severity: 2,
    userId: 'user-789',
    sessionId: 'upload-session',
    additionalFields: {
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      category: 'normal'
    },
    metadata: {
      agentId: 'agent-8',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  },
  {
    logId: 'log-' + Math.random().toString(36).substr(2, 9),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    logLevel: 'debug',
    message: 'Cache cleared',
    sourceType: 'application',
    source: 'Application',
    host: 'cache-server.example.com',
    severity: 1,
    userId: 'admin',
    sessionId: 'maintenance',
    additionalFields: {
      ipAddress: '************',
      userAgent: 'Admin Console',
      category: 'normal'
    },
    metadata: {
      agentId: 'agent-9',
      agentVersion: '1.0.0',
      sourceMetadata: { generated: true }
    }
  }
];

async function generateTestLogs() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const db = mongoose.connection.db;
    const logsCollection = db.collection('logs');

    // Clear existing logs
    await logsCollection.deleteMany({});
    console.log('Cleared existing logs');

    // Generate more logs by duplicating and varying the sample data
    const allLogs = [];
    for (let i = 0; i < 50; i++) {
      sampleLogs.forEach((log, index) => {
        const newLog = {
          ...log,
          logId: 'log-' + i + '-' + index + '-' + Math.random().toString(36).substr(2, 9),
          timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
          sessionId: log.sessionId + '-' + i,
          userId: log.userId + '-' + Math.floor(Math.random() * 100),
          metadata: {
            ...log.metadata,
            agentId: log.metadata.agentId + '-' + i
          }
        };
        allLogs.push(newLog);
      });
    }

    console.log(`Inserting ${allLogs.length} test logs...`);
    await logsCollection.insertMany(allLogs);
    console.log('Successfully inserted test logs');

    // Print statistics
    const stats = await logsCollection.aggregate([
      {
        $group: {
          _id: '$logLevel',
          count: { $sum: 1 }
        }
      }
    ]).toArray();

    console.log('Log level distribution:');
    stats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count}`);
    });

    const categoryStats = await logsCollection.aggregate([
      {
        $group: {
          _id: '$metadata.category',
          count: { $sum: 1 }
        }
      }
    ]).toArray();

    console.log('Category distribution:');
    categoryStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count}`);
    });

  } catch (error) {
    console.error('Error generating test logs:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

generateTestLogs();

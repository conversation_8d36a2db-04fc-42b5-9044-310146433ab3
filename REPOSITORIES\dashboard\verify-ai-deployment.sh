#!/bin/bash

# AI Insights Deployment Verification Script
# This script verifies that the AI insights feature has been properly deployed

echo "🚀 AI Insights Deployment Verification"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}✅ $message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $message${NC}"
    elif [ "$status" = "ERROR" ]; then
        echo -e "${RED}❌ $message${NC}"
    else
        echo -e "${BLUE}ℹ️  $message${NC}"
    fi
}

# Function to check if a file exists
check_file() {
    local file=$1
    local description=$2
    if [ -f "$file" ]; then
        print_status "OK" "$description exists: $file"
        return 0
    else
        print_status "ERROR" "$description missing: $file"
        return 1
    fi
}

# Function to check if a directory exists
check_directory() {
    local dir=$1
    local description=$2
    if [ -d "$dir" ]; then
        print_status "OK" "$description exists: $dir"
        return 0
    else
        print_status "ERROR" "$description missing: $dir"
        return 1
    fi
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    if docker-compose ps | grep -q "$service_name.*Up"; then
        print_status "OK" "$service_name service is running"
        return 0
    else
        print_status "ERROR" "$service_name service is not running"
        return 1
    fi
}

# Function to check HTTP endpoint
check_endpoint() {
    local url=$1
    local description=$2
    local timeout=${3:-10}
    
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        print_status "OK" "$description is accessible: $url"
        return 0
    else
        print_status "ERROR" "$description is not accessible: $url"
        return 1
    fi
}

# Initialize counters
total_checks=0
passed_checks=0

# Check AI Service Files
echo -e "\n${BLUE}📁 Checking AI Service Files...${NC}"
files_to_check=(
    "ai-service/package.json:AI Service package.json"
    "ai-service/requirements.txt:AI Service requirements.txt"
    "ai-service/Dockerfile:AI Service Dockerfile"
    "ai-service/src/index.js:AI Service main file"
    "ai-service/src/services/aiAnalyzer.js:AI Analyzer service"
    "ai-service/src/services/anomalyDetector.js:Anomaly Detector service"
    "ai-service/src/services/threatPredictor.js:Threat Predictor service"
    "ai-service/src/services/patternMatcher.js:Pattern Matcher service"
    "ai-service/src/utils/logger.js:AI Service logger"
)

for item in "${files_to_check[@]}"; do
    IFS=':' read -r file description <<< "$item"
    total_checks=$((total_checks + 1))
    if check_file "$file" "$description"; then
        passed_checks=$((passed_checks + 1))
    fi
done

# Check Backend Integration Files
echo -e "\n${BLUE}🔗 Checking Backend Integration Files...${NC}"
backend_files=(
    "backend/src/routes/ai.js:AI routes"
    "backend/src/models/AIInsight.js:AI Insight model"
    "backend/src/models/AIFeedback.js:AI Feedback model"
    "backend/src/models/AIConfig.js:AI Config model"
)

for item in "${backend_files[@]}"; do
    IFS=':' read -r file description <<< "$item"
    total_checks=$((total_checks + 1))
    if check_file "$file" "$description"; then
        passed_checks=$((passed_checks + 1))
    fi
done

# Check Frontend Integration Files
echo -e "\n${BLUE}🎨 Checking Frontend Integration Files...${NC}"
frontend_files=(
    "frontend/src/components/Dashboard/AIInsightsWidget.jsx:AI Insights Widget"
    "frontend/src/pages/AIInsights/AIInsights.jsx:AI Insights Page"
    "frontend/src/pages/AIInsights/components/ThreatOverviewDashboard.jsx:Threat Overview Component"
    "frontend/src/pages/AIInsights/components/AnomalyDetectionResults.jsx:Anomaly Detection Component"
    "frontend/src/pages/AIInsights/components/SecurityPatternsAnalysis.jsx:Security Patterns Component"
    "frontend/src/pages/AIInsights/components/AIConfiguration.jsx:AI Configuration Component"
)

for item in "${frontend_files[@]}"; do
    IFS=':' read -r file description <<< "$item"
    total_checks=$((total_checks + 1))
    if check_file "$file" "$description"; then
        passed_checks=$((passed_checks + 1))
    fi
done

# Check Docker Configuration
echo -e "\n${BLUE}🐳 Checking Docker Configuration...${NC}"
total_checks=$((total_checks + 1))
if grep -q "ai-insights:" docker-compose.yml; then
    print_status "OK" "AI service added to docker-compose.yml"
    passed_checks=$((passed_checks + 1))
else
    print_status "ERROR" "AI service not found in docker-compose.yml"
fi

total_checks=$((total_checks + 1))
if grep -q "ai_models:" docker-compose.yml; then
    print_status "OK" "AI volumes configured in docker-compose.yml"
    passed_checks=$((passed_checks + 1))
else
    print_status "ERROR" "AI volumes not configured in docker-compose.yml"
fi

# Check Nginx Configuration
echo -e "\n${BLUE}🌐 Checking Nginx Configuration...${NC}"
total_checks=$((total_checks + 1))
if grep -q "ai_service" nginx/nginx.conf; then
    print_status "OK" "AI service upstream configured in nginx"
    passed_checks=$((passed_checks + 1))
else
    print_status "ERROR" "AI service upstream not configured in nginx"
fi

total_checks=$((total_checks + 1))
if grep -q "/api/ai/" nginx/nginx.conf; then
    print_status "OK" "AI routes configured in nginx"
    passed_checks=$((passed_checks + 1))
else
    print_status "ERROR" "AI routes not configured in nginx"
fi

# Check if Docker Compose is available
echo -e "\n${BLUE}🔧 Checking Docker Environment...${NC}"
total_checks=$((total_checks + 1))
if command -v docker-compose &> /dev/null; then
    print_status "OK" "Docker Compose is available"
    passed_checks=$((passed_checks + 1))
else
    print_status "ERROR" "Docker Compose is not available"
fi

# Check if services are running (if Docker Compose is available)
if command -v docker-compose &> /dev/null; then
    echo -e "\n${BLUE}🏃 Checking Running Services...${NC}"
    
    services=("backend" "frontend" "mongodb" "nginx")
    for service in "${services[@]}"; do
        total_checks=$((total_checks + 1))
        if check_service "$service"; then
            passed_checks=$((passed_checks + 1))
        fi
    done
    
    # Check AI service specifically
    total_checks=$((total_checks + 1))
    if check_service "ai-insights"; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # Check endpoints if services are running
    if docker-compose ps | grep -q "Up"; then
        echo -e "\n${BLUE}🌍 Checking Service Endpoints...${NC}"
        
        endpoints=(
            "http://localhost:5002/health:AI Service Health"
            "http://localhost:5000/health:Backend Health"
            "http://localhost:8080/health:Nginx Health"
        )
        
        for item in "${endpoints[@]}"; do
            IFS=':' read -r url description <<< "$item"
            total_checks=$((total_checks + 1))
            if check_endpoint "$url" "$description" 5; then
                passed_checks=$((passed_checks + 1))
            fi
        done
    fi
fi

# Summary
echo -e "\n${BLUE}📊 Deployment Verification Summary${NC}"
echo "=================================="

percentage=$((passed_checks * 100 / total_checks))

if [ $percentage -eq 100 ]; then
    print_status "OK" "All checks passed! ($passed_checks/$total_checks)"
    echo -e "\n🎉 ${GREEN}AI Insights deployment is complete and ready!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Access the dashboard at http://localhost:8080"
    echo "2. Navigate to 'AI Insights' in the sidebar"
    echo "3. Run an analysis to see AI-powered security insights"
    echo "4. Configure AI settings as needed"
elif [ $percentage -ge 80 ]; then
    print_status "WARN" "Most checks passed ($passed_checks/$total_checks - $percentage%)"
    echo -e "\n⚠️  ${YELLOW}AI Insights deployment is mostly complete but has some issues.${NC}"
    echo "Review the failed checks above and fix any missing components."
elif [ $percentage -ge 50 ]; then
    print_status "WARN" "Some checks passed ($passed_checks/$total_checks - $percentage%)"
    echo -e "\n⚠️  ${YELLOW}AI Insights deployment is partially complete.${NC}"
    echo "Several components are missing or not configured correctly."
else
    print_status "ERROR" "Many checks failed ($passed_checks/$total_checks - $percentage%)"
    echo -e "\n❌ ${RED}AI Insights deployment has significant issues.${NC}"
    echo "Please review the implementation and fix the failed checks."
fi

echo ""
echo "For troubleshooting help, check the implementation plan:"
echo "dashboard/AI_INSIGHTS_IMPLEMENTATION_PLAN.md"

exit $((total_checks - passed_checks))
